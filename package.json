{"name": "getrix-mls-site", "version": "v6.74.0", "description": "MLS - Multi Layer System for Getrix", "license": "proprietary", "private": true, "workspaces": ["packages/react-slot-fill", "packages/webpack-plugins/babelfish-loader-wrapper", "packages/webpack-plugins/i18nPo2Json", "packages/gx-navigation", "packages/estats"], "browser": {"fs": false, "path": false, "stream": false}, "directories": {"doc": "doc"}, "scripts": {"start": "webpack watch --config webpack.dev.mjs --progress", "start-selective": "webpack watch --config webpack.selective_watch.mjs --progress", "start:typecheck": "node webpack.typecheck.mjs", "start-selective:typecheck": "node webpack.typecheck-selective.mjs", "build": "yarn build-plugins && yarn build-estats && yarn build-nav && yarn build-mls", "build-mls": "webpack --config webpack.prod.mjs", "build-mls-with-stats": "webpack --json > stats.json --config webpack.prod.mjs", "analyze-bundle": "webpack-bundle-analyzer stats.json", "build-plugins": "yarn workspaces foreach -p --include '{@getrix/babelfish-loader-wrapper,@getrix/i18n-webpack-plugin}' run build", "build-estats": "yarn workspace estats run build", "build-nav": "yarn workspace gx-navigation run build", "watch-nav": "yarn workspace gx-navigation run watch", "build-selective": "webpack --config webpack.selective_build.mjs", "link-common": "rm -rf ./node_modules/@getrix/common && ln -s /home/<USER>/projects/gestionale/getrix-common ./node_modules/@getrix/common", "unlink-common": "rm -rf ./node_modules/@getrix/common && yarn install", "lint": "eslint --ext .js,.jsx,.ts,.tsx --ignore-path .gitignore .", "codemod": "jscodeshift -t assets/codemod/transform.ts assets/js", "auto-include:files": "node ./tools/auto-include --target files", "auto-include:directories": "node ./tools/auto-include --target directories", "typecheck": "npx tsc --noEmit -p tsconfig.json --skipLib<PERSON><PERSON><PERSON>", "typecheck:incremental": "npx tsc --noEmit --project incremental-typecheck.tsconfig.json --skipLibCheck", "lint-staged:auto-include": "node ./tools/auto-include/lint-staged-auto-include.js && lint-staged", "generate:fapi-oas": "node ./generate-fapi-oas.mjs", "test": "vitest", "storybook": "NODE_ENV=development storybook dev -p 6006", "build-storybook": "NODE_ENV=development storybook build"}, "repository": {"type": "git", "url": "********************:getrix/mls-site.git"}, "author": {"name": "ImmobiliareLabs", "email": "<EMAIL>"}, "dependencies": {"@bower_components/bootstrap-sass-official": "twbs/bootstrap-sass#~3.3.0", "@bower_components/eonasdan-bootstrap-datetimepicker": "<PERSON><PERSON><PERSON><PERSON>/bootstrap-datetimepicker#^4.17.43", "@braze/web-sdk": "^5.6.1", "@date-fns/utc": "^1.2.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@gestionale-immobiliare/react-slot-fill": "^2.0.0", "@getrix/common": "7.56.0", "@getrix/photoplan": "1.26.4", "@getrix/virtual-tour": "1.26.3", "@getrix/vt-shared": "1.26.3", "@growthbook/growthbook-react": "^1.4.1", "@gx-design/accordion": "5.2.16", "@gx-design/action-list": "3.2.2", "@gx-design/addon-input": "5.2.15", "@gx-design/alert": "5.2.15", "@gx-design/avatar": "3.2.4", "@gx-design/badge": "5.3.4", "@gx-design/button": "5.3.13", "@gx-design/button-input": "5.2.18", "@gx-design/checkbox": "5.4.14", "@gx-design/clearable-input": "5.2.15", "@gx-design/core": "1.1.0", "@gx-design/dropdown": "5.2.19", "@gx-design/empty-state": "5.3.1", "@gx-design/energetic-tag": "3.2.2", "@gx-design/helper-text": "5.2.14", "@gx-design/icon": "5.6.3", "@gx-design/icon-input": "5.2.17", "@gx-design/illustration": "4.0.1", "@gx-design/illustration-set": "3.1.2", "@gx-design/input": "5.3.14", "@gx-design/label": "1.1.13", "@gx-design/layout": "1.3.2", "@gx-design/list": "5.3.13", "@gx-design/loader": "1.4.1", "@gx-design/modal": "5.3.20", "@gx-design/notification-badge": "4.2.1", "@gx-design/number-input": "5.2.18", "@gx-design/pagination-bar": "5.2.16", "@gx-design/popover": "3.2.0", "@gx-design/radio": "5.4.15", "@gx-design/select": "^5.3.18", "@gx-design/snackbar": "^5.2.16", "@gx-design/summary-item": "^3.2.0", "@gx-design/table-data": "^1.1.0", "@gx-design/tabs": "^3.2.3", "@gx-design/tag-input": "^3.1.3", "@gx-design/textarea": "^5.2.17", "@gx-design/theme": "^1.3.0", "@gx-design/toggle": "^1.1.14", "@gx-design/toolbar": "5.4.24", "@gx-design/tools": "^1.1.0", "@gx-design/tooltip": "3.1.0", "@gx-design/typography": "^1.1.0", "@gx-design/use-media-match": "^3.0.1", "@gx-design/use-on-click-outside": "^3.0.1", "@gx-design/utilities": "^1.1.4", "@immobiliare-labs/dom-classlist": "^2.2.6", "@immobiliare-labs/imgeditor": "^4.0.0", "@immobiliare-labs/maps-migrate": "^1.0.0", "@immobiliare-labs/push-state-view": "^2.2.4", "@immobiliare-labs/querystring": "^2.1.10", "@nugget/carousel": "^3.0.0", "@pepita-component/chart": "^3.0.0", "@pepita-fe/browserslist-config": "^1.2.0", "@pepita-fe/sprite-b2b": "1.17.0", "@pepita-i18n/babelfish": "^2.0.4", "@pepita/chart-utils": "^1.0.2", "@pepita/dom-ensure-visible": "^3.2.3", "@pepita/http": "^1.0.0", "@pepita/querystring": "^2.5.1", "@reduxjs/toolkit": "^1.9.0", "@sentry/browser": "^8.54.0", "@sentry/webpack-plugin": "^3.3.1", "@swc/cli": "^0.1.62", "@swc/core": "^1.3.55", "@tanstack/react-query": "^5.37.1", "@tanstack/react-query-devtools": "^5.37.1", "@tanstack/react-table": "^8.21.3", "broadcast-channel": "^4.20.2", "classnames": "^2.2.5", "copy-webpack-plugin": "^11.0.0", "core-js": "^3.6.4", "core-utils": "~4.14.6", "cross-fetch": "^4.0.0", "date-fns": "^3.6.0", "dlv": "^1.1.1", "dom-helpers": "^5.2.1", "dotenv": "^16.0.3", "downshift": "^9.0.9", "eslint": "^8.40.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "exports-loader": "^0.6", "formik": "^2.2.5", "glob": "^7.1.6", "immer": "^9.0.7", "immobiliare-flux": "^0.7.0", "imports-loader": "^0.6", "jquery-1-10-2": "^1.0.0", "leaflet": "1.0.3", "linkstate": "^1.1.1", "lodash": "^4.12.0", "microbundle": "^0.12.4", "mini-css-extract-plugin": "^2.7.2", "mixpanel-browser": "^2.41.0", "openapi-fetch": "^0.12.2", "openapi-react-query": "^0.2.0", "parsleyjs": "2.9.2", "promise-polyfill": "^8.1.3", "raw-loader": "^0.5", "rc-picker": "^2.5.19", "react": "^18.3.1", "react-calendar": "^4.2.1", "react-dom": "^18.3.1", "react-error-boundary": "^4.0.13", "react-number-format": "^5.2.2", "react-redux": "^7.1.3", "react-router-dom": "^6.18.0", "react-smooth": "^2.0.2", "react-sortable-hoc": "^1.11.0", "react-with-direction": "1.3.1", "recharts": "^2.6.2", "redux": "^4.0.5", "redux-actions": "^2.6.5", "redux-thunk": "^2.2.0", "reselect": "^4.0.0", "resize-observer-polyfill": "^1.5.1", "sass": "1.32.8", "sass-loader": "10.1.x", "swc-loader": "^0.2.3", "swig": "^1.4.2", "swiper": "^11.2.6", "twig-html-loader": "^0.1.9", "twig-loader": "^0.5.5", "url-loader": "^4.1.1", "webpack": "^5.80.0", "webpack-bundle-analyzer": "^4.8.0", "webpack-bundle-tracker": "^1.8.1", "webpack-cli": "^5.1.1", "webpack-dev-server": "^4.15.0", "webpack-manifest-plugin": "^5.0.0", "webpack-merge": "^5.8.0", "yup": "^1.2.0"}, "devDependencies": {"@mswjs/source": "^0.3.1", "@pepita-tools/vitest-gitlab-reporter": "^2.0.4", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-mdx-gfm": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/addon-styling-webpack": "^1.0.1", "@storybook/addon-webpack5-compiler-swc": "^3.0.0", "@storybook/blocks": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/react-webpack5": "^8.6.12", "@storybook/test": "^8.6.12", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.1.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.0", "@types/jscodeshift": "^0.7.0", "@types/react": "^18.3.20", "@types/react-beautiful-dnd": "^13.1.3", "@types/react-redux": "^7", "@types/recharts": "^1.8.24", "@types/resolve": "^1.14.0", "@types/webpack-bundle-analyzer": "^4.6.0", "@typescript-eslint/parser": "^6.4.0", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.0.2", "@vitest/ui": "^3.0.2", "browser-sync": "^2.27.11", "browser-sync-webpack-plugin": "^2.3.0", "chokidar": "^3.5.3", "eslint": "^8.44.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-compat": "^4.2.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.12.0", "eslint-plugin-testing-library": "^6.1.0", "eslint-plugin-vitest": "^0.3.20", "eslint-plugin-you-dont-need-lodash-underscore": "^6.12.0", "eslint-plugin-you-dont-need-momentjs": "^1.6.0", "inquirer": "^9.2.7", "jest-dom": "^4.0.0", "jscodeshift": "^0.7.0", "jsdom": "^26.0.0", "lint-staged": "^14.0.1", "msw": "^2.6.0", "openapi-typescript": "^7.4.1", "prettier": "^3.0.3", "resolve": "^1.15.1", "storybook": "^8.6.12", "tsconfig-paths-webpack-plugin": "^4.2.0", "typescript": "^5.6.2", "vite-tsconfig-paths": "^4.2.1", "vitest": "^3.0.2"}, "browserslist": ["defaults", "Chrome >= 64", "Edge >= 79", "Firefox >= 67", "Opera >= 51", "Safari >= 12", "and_chr >= 64", "and_ff >= 67", "op_mob >= 51", "ios_saf >= 12", "Samsung >= 6", "not op_mini all", "not kaios > 0", "not baidu > 0", "not and_qq >= 0"], "publishConfig": {"registry": "https://npm.pepita.io/"}, "engines": {"node": ">=12.0.0"}, "resolutions": {"@pepita/querystring": "^2.5.1", "@types/react": "^18.3.20", "react": "^18.3.1", "react-dom": "^18.3.1", "@pepita-fe/sprite-b2b": "1.17.0", "@nugget/motion": "2.0.4"}, "volta": {"node": "18.16.0", "yarn": "3.4.1"}, "msw": {"workerDirectory": ["public"]}}