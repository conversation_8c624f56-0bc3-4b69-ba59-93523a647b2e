@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

// Making sure the height is equal to page height
.emptyList--immovisita {
  min-height: 96rem;
}

.gx-date-input-wrapper {
  margin-right: space(md);

  .DayPicker__withBorder {
    border: 0.1rem solid color(background-alt);
    border-radius: radius(sm);
    box-shadow: none;
  }

  .SingleDatePicker {
    & > div {
      height: 100%;
    }

    &_picker {
      top: space(2xl) !important;
      left: -(space(2xl)) !important;
      @include z-index(base, 2);
    }

    &Input {
      height: 100%;

      &__withBorder {
        border: 0;
      }

      .DateInput {
        width: 100%;
        height: 100%;
        margin-left: 0;
        border-radius: radius(md);

        &_fang {
          display: none;
        }

        &_input {
          height: 100%;
          padding: space(sm);
          border: 0;
          color: color(content-medium);
          font-size: 1.6rem;
          line-height: inherit;

          @include media('screen', '>=#{breakpoint(sm)}') {
            font-size: 1.4rem;
          }
        }
      }
    }
  }
}

.gx-listing__actionBar {
  display: flex;
  justify-content: space-between;
  padding: space(md) space(xl);

  form {
    min-width: 40rem;
  }

  &Buttons {
    display: flex;

    .btn--rimuovi--filtri {
      min-width: 14rem;
    }
  }

  @include media('screen', '<#{breakpoint(md)}') {
    form {
      min-width: auto;
    }

    .gx-input-group {
      margin-right: 0;
    }
  }

  @include media('screen', '<=#{breakpoint(sm)}') {
    display: flex;
    flex-wrap: wrap;
    padding: space(sm) space(md);
    border-bottom: 0.1rem solid color(border-main);

    form {
      width: 100%;
    }

    .gx-input-group {
      max-width: 100%;
      flex: 1;
    }
  }
}

.gx-scheduled-visit-modal__addUserRow .gx-input-master-wrapper {
  display: flex;
}

.gx-time_picker {
  position: relative;
  flex: 1;

  @include media('screen', '>=#{breakpoint(sm)}') {
    flex: 0 0 auto;
  }

  &__pickerbox {
    display: flex;
    position: absolute;
    top: ($gx-unit-size * 5) + space(sm);
    right: 0;
    align-items: center;
    justify-content: space-between;
    min-width: 25rem;
    padding: space(lg);
    border: solid 0.1rem color(border-main);
    border-radius: radius(sm);
    background-color: #ffffff;
    @include z-index(base, 9);

    @include media('screen', '>=#{breakpoint(sm)}') {
      right: auto;
      left: 0;
    }
  }

  &__number-box {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  &__digit {
    width: 100%;
    padding: space(xl) space(md);
    font-weight: 700;
    text-align: center;
  }
}

.gx-scheduled-visit-modal__property {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-bottom: 2rem;
  border-bottom: 0.1rem solid color(border-main);

  .gx-loader-overlay {
    left: 50%;
  }

  &Error {
    text-align: center;
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    padding-bottom: 3rem;
  }
}

.gx-scheduled-visit-modal__ref {
  display: flex;
  align-items: center;
  margin-right: space(md);

  .list__field__thumb {
    flex-shrink: 0;
  }

  &Info {
    margin-left: space(sm);
    color: color(content-medium);
    font-size: 1.4rem;
  }
}

.gx-scheduled-visit-modal__body {
  padding-top: 3rem;

  .gx-input-wrapper + .gx-input-wrapper {
    margin-top: space(xl);
  }
}

.gx-scheduled-visit-modal__date {
  display: block;
  flex: 2;

  @include media('screen', '>=#{breakpoint(sm)}') {
    flex: 0 0 auto;
  }
}

.gx-scheduled-visit-modal__time {
  display: flex;
}

.gx-scheduled-visit-modal__addUser {
  margin-top: space(md);
}

.gx-scheduled-visit-modal__agentSelect {
  width: 100%;

  @include media('screen', '>=#{breakpoint(sm)}') {
    width: 50%;
  }
}

.gx-scheduled-visit-modal__addUserRow {
  .gx-input-addon-wrapper {
    width: 100%;
  }

  .gx-button {
    margin-left: space(md);
  }

  & + & {
    margin-top: space(md);
  }
}

// TODO: change prefix name

.iv-changeRealEstate {
  &__search {
    display: flex;
    justify-content: space-between;
    gap: space(md);
  }

  &__searchInput {
    flex-grow: 1;
  }

  &__moreFilters {
    display: flex;
    justify-content: flex-end;
    margin-left: space(md);
  }

  &__filter {
    border-right: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  &__list {
    padding-top: space(lg);
    position: relative;

    &--noresults {
      margin-top: space(md);
      text-align: center;
    }

    h3 {
      margin-top: 0;
      margin-bottom: space(md);
      font-size: 1.4rem;
    }
  }

  &__error {
    padding: space(xl) 0 space(sm);
    text-align: center;
  }

  &__content {
    max-height: 50rem;
    overflow-y: auto;
  }

  &__footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: space(xs) space(md);
    box-shadow: 0 0 1.6rem rgb(0 0 0 / 20%);
    @include z-index(base, 1);
  }

  &__pagination {
    padding-top: space(md);
  }
}

.iv-changeRealEstateFilters {
  padding-bottom: space(sm);
  border-bottom: 0.1rem solid color(border-main);

  &__row {
    display: flex;
    margin: space(md) 0;
    margin-right: -(space(sm));
    margin-left: -(space(sm));

    &:first-of-type {
      flex-direction: column;

      & > div + div {
        margin-top: space(md);
      }

      @include media('screen', '>=#{emConverter(550)}') {
        flex-direction: row;

        & > div + div {
          margin-top: 0;
        }
      }
    }

    & > div {
      width: 100%;
      padding: 0 space(sm);

      & > label {
        display: block;
        margin-bottom: space(sm);
        font-size: 1.4rem;
        font-weight: 600;
      }
    }
  }

  &__buttons {
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;

    button {
      margin-right: space(sm);

      &:last-of-type {
        margin: 0;
      }
    }
  }
}

.iv-select-label {
  display: flex;
  align-items: center;
}

.iv-realEstateHorizontalCard {
  display: flex;
  position: relative;
  width: 100%;
  margin-bottom: space(md);
  padding: space(md);
  border: 0.1rem solid color(border-main);
  border-radius: radius(sm);
  cursor: pointer;

  &--selected,
  &:hover {
    border-color: color(border-selected);
    background-color: color(background-selected);
  }

  &__image {
    flex-shrink: 0;
    width: 8rem;
    height: 8rem;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center center;
    }

    &--placeholder {
      height: 6rem;
    }
  }

  &__content {
    margin-left: space(md);
    padding-top: 0;

    strong {
      color: color(content-action);
    }

    p {
      margin: 0;
      line-height: 2.2rem;
    }
  }

  &__contentBottom {
    display: flex;
    width: auto;
    margin-top: space(md);

    @include media('screen', '>=#{emConverter(550)}') {
      position: absolute;
      right: space(md);
      bottom: space(md);
      margin: 0;
    }
  }

  &__icon {
    display: flex;
    align-items: center;
    margin-left: space(lg);
    color: color(content-action);
    font-size: 2.4rem;

    span {
      margin-left: space(xs);
      font-size: 1.4rem;
    }

    &:first-of-type {
      margin-left: 0;
    }
  }

  &:last-of-type {
    margin-bottom: 0;
  }
}

.gx-immovisitaRow__property {
  display: flex;
  align-items: center;

  .list__field__thumb {
    margin-right: space(md);
  }
}

.gx-scheduled-visit__links {
  display: flex;
  padding: space(md) 0;

  .gx-link-wrapper {
    margin-right: space(md);
    padding: space(sm) space(md);
    border: 0.1rem solid color(border-main);
    background-color: color(background-alt);
    word-break: break-word;
  }

  .gx-button {
    flex-shrink: 0;
  }
}

.gx-scheduled-visit-links-modal {
  .modal-body {
    padding: space(sm) 3rem;
  }
}

.list__field--immovisita {
  padding-right: 3rem;
}

.ivsch-modal {
  &__request {
    width: calc(100% + 6rem);
    margin: -3rem -3rem 0;
    padding: 3rem;
    background-color: color(background-alt);
  }
}

@include media('screen', '>=#{breakpoint(sm)}') {
  .ivsch-modal {
    .modal-body {
      max-height: 60vh;
    }
  }
}
