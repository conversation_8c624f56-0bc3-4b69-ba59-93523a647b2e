@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

// Performance

#performance-content {
  .gx-loader-overlay {
    &--inline {
      height: calc(100% - 5.6rem);
      margin-top: 0;
    }
  }
}

.performance-detail {
  > h2 {
    margin-bottom: space(md);
  }
}

.performance-row {
  display: flex;
  flex-direction: column;
  margin-top: space(xl);
  margin-right: -(space(sm));
  margin-left: -(space(sm));
  gap: space(lg);

  & + & {
    margin-top: space(xl);
  }

  @include media('>=#{breakpoint(sm)}') {
    margin-right: 0;
    margin-left: 0;
  }

  @include media('>=#{breakpoint(md)}') {
    .gx-card + .gx-card {
      margin-top: 0;
    }
  }

  @include media('>=#{breakpoint(lg)}') {
    flex-direction: row;
  }

  @include media('>=#{breakpoint(lg)}', '<#{breakpoint(xl)}') {
    .crm-section:has(.crm-sidebar--left.is-open) & {
      flex-direction: column;

      .performance-graphs__events {
        width: 100%;
      }
    }
  }

  small {
    color: color(content-medium);
  }

  &__title {
    color: color(content-high);
  }

  &__balloon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: space(2xl);
    height: space(2xl);
    margin-bottom: space(sm);
    border-radius: radius(rounded);
    background-color: color(background-brand);
    color: color(background-brand-alt);
    font-size: 1.6rem;
  }

  &__feedback {
    margin-top: space(sm);
  }

  p {
    margin-top: space(sm);
    margin-bottom: space(lg);
    color: color(content-medium);
  }

  &__graphEvent {
    height: 40rem;
    margin-right: space(sm);
    margin-left: space(sm);
    padding-top: space(lg);

    @include media('>=#{breakpoint(sm)}') {
      margin-right: 0;
      margin-left: 0;
    }
  }

  &__graphContacts {
    margin-top: auto;
    padding: space(2xl) 0 space(3xl);
  }
}

.performanceGradientArea1 {
  stop-color: color(background-brand);
}

.performanceGradientArea2 {
  stop-color: color(background-main);
}

.performanceActiveDot {
  fill: color(background-main);
  stroke: color(background-brand);
}

.recharts-area-curve {
  stroke: color(background-brand);
}

.eventsGraph {
  position: relative;
  margin-top: auto;
  width: 32.3rem;

  .recharts-responsive-container {
    height: 30rem;
  }

  &__total {
    position: absolute;
    top: -8rem;
    margin-top: space(lg);
    margin-bottom: space(2xl);
    font-size: 3.2rem;
    font-weight: 600;
  }

  .recharts-default-legend {
    @include typography(body-tiny);

    .recharts-legend-item {
      display: inline-flex !important;
      align-items: center;
    }

    .recharts-surface {
      width: 1rem;
      height: 1rem;
    }
  }

  &__popover {
    @include typography(body-small);
    border-radius: radius(sm);
    background-color: color(background-main);
    box-shadow: elevation(raised);

    &Data {
      padding: space(sm);
      border-bottom: 0.1rem solid color(border-main);
      color: color(content-medium);
      text-transform: capitalize;

      .legend-dot {
        display: inline-block;
        width: 1rem;
        height: 1rem;
        margin-right: space(sm);
        border-radius: radius(rounded);
      }
    }

    &Content {
      padding: space(sm);

      > div + div {
        margin-top: space(sm);
      }

      .gx-badge {
        margin-right: space(xs);
      }
    }
  }
}

.contactsGraph {
  margin: space(md) auto 0;

  ul {
    @include typography(body-tiny);
  }

  li {
    margin-bottom: space(xs);
  }

  .recharts-default-legend {
    .recharts-surface {
      width: 1rem !important;
    }
  }

  .recharts-sector,
  .recharts-tooltip-wrapper {
    outline: none;
  }

  .recharts-legend-item {
    display: inline-flex !important;
    align-items: center;
  }

  .recharts-wrapper .recharts-surface {
    width: 100%;
  }
}

.recharts-pie-sector {
  outline: none;
}

.sectorWrap {
  .sector {
    transition: opacity 0.3s ease-in-out;
  }

  .sector-highlight {
    transform: scale(0.75);
    transform-origin: center center;
    transition: all 0 ease-in-out;
    opacity: 0;
  }

  &:hover {
    .sector {
      opacity: 0.8;
    }

    .sector-highlight {
      transform: scale(1);
      opacity: 0.25;
    }
  }
}

.performance-graphs {
  margin-top: 0;

  &__head {
    display: flex;
    flex-wrap: wrap;
    align-items: baseline;
    margin-bottom: space(md);

    & > div {
      margin-right: space(xs);
    }

    &Column {
      flex-direction: column;
    }
  }

  .gx-card:only-child {
    flex-basis: auto;
  }

  @include media('>=#{breakpoint(md)}') {
    &__contacts {
      flex-basis: 37rem;
      flex-grow: 0;
      flex-shrink: 0;

      .gx-card__content {
        display: flex;
        flex-direction: column;

        .emptyChart {
          margin-top: auto;
        }
      }

      .eventsGraph {
        margin-top: space(lg);
      }
    }
  }

  @include media('>=#{breakpoint(lg)}') {
    &__events {
      width: calc(100% - 39.4rem);
    }
  }
}

.emptyChart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 36rem;
  margin-top: auto;

  svg {
    margin-bottom: space(xs);
    color: color(background-brand);
    @include icon-size(md);
  }
}

.referent-card {
  display: flex;

  &__name {
    font-size: 1.4rem;
    font-weight: bold;
  }

  &__contact {
    svg {
      color: color(content-medium);
      @include icon-size(md);
    }
  }

  &__content {
    margin-left: space(md);

    div + div {
      margin-top: space(sm);
    }

    .gx-icon {
      margin-right: space(xs);
    }
  }
}

.data-range-performance {
  position: relative;
  display: inline-flex;
  margin: space(md) 0;

  .DayPicker {
    position: absolute;
    top: calc(100% + #{space(xs)});
    right: 0;
    border: 0.1rem solid color(border-main);
    overflow: hidden;
    @include z-index(dropdown);
  }

  .gx-button {
    @include typography(body-small);
    text-transform: none;

    &--primary {
      border: 0.1rem solid color(border-selected);
      background-color: color(background-selected);
      color: color(content-selected);
    }
  }
}

.graphLoading {
  display: flex;
  align-items: center;
  align-self: flex-end;
  justify-content: center;
  width: 100%;
  height: 40rem;
  margin-top: auto;
  color: color(background-brand);
  font-size: 3.2rem;
}

// Increase performance modal

.increases-performance-box {
  &__head {
    display: flex;
    flex-direction: column;
    margin-bottom: space(lg);

    &Center {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    &Title {
      display: flex;
      align-items: center;
      margin-bottom: space(sm);

      svg {
        @include icon-size(sm);
      }
    }

    &Position {
      display: flex;
      align-items: center;
    }
  }

  &__title {
    display: flex;
    margin-bottom: space(sm);

    h6 {
      margin: 0;
    }

    span {
      display: inline-flex;
      justify-content: center;
      margin-right: space(sm);
      color: color(content-medium);

      svg {
        @include icon-size(sm);
      }
    }

    h6 {
      font-weight: 400;
    }
  }

  &__position {
    display: flex;
    align-items: center;
    margin-bottom: space(sm);
    color: color(content-medium);

    svg {
      @include icon-size(sm);
    }

    > span {
      margin: 0 space(xs);
      color: color(content-high);
      font-size: 2.4rem;
    }

    .gx-badge,
    > .gx-icon {
      margin-left: space(sm);
    }
  }

  &__trend {
    display: block;
    margin-top: space(lg);
    @include typography(button);

    svg {
      @include icon-size(sm);
      margin-right: space(sm);
    }
  }

  &__details {
    .gx-overline {
      color: color(content-medium);
    }

    .gx-card + .gx-card {
      margin-top: space(md);
    }

    .gx-card {
      &__content {
        display: flex;
        flex-direction: column;
        height: 100%;
      }

      .performance-icon-status {
        font-size: ($gx-unit-size * 4);
        margin-right: space(sm);
      }

      .performance-position {
        display: flex;
        align-items: center;
        margin-top: auto;

        > div {
          font-size: 1.6rem;
          font-weight: 400;

          span {
            font-weight: 700;
          }
        }

        svg {
          @include icon-size(xs);
        }
      }

      &.performance-high {
        background-color: color(background-success);

        .performance-icon-status {
          color: color(content-success);
        }
      }

      &.performance-medium {
        background-color: color(background-warning);

        .performance-icon-status {
          color: color(content-warning);
        }
      }

      &.performance-low {
        background-color: color(background-error);

        .performance-icon-status {
          color: color(content-error);
        }
      }
    }

    @include media('>=#{breakpoint(md)}') {
      display: flex;
      gap: space(md);

      .gx-card + .gx-card {
        margin-top: 0;
      }
    }

    .gx-card {
      background-color: color(background-alt);
      border-color: color(background-alt);
    }
  }

  &__actions {
    display: flex;
    flex-direction: column;
    gap: space(lg);

    .gx-card + .gx-card {
      margin-top: 0;
    }

    &Title {
      color: color(content-medium);
    }

    @include media('screen', '>=#{breakpoint(md)}') {
      flex-direction: row;
      flex-wrap: wrap;
      gap: 0;

      h1 {
        width: 100%;
        margin-bottom: space(md);
      }

      .gx-card + .gx-card {
        margin-top: space(md);
      }
    }
  }

  &__action {
    &Title {
      display: flex;
      align-items: center;
      height: ($gx-unit-size * 3);
      margin-bottom: space(sm);

      * + * {
        margin-left: space(xs);
      }

      svg {
        @include icon-size(xs);
      }
    }

    @include media('>=#{breakpoint(md)}') {
      margin-right: space(xl);
      padding-right: space(xl);
      border-right: 0.1rem solid color(border-main);

      &:last-child {
        margin-right: 0;
        padding-right: 0;
        border-right: none;
      }
    }

    &Percentage {
      font-weight: bold;
    }
  }

  &__viewDetail {
    margin: space(xl) 0;
    text-align: center;
    text-transform: uppercase;

    a {
      display: inline-flex;
      align-items: center;
    }

    svg {
      margin-left: space(sm);
      @include icon-size(md);
    }
  }

  &__note {
    margin-bottom: 0;
    @include typography(body-small);
    color: color(content-medium);
  }

  &__skeleton {
    margin-top: space(md);

    > * + * {
      margin-top: space(md);
    }
  }

  &__skeletonAction {
    display: flex;
    flex-wrap: wrap;

    > * + * {
      width: 100%;
      margin-top: space(md);
    }

    @include media('>=#{breakpoint(md)}') {
      > * + * {
        width: auto;
        margin-top: 0;
        margin-left: space(3xl);
      }
    }
  }

  &__valueEmpty {
    height: 4.2rem;
    margin-top: ($gx-unit-size * 2);
  }
}

.suggestionsCard {
  margin-top: space(xl);
  margin-bottom: 7.2rem;
}

.statsCard {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;

  &__skeleton {
    display: flex;
    flex-direction: column;
    gap: space(sm);

    &Cards {
      display: flex;
      flex-wrap: wrap;
      gap: 1.2rem;
      margin-top: space(md);

      > * {
        width: calc(50% - 1.2rem);
      }
    }
  }
}

.stats-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 1.2rem;
  margin-top: auto;
}

.stats-card {
  flex-grow: 1;
  width: calc(50% - 1.2rem);
  padding: space(md);
  background-color: color(background-alt);
  border-radius: radius(md);

  &__head {
    display: flex;
    align-items: center;
    gap: 0 space(xs);
    margin-bottom: space(sm);

    svg {
      @include icon-size(sm);
    }
  }
}

.kpi-action-card {
  &__title {
    display: flex;
    align-items: center;
    height: ($gx-unit-size * 3);
    margin-bottom: space(xs);

    > * + * {
      margin-left: space(sm);
    }

    svg {
      margin-left: space(xs);
      @include icon-size(xs);
    }
  }

  &__content {
    @include typography(body-small);
    margin-bottom: space(md);
  }

  &__empty {
    width: 100%;
    text-align: center;

    svg {
      margin-bottom: space(sm);
      @include icon-size(md);
    }
  }

  @include media('screen', '>=#{breakpoint(md)}') {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &__content {
      margin-bottom: 0;
    }
  }
}

.skeleton-alert {
  margin-bottom: space(md);

  .gx-modal__body & {
    margin-top: space(md);
    margin-bottom: 0;
  }
}

.general-trend-cards {
  display: flex;
  flex-direction: column;
  gap: space(lg);

  @include media('>=#{breakpoint(lg)}') {
    flex-direction: row;

    .gx-card:only-child {
      .stats-card {
        width: calc(25% - 1.2rem);
      }
    }
  }

  @include media('>=#{breakpoint(lg)}', '<#{breakpoint(xl)}') {
    .crm-section:has(.crm-sidebar--left.is-open) & {
      flex-direction: column;
    }
  }
}

.general-trend-card-action {
  display: flex;
  justify-content: center;
  margin-top: space(xl);
}

//TO-DO valutare se modificare il componente card
.performance-detail {
  > * {
    max-width: 128rem;
    margin-left: auto;
    margin-right: auto;
  }

  .competitors-title {
    margin-top: space(xl);

    &Results {
      margin-top: space(md);

      span {
        font-weight: 400;
      }
    }
  }

  .increases-performance-box {
    &__headTitle {
      gap: 0 space(sm);

      svg {
        @include icon-size(md);
      }

      .gx-icon--info {
        @include icon-size(xs);
      }
    }

    &__headSubTitle {
      display: flex;
      align-items: center;
      font-weight: 400;
      gap: 0 0.8rem;

      svg {
        font-size: ($gx-unit-size * 4);
      }

      span {
        font-weight: 700;
      }

      &.performance-high svg {
        color: color(content-success);
      }

      &.performance-medium svg {
        color: color(content-warning);
      }

      &.performance-low svg {
        color: color(content-error);
      }
    }
  }

  .gx-card--rounded {
    // ?? box-shadow: elevation(overlay);
    border: none;
    border-radius: radius(lg);
    box-shadow:
      0 0.4rem 0.4rem rgba(6, 14, 18, 0.03),
      0 0.8rem 2.4rem rgba(6, 14, 18, 0.05);

    > .gx-card__content {
      padding: space(lg);
    }
  }
}

.section-property-performance {
  .gx-head-section {
    background-color: color(background-main);
  }

  & + .gtx-ask-help {
    & + .gtx-ah-footer-spacing {
      background: color(background-alt);
    }
  }
}

.properties-stats-table .gx-table-new__row:hover .gx-table-new__actions::before {
  background: transparent;
}

.table-card-row {
  table {
    border-spacing: 0 space(sm);
  }

  tr,
  td {
    height: 100%; //fix FF
  }

  th:last-child {
    border-radius: 0 radius(md) radius(md) 0;
  }

  td,
  .gx-table-new__cell--main > div,
  .gx-table-new__cell--actions > div {
    transition: 0.2s background-color ease-in-out;
  }

  tbody tr {
    td.gx-table-new__cell {
      background-color: color(background-main);
    }

    .gx-table-new__cell--main {
      height: 0;
      padding: 0;

      > div {
        height: 100%;
        padding-left: space(md);
        border-radius: radius(lg) 0 0 radius(lg);
        justify-content: flex-start;
      }
    }

    td:last-child {
      background-color: color(background-main);
    }

    .gx-table-new__cell--actions {
      height: 0;
      padding: 0;

      > div {
        width: 100%;
        height: 100%;
        border-radius: 0 radius(lg) radius(lg) 0;
        justify-content: center;
      }
    }

    .gx-table-new__cell:last-child:not(.gx-table-new__cell--actions) {
      height: 0;
      padding: 0;

      > div {
        width: auto;
        border-radius: 0 radius(lg) radius(lg) 0;
      }
    }
  }

  .property-media-cell {
    .gx-badge {
      background-color: color(background-main);
    }
  }

  .gx-table-new__head {
    th:first-child.gx-table-new__cell--main,
    th:last-child.last-col {
      position: sticky;
      padding: 0;
      background-color: color(background-main);

      > div {
        width: 100%;
        height: 100%;
        min-height: ($gx-unit-size * 4); //fix FF
        background-color: transparent;
      }
    }

    &First {
      border-radius: radius(md) 0 0 radius(md);
      background-color: color(background-alt);
    }

    .gx-table-new__headLast {
      border-radius: 0 radius(md) radius(md) 0;
    }
  }

  .your-property-sticky .gx-table-new__cell,
  .gx-table-new__cell {
    border-bottom: none !important;
  }

  .gx-table-new__cell--actions {
    min-width: 5.6rem;
  }

  .gx-table-new__actionsWrap {
    height: 100%;
  }

  .gx-table-new__actions {
    right: 0.8rem;
  }

  tbody tr:nth-child(2n) {
    td {
      background: color(background-alt);
    }

    .gx-table-new__cell--main {
      background: color(background-main);

      > div {
        background: color(background-alt);
      }
    }

    .gx-table-new__cell--actions {
      background: color(background-main);

      &::before {
        background-color: transparent;
      }

      .gx-table-new__actions {
        background: transparent;
      }

      > div {
        width: 100%;
        background: color(background-alt);
      }
    }
  }

  tbody tr:hover {
    td {
      background: color(background-selected);
    }

    .gx-table-new__cell--main {
      background: color(background-main);

      > div {
        background: color(background-selected);
      }
    }

    .gx-table-new__cell--actions {
      background: color(background-main);

      &::before {
        background-color: transparent;
      }

      .gx-table-new__actions {
        background: transparent;
      }

      > div {
        width: 100%;
        background: color(background-selected);
      }
    }
  }
}

.properties-stats-table {
  td {
    height: 10.4rem;
  }

  td:last-child {
    border-radius: 0 radius(lg) radius(lg) 0;

    > div {
      margin-left: space(md);
    }
  }
}

.propertiesListCard__tabs {
  margin-bottom: space(xl);

  .gx-tabs__item .gx-icon {
    @include icon-size(md);
  }

  span {
    @include typography(title-2);
  }
}

//PRINT DETAIL
.performance-detail-page {
  padding: space(lg) 0;

  .general-trend-cards {
    margin-bottom: space(md);
    gap: space(md);
  }

  .increases-performance-box {
    &__title {
      h6 {
        margin: 0;
      }
    }

    &__headTitle {
      gap: 0 space(sm);

      svg {
        @include icon-size(md);
      }

      .gx-icon--info {
        @include icon-size(xs);
      }
    }

    &__headSubTitle {
      display: flex;
      align-items: center;
      font-weight: 400;

      svg {
        font-size: ($gx-unit-size * 4);
        margin-right: space(sm);
      }

      span {
        font-weight: 700;
      }

      &.performance-high svg {
        color: color(content-success);
      }

      &.performance-medium svg {
        color: color(content-warning);
      }

      &.performance-low svg {
        color: color(content-error);
      }
    }

    &__trend {
      display: none;
    }
  }

  &__propertyId {
    display: flex;
    align-items: center;
    margin-bottom: 24pt;

    &Img {
      width: 5.8rem;
      height: 4rem;
      margin-right: 16pt;
      border-radius: radius(md);
      overflow: hidden;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    span {
      font-size: 10pt;
    }
  }

  &__content {
    margin: 0 1cm;
  }

  .gx-alert--info {
    background-color: color(background-info) !important;
  }

  .performance-tag-positive {
    border-color: color(content-success) !important;
    background-color: color(content-success) !important;
  }

  .performance-tag-warning {
    border-color: #ffb801 !important;
    background-color: #ffb801 !important;
  }

  .performance-tag-negative {
    border-color: color(content-error) !important;
    background-color: color(content-error) !important;
  }

  .performance-tag-positive,
  .performance-tag-warning,
  .performance-tag-negative {
    span {
      color: color(content-accent) !important;
    }

    svg {
      fill: color(content-accent) !important;
    }
  }

  .increases-performance-box {
    &__title {
      span {
        display: none;
      }
    }

    &__details {
      display: flex;
      gap: space(md);
      margin-bottom: 0;

      .gx-card + .gx-card {
        margin-top: 0;
      }
    }

    &__actions {
      display: none;
    }
  }

  .performance-graphs {
    &__events,
    &__contacts {
      width: 100% !important;

      .gx-tabs {
        display: none;
      }
    }
  }

  .gx-card {
    border: 0.1rem solid color(border-main);
    border-radius: radius(sm);
    page-break-inside: avoid;

    &__content {
      font-size: 10pt;
    }
  }

  .recharts-legend-wrapper {
    font-size: 8pt !important;
  }

  .gx-overline {
    font-size: 8pt;
  }

  .gx-table-new {
    .performance-badge,
    .gx-badge {
      height: 1.6rem;
      font-size: 1.2rem;
    }

    &__head {
      th {
        font-size: 1.2rem;
      }
    }

    &Wrap {
      overflow: hidden;

      & + .gx-title-2 {
        margin-top: space(md);
      }
    }

    &__body {
      font-size: 1.2rem;
    }

    &__row:last-child td {
      border-bottom: none;
    }

    &__cell {
      min-width: 0;
      padding: space(sm);

      .gx-property-item__pic {
        width: 6rem;
        height: 4.5rem;
      }
    }
  }

  .property-media-cell .gx-badge {
    height: 1.6rem;
    font-size: 1rem;
  }

  .gx-table-new__head th:first-child > div,
  .gx-table-new__head th:last-child > div {
    background-color: color(background-alt);
  }

  .position-column {
    width: 4.8rem;
  }

  .reference-column {
    width: 9.6rem;
  }

  .price-column {
    width: 8rem;
  }

  .photos-column {
    width: 6rem;
  }

  .media-column {
    width: 14rem;
  }

  .visibility-column {
    width: 12rem;
  }

  .quality-column {
    width: 8rem;
  }

  .gx-table-new__actions svg {
    @include icon-size(sm);
  }

  .stats-card {
    width: calc(25% - 1.2rem);
  }

  .general-trend-card-action {
    display: none;
  }
}

// TO-DO: capire come andrà componentizzato
.button-withNotify {
  position: relative;

  &::before {
    content: '';
    display: block;
    position: absolute;
    top: 0.2rem;
    right: 0.2rem;
    width: 1.2rem;
    height: 1.2rem;
    border-radius: radius(rounded);
    background-color: color(background-notification);
    border: 0.1rem solid color(border-reversed);
  }
}

.performance-text-maxWidth {
  max-width: 20rem;
}

// Similar Properties
.card-similar-properties {
  margin-top: space(md) !important;

  h2 {
    margin-bottom: space(lg);
    margin-right: space(sm);
  }

  h4 {
    margin-top: 0;
    margin-bottom: space(md);
  }

  .gx-table-new__actions {
    .gx-button {
      background-color: color(background-brand);
      color: color(content-reversed);
    }
  }
}

.your-property-sticky {
  .gx-table-new__actions {
    .gx-button {
      background-color: color(background-brand);
      color: color(content-reversed);
    }
  }
}

.your-property-table {
  margin-top: space(md);

  .gx-table-newWrap {
    //Hidden browser scrollbar
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .gx-table-new__cell {
    border-bottom: none;
    cursor: default;
  }
}

.property-position {
  width: ($gx-unit-size * 5);
  margin-right: space(md);
}

.property-media-cell {
  display: flex;
  flex-wrap: wrap;

  .gx-badge {
    margin: 0 space(xs) space(xs) 0;
  }
}

.your-property-sticky {
  position: fixed;
  top: $gx-new-menu-header-height;
  right: 0;
  width: 100%;
  margin-left: -1.6rem;
  max-width: 100%;
  padding: space(md) 4rem 0;
  transition: none;
  border-bottom: 0.1rem solid color(border-main);
  background-color: color(background-main);
  box-shadow: elevation(fixed-top);
  // high z-index because loader was covering it
  @include z-index(loader-overlay, 1);

  @include media('screen', '>=#{breakpoint(md)}') {
    padding-left: 5.6rem;
    padding-right: 5.6rem;
    margin-left: -(space(xl));
  }

  > div {
    transition: 0.3s all ease-in-out;
  }

  @include media('screen', '>=#{breakpoint(md)}') {
    width: calc(100% - #{$gx-new-menu-width});

    .crm-section:has(.crm-sidebar--left.is-open) & {
      width: calc(100% - #{$gx-new-menu-width} - 28rem);
    }
  }

  @include media('screen', '>=#{breakpoint(lg)}') {
    .has-menu-fixed & {
      width: calc(100% - #{$gx-new-menu-width} - 26rem);
    }
  }

  .gx-table-new__cell {
    border-bottom: none;
    cursor: default;
  }

  .gx-table-newWrap {
    max-width: calc(128rem - #{space(lg) * 2});
    margin: 0 auto;
    //Hidden browser scrollbar
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */

    &::-webkit-scrollbar {
      display: none;
    }
  }
}

$performance-colors: (
  high: (
    color: #0d4e27,
    background: #f2f8f3,
    border: #75c18a,
    icon-color: success,
  ),
  medium: (
    color: #725b00,
    background: #fff6e4,
    border: #e4b600,
    icon-color: warning,
  ),
  low: (
    color: #610f1e,
    background: #fbf2f3,
    border: #e07681,
    icon-color: error,
  ),
);

.performance-badge {
  display: inline-flex;
  align-items: center;
  flex-shrink: 0;
  height: ($gx-unit-size * 3);
  padding: 0 space(xs);
  border-width: 0.1rem;
  border-style: solid;
  border-radius: radius(sm);
  @include typography(body-small);

  * + * {
    margin-left: space(xs);
  }

  svg {
    @include icon-size(xs);
  }

  &--large {
    height: ($gx-unit-size * 4);
    padding: 0 space(sm);
  }

  @each $performance, $colors in $performance-colors {
    $color: map-get($colors, color);
    $background: map-get($colors, background);
    $border: map-get($colors, border);
    $iconColor: map-get($colors, icon-color);

    &--#{$performance} {
      border-color: $border;
      background-color: $background;
      color: $color;

      svg {
        color: color(content-#{$iconColor});
      }
    }
  }

  &__highlight {
    font-size: 1.4rem;

    &--large {
      font-size: 1.8rem;
    }
  }

  &--modal {
    margin-top: space(sm);
    margin-bottom: space(md);
  }
}

.performance-quality-cell {
  min-width: 14rem;
}

.similar-ads-media-width {
  min-width: 250px;
  width: 250px;
}

.button__no-padding {
  padding: 0;
}

.similar-ads-title {
  display: flex;
  align-items: baseline;
  margin-top: space(2xl);
  margin-bottom: space(xl);

  h2 {
    margin-right: space(sm);
  }

  svg {
    margin-left: space(xs);
  }
}

.title-card-outside {
  margin-top: 0;
  margin-bottom: 0;
}

.table-with-pagination {
  position: relative;

  .gx-table-new__cell {
    cursor: default;
  }

  & + .gx-paginationBar {
    padding-left: 0;
    padding-right: 0;
    margin-bottom: space(2xl);
    border-top: none;
  }
}

.price-column {
  min-width: 12.4rem;
}

.quality-column-last {
  min-width: 20rem;
}

.reference-cell {
  display: flex;
  align-items: center;
}

// Range Input
// TODO: spostare su common
.filter-bar {
  display: flex;
  gap: space(md);

  &__actions {
    display: flex;
    flex-wrap: wrap;
    gap: space(sm);
  }

  &__quick-filter {
    .gx-checkbox {
      background-color: color(background-selected);
      border-color: color(border-selected);

      &__icon {
        color: color(content-selected);
      }

      &:hover {
        background-color: color(background-selected);
        border-color: color(border-selected);
      }
    }
  }

  &__text {
    display: inline-flex;
    align-items: center;
    height: ($gx-unit-size * 5);
    color: color(content-medium);
    @include typography(body-small);
  }

  .gx-checkbox--checked {
    .gx-checkbox__icon {
      color: color(content-selected);
    }
  }
}

//TODO: to move
.gx-range-input {
  min-width: 30rem;
  max-width: 32rem;

  &Control {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: ($gx-unit-size * 5);
    padding: 0 space(sm) 0 space(md);
    border: 0.1rem solid color(border-main);
    border-radius: radius(sm);
    background-color: color(background-main);
    color: color(content-selectable);
    cursor: pointer;

    &.is-open {
      .gx-range-inputControl__caret {
        transform: rotate(180deg);
      }
    }

    &.is-selected {
      background-color: color(background-selected);
      color: color(content-selected);
      border-color: color(border-selected);
    }

    svg {
      flex-shrink: 0;
      @include icon-size(md);
      color: currentColor;
    }

    &__caret {
      transition: transform 0.3s ease-in-out;
    }

    * + * {
      margin-left: space(sm);
    }
  }

  &__inputContainer {
    display: flex;
    padding: space(sm) space(md);
    gap: space(md);
    border-bottom: 0.1rem solid color(border-main);

    &:has(+ .react-calendar-range-wrapper) {
      border-bottom: none;
    }
  }

  &__list {
    padding: 0;
    overflow-y: auto;
    overscroll-behavior-y: contain;
    max-height: 21.6rem;
    background-attachment: local, local, scroll, scroll;
    background-image: linear-gradient(color(background-main), color(background-main)),
      linear-gradient(color(background-main), color(background-main)), linear-gradient(rgba(0, 0, 0, 0.1), transparent),
      linear-gradient(0deg, rgba(0, 0, 0, 0.1), transparent);
    background-repeat: no-repeat;
    background-position:
      0 0,
      0 100%,
      0 0,
      0 100%;
    background-size: 100% space(sm);
    transition: max-height 0.3s ease;
    background-color: color(background-main);

    &--right {
      text-align: right;
    }

    &Item {
      padding: space(sm) space(lg);
      cursor: pointer;

      &:hover {
        background-color: color(background-alt);
        color: color(content-selectable);
      }

      &.is-selected {
        background-color: color(background-selected);
        color: color(content-selected);
      }
    }
  }

  &__footer {
    padding: space(md);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 0.1rem solid color(border-main);

    a {
      cursor: pointer;
      color: color(content-action);
      @include typography(body-small);
    }
  }
}

// Explore CTA promotion
// test cta performance
.explore-cta-promotion {
  background: #e50013;
  color: #fff;
  border-color: #e50013;

  &:hover,
  &:focus {
    color: #fff;
  }
}

.variation-index {
  display: inline-flex;
  align-items: center;
  margin-left: space(sm);

  svg {
    flex-shrink: 0;
    @include icon-size(xs);
  }
}

.variation-helper {
  display: flex;
  align-items: center;
  height: 4.8rem;
  margin-bottom: space(xl);
  padding: 0 space(lg);
  border: 0.1rem solid color(border-main);
  border-radius: radius(rounded);

  svg {
    flex-shrink: 0;
    @include icon-size(md);
  }

  a {
    font-weight: bold;
    text-decoration: underline;
  }

  span {
    margin-left: space(sm);
    margin-right: space(md);
  }

  &--success {
    background-color: color(background-success);
    border-color: color(border-success);

    svg {
      color: color(content-success);
    }
  }

  &--error {
    background-color: color(background-error);
    border-color: color(border-error);

    svg {
      color: color(content-error);
    }
  }
}

// Performance advice box
.performance-advice-box {
  &__title {
    margin-bottom: space(xl);

    svg {
      margin-right: space(sm);
      @include icon-size(md);
    }
  }

  &__list {
    li {
      position: relative;
      padding-left: space(md);

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: space(sm);
        display: inline-flex;
        width: 0.8rem;
        height: 0.8rem;
        margin-right: space(sm);
        background-color: color(content-info);
        border-radius: radius(rounded);
      }

      > span {
        margin-right: space(sm);
      }
    }

    li + li {
      margin-top: space(md);
    }
  }

  &__footer {
    display: flex;
    justify-content: center;
    margin-top: space(xl);
  }

  &-action {
    height: auto;
    padding: 0;

    &:hover {
      background-image: none;
      text-decoration: underline;
    }

    span {
      margin-right: space(xs);
    }
  }
}

.react-calendar-range-start {
  opacity: 1 !important;

  &,
  &:hover {
    background-color: color(background-selected-high) !important;
    color: color(content-selected-high) !important;
  }
}

@keyframes sparkle {
  0% {
    opacity: 1;
    transform: rotate(49deg) scale(0);
  }

  5% {
    transform: rotate(49deg) scale(0.4);
  }

  20% {
    transform: rotate(-4deg) scale(0.8);
  }

  33% {
    transform: rotate(-5deg) scale(0.74);
  }

  42% {
    transform: rotate(0deg) scale(0.73);
  }

  52% {
    transform: rotate(0deg) scale(0.82);
  }

  70% {
    transform: rotate(0deg) scale(0.63);
  }

  75% {
    opacity: 1;
  }

  90% {
    transform: rotate(0deg) scale(1);
  }

  95% {
    transform: rotate(0deg) scale(1);
    opacity: 0;
  }

  100% {
    opacity: 0;
    transform: rotate(49deg) scale(0);
  }
}

.cockade-first-page-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.cockade-first-page {
  width: 4rem;
  height: 4rem;

  &__color1 {
    fill: color(background-main);
  }

  &__color2 {
    fill: #f9b906;
  }

  &__color3 {
    fill: #fff04e;
  }

  &__color4 {
    fill: #fac110;
  }

  &__color5 {
    fill: #fed643;
  }

  &__sparkle {
    opacity: 0;
    transform-origin: center;
    transform-box: content-box;
    animation-iteration-count: infinite;
  }

  .gx-table-new__row:hover &,
  .crm-table__row:hover & {
    .sparkle-1 {
      animation-name: sparkle;
      animation-duration: 3s;
    }

    .sparkle-2 {
      animation-name: sparkle;
      animation-duration: 3s;
      animation-delay: 0.1s;
    }

    .sparkle-3 {
      animation-name: sparkle;
      animation-duration: 3s;
      animation-delay: 0.2s;
    }

    .sparkle-4 {
      animation-name: sparkle;
      animation-duration: 3s;
      animation-delay: 0.3s;
    }
  }
}

.first-page-badge {
  position: relative;
  display: inline-flex;
  background-color: #ffd255;
  padding: space(xs) 1.2rem;
  color: #7a4d08;
  font-weight: 500;
  box-shadow: 0 0.2rem 0 0 #daaa32;
  border-radius: radius(rounded);
  white-space: nowrap;

  .gx-table-new__row:hover &,
  .crm-table__row:hover & {
    &::after {
      content: '';
      position: absolute;
      opacity: 0;
      top: 0;
      left: 0;
      background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.03) 1%,
        rgba(255, 255, 255, 0.6) 30%,
        rgba(255, 255, 255, 0.85) 50%,
        rgba(255, 255, 255, 0.85) 70%,
        rgba(255, 255, 255, 0.85) 71%,
        rgba(255, 255, 255, 0) 100%
      );
      width: 15%;
      height: 100%;
      transform: skew(-10deg, 0deg);
      animation: move 1.5s;
      animation-iteration-count: infinite;
    }

    @keyframes move {
      0% {
        left: 0;
        opacity: 0;
      }

      5% {
        opacity: 0.2;
      }

      48% {
        opacity: 0.6;
      }

      80% {
        opacity: 0.2;
      }

      100% {
        left: 90%;
        opacity: 0;
      }
    }
  }
}
