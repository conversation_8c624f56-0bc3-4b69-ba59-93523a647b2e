@use 'sass:color';

@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$crm-table: 'crm-table';
$crm-cell-padding: 1.2rem;

.#{$crm-table} {
  table-layout: fixed;

  &-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: space(sm) space(lg) space(md);
    position: sticky;
    left: 0;

    &__results {
      color: color(content-medium);
    }

    &__actions {
      .gx-button + .gx-button {
        margin-left: space(sm);
      }
    }
  }

  &-wrapper {
    margin-right: -#{space(lg)};
  }

  &__ellipsisWrapper {
    max-width: 100%;
  }

  &__head {
    position: relative;

    th {
      position: sticky;
      top: 0;
      padding: 0;
      user-select: none;
      @include z-index(base, 3);

      &.#{$crm-table}__cellHead--pinned {
        &,
        &:hover {
          @include z-index(base, 4);
        }

        &:last-child,
        &:last-child:hover {
          @include z-index(base, 3);
        }
      }

      &:hover &:has(.crm-table__resize.is-active) {
        @include z-index(base, 3);
      }

      &:has(.crm-table__resize.is-active) {
        @include z-index(base, 4);
      }
    }

    .#{$crm-table}__row:has(+ .crm-table__headActions.is-visible) {
      .#{$crm-table}__cellHead {
        visibility: hidden;
        height: 0;
        padding: 0 space(md);
      }
    }

    &Actions {
      display: none;

      &Content {
        display: none;
        gap: space(md);
        align-items: center;
        height: 4rem;

        .gx-button + .gx-button {
          margin-left: 0;
        }
      }

      &.is-visible {
        display: table-row;
        top: 0;
        width: 100%;
        background-color: color(background-selected);
        @include z-index(base);

        td {
          position: sticky;
          left: space(lg);
          top: 0;
          height: 4rem;
          background-color: color(background-selected);
          @include z-index(base, 2);

          &:first-child {
            @include z-index(base, 3);

            &::before {
              content: '';
              display: block;
              width: space(lg);
              position: absolute;
              top: 0;
              left: -#{space(lg)};
              height: calc(100% + 1px);
              background-color: color(background-main);
            }
          }
        }

        .#{$crm-table}__headActionsContent {
          display: flex;
          padding: 0 space(md);
          box-shadow: 0 -1px 0 color(border-main) inset;
        }
      }

      &Buttons {
        .gx-button + .gx-button {
          margin-left: space(sm);
        }
      }
    }
  }

  &__body {
    td {
      border-bottom: 1px solid color(border-main);
    }

    td:not(:last-child) {
      border-right: 1px solid color(border-main);
    }
  }

  &__resize {
    position: absolute;
    top: 0;
    right: -#{space(xs)};
    display: flex;
    justify-content: center;
    width: 0.8rem;
    height: 100%;
    cursor: col-resize;
    @include z-index(base, 1);

    &::before {
      content: '';
      display: block;
      width: 0.4rem;
      height: 100%;
      background-color: transparent;
      border-radius: radius(rounded);
    }

    th &:hover,
    th &.is-active {
      &::before {
        background-color: color(background-brand);
      }
    }

    td:has(&.is-active) {
      border-right-color: color(background-brand);
    }
  }

  &__dndButton {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-shrink: 0;
    width: 1.6rem;
    height: 1.6rem;
    padding: 0;
    border: none;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    background-color: transparent;
    cursor: grab;

    .#{$crm-table}__cellHead:hover & {
      opacity: 1;
    }

    svg {
      @include icon-size(sm);
      color: color(content-low);
    }

    &:active {
      svg {
        color: color(--content-selected);
      }
    }
  }

  &__cell {
    padding: 0;
    color: color(content-high);
    background-color: color(background-main);
    @include typography(body-small);
    position: relative;

    &--pinned,
    &Head--pinned {
      @include z-index(base);
      position: sticky;

      &::after {
        content: '';
        display: block;
        width: 1.6rem;
        height: 100%;
        position: absolute;
        top: 0;
        transition: all 0.15s ease-in-out;
      }

      &:has(+ th:hover)::after {
        display: none;
      }

      &:first-child::after {
        right: -1.6rem;
        background: linear-gradient(90deg, rgba(color(background-reversed, true), 0.08) 0%, rgba(255, 255, 255, 0) 80%);

        .start-scroll &,
        .no-scroll & {
          opacity: 0;
        }
      }

      &:last-child::after {
        left: -1.6rem;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0, rgba(color(background-reversed, true), 0.08) 80%);

        .end-scroll &,
        .no-scroll & {
          opacity: 0;
        }

        .#{$crm-table}__row:hover & {
          display: none;
        }
      }
    }

    &--pinned:last-child {
      @include z-index(base, 1);
    }

    &Head--pinned {
      width: 100%;

      &:first-child,
      &:last-child {
        .crm-table__cellHead {
          &::before {
            content: '';
            display: block;
            width: space(lg);
            position: absolute;
            top: 0;
            height: 100%;
            background-color: color(background-main);
          }
        }
      }

      &:first-child {
        .gx-text-ellipsis {
          overflow: visible;
        }

        .crm-table__cellHead {
          &::before {
            left: -#{space(lg)};
          }
        }
      }

      &:last-child {
        .crm-table__cellHead {
          &::before {
            right: -#{space(lg)};
          }
        }
      }

      &:last-child {
        right: 0;
        width: 5.6rem;
      }
    }

    &--pinned {
      &:first-child,
      &:last-child {
        .crm-table__cellContent {
          &::before {
            content: '';
            display: block;
            width: space(lg);
            position: absolute;
            top: 0;
            height: calc(100% + 1px);
            background-color: color(background-main);
          }
        }
      }

      &:first-child {
        .crm-table__cellContent {
          &::before {
            left: -#{space(lg)};
          }
        }
      }

      &:last-child {
        .crm-table__cellContent {
          overflow: visible;

          &::before {
            right: -#{space(lg)};
          }
        }
      }
    }

    &--pinned:has(.crm-cell-actions) {
      .#{$crm-table}__row:hover & {
        &::before {
          content: '';
          position: absolute;
          left: -16rem;
          top: 0;
          transform: translateX(-100%);
          width: 2rem;
          height: 100%;
          background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, color(background-alt) 100%);
        }
      }

      &:has(.crm-cell-actions) {
        border-left: none !important;
      }
    }

    &Head {
      position: relative;
      display: flex;
      align-items: center;
      height: 4rem;
      padding: $crm-cell-padding space(md);
      @include typography(body-tiny);
      font-weight: 600;
      color: color(content-medium);
      background-color: color(background-main);
      box-shadow: 0 -1px 0 color(border-main) inset;

      &:hover {
        background-color: color(background-alt);
      }

      &--pinned &:hover {
        background-color: color(background-main);
      }

      &Ellipsis {
        display: flex;
        align-items: center;
        overflow: hidden;

        .crm-table__cellHead:first-child & {
          width: 100%;
          overflow: visible;
        }
      }

      &Label {
        white-space: nowrap;
      }

      .sortable {
        display: flex;
        align-items: center;
        gap: space(xs);
        cursor: pointer;

        svg {
          @include icon-size(sm);
          flex-shrink: 0;
          color: color(content-low);
        }

        &.asc {
          svg {
            --order-top-color: var(--content-high);
            --order-bottom-color: #{rgba(color(content-low, true), 0.6)};
          }
        }

        &.desc {
          svg {
            --order-top-color: #{rgba(color(content-low, true), 0.6)};
            --order-bottom-color: var(--content-high);
          }
        }
      }
    }

    &:has(+ .crm-table__cell--pinned):not(:last-child) {
      border-right: none;
    }

    &Content {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: space(sm);
      padding: $crm-cell-padding space(md);
      overflow: hidden;

      :is(td, th):not(.crm-table__cell--pinned) & {
        overflow: hidden;
      }

      .gx-text-ellipsis {
        max-width: 100%;
      }

      &:has(.crm-cell-actions) {
        position: absolute;
        top: 0;
        right: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 5.6rem;
        height: 100%;
        padding-left: 1.2rem;
        padding-right: 1.2rem;

        .#{$crm-table}__row:hover & {
          background: color(background-alt);
          width: 21.6rem;
        }

        .#{$crm-table}__row--selected:hover & {
          background: color(background-selected);
        }
      }
    }

    .gx-button,
    .gx-badge {
      width: max-content; //Fix for Firefox
    }
  }

  &__row {
    &:hover {
      .#{$crm-table}__cell {
        background-color: color(background-alt);
      }

      .crm-cell-actions .gx-button {
        display: inline-flex;
      }
    }

    &--selected {
      background-color: color(background-selected);
      &,
      &:hover {
        .#{$crm-table}__cell {
          background-color: color(background-selected);
        }
      }

      .#{$crm-table}__cell--pinned:has(.crm-cell-actions) {
        &::before {
          content: '';
          position: absolute;
          left: -16rem;
          top: 0;
          transform: translateX(-100%);
          width: 2rem;
          height: 100%;
          background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, color(background-brand-alt) 100%);
        }
      }

      &:hover .#{$crm-table}__cell--pinned:has(.crm-cell-actions) {
        &::before {
          background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, color(background-brand-alt) 100%);
        }
      }
    }

    &Actions {
      display: flex;

      .gx-button + .gx-button {
        margin-left: space(sm);
      }
    }
  }
}

.crm-cell-id {
  display: flex;
  align-items: center;
  gap: space(sm);
  font-size: 1.4rem;

  th & {
    gap: space(lg);
    font-size: 1.2rem;
  }

  .crm-table__cellHead:first-child & {
    overflow: hidden;
    margin-left: 2rem;
  }

  &__tools {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: space(sm);
    margin-right: space(md);

    &Button {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 2rem;
      height: 2rem;
      padding: 0;
      background: transparent;
      border: none;
      cursor: pointer;

      &::before {
        content: '';
        position: absolute;
        appearance: none;
        z-index: 0;
        left: -0.6rem;
        top: -0.6rem;
        width: 3.2rem;
        height: 3.2rem;
        display: block;
        margin: 0;
        border-radius: radius(rounded);
        background-color: rgba(0, 0, 0, 0.8);
        outline: none;
        opacity: 0;
        transform: scale(1);
        pointer-events: none;
        transition:
          opacity 0.3s,
          transform 0.2s;
      }

      &:hover {
        &::before {
          opacity: 0.1;
        }
      }

      &:disabled {
        opacity: 0.4;
        cursor: not-allowed;

        &::before {
          display: none;
        }
      }

      svg {
        @include icon-size(sm);
        flex-shrink: 0;
        color: color(content-selectable);
      }

      &.is-selected svg {
        color: color(background-selected-high);
      }

      &--favourite {
        @keyframes beatIn {
          0% {
            transform: scale(0);
            opacity: 0;
          }
          80% {
            transform: scale(1.2);
            opacity: 1;
          }
          100% {
            transform: scale(1);
            opacity: 1;
          }
        }

        @keyframes beatOut {
          0% {
            transform: scale(1);
            opacity: 1;
          }
          80% {
            transform: scale(1.2);
            opacity: 1;
          }
          100% {
            transform: scale(1);
            opacity: 0;
          }
        }

        svg {
          position: absolute;
        }

        .outline {
          opacity: 1;
        }

        .filled {
          opacity: 0;
          color: color(background-selected-high);
        }

        &.is-selected {
          animation-name: beatIn;
          animation-duration: 0.5s;

          .outline {
            opacity: 0;
          }
          .filled {
            opacity: 1;
          }
        }

        &:not(.is-selected) {
          .filled {
            animation-name: beatOut;
            animation-duration: 0.3s;
          }
        }
      }
    }

    .gx-checkbox {
      min-height: 2rem;
    }
  }
}

.crm-property-item {
  display: flex;
  align-items: center;
  gap: space(sm);
  max-width: 100%;
  cursor: pointer;

  &__pic {
    flex-shrink: 0;
    width: 6.4rem;
    height: 6.4rem;
    border-radius: radius(md);
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
    }

    &.no-image {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 6.4rem;
      height: 6.4rem;
      background-color: color(background-alt);

      svg {
        @include icon-size(sm);
        color: color(content-low);
      }
    }
  }

  &__rif {
    color: color(content-action);
  }

  &__desc {
    ul {
      display: flex;
      flex-direction: column;
      gap: space(xs);
    }

    li {
      white-space: nowrap;

      > span,
      > a {
        display: block;
      }
    }

    .gx-badge + .gx-badge {
      margin-left: space(xs);
    }
  }
}

.crm-cell-contentItem {
  height: 2.4rem;
  display: flex;
  align-items: center;
  gap: space(sm);

  &.gx-text-ellipsis {
    display: block;
    line-height: 2.4rem;
  }

  &--visibility {
    display: flex;
    gap: space(xs);
    max-width: 100%;
  }

  &--xsGap {
    gap: space(xs);
  }

  .gx-badge + .gx-badge {
    margin-left: 0;
  }
}

.crm-cell-actions {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  gap: space(sm);

  .gx-button {
    flex-shrink: 0;

    &:not(.crm-cell-actions__otherActions) {
      display: none;
    }

    & + .gx-button {
      margin-left: 0;
    }
  }

  // ContentDropdown customization for actions cell
  .actions-dropdown-no-caret {
    color: color(content-action);
    .gx-range-inputControl__caret {
      display: none;
    }
  }
}

// ActionList with disabled overlay for popover hover
.action-list-disabled-with-overlay {
  .gx-action-list {
    border-top: 1px solid color(border-main);

    &__item--withEndElement svg {
      @include icon-size(xs);
    }
  }
}

.unlockGetrixPopover {
  &__title {
    color: color(content-high);
    margin: 1.2rem 0 space(sm) 0;
  }

  &__description {
    color: color(content-medium);
    margin-bottom: 1.2rem;
  }

  &__icon {
    width: 3.6rem;
    height: auto;
    color: color(content-selected);
    background-color: color(background-brand-alt);
    padding: space(sm);
    border-radius: radius(rounded);
  }

  &__subtitle {
    color: color(content-high);
    font-weight: 600;
    margin-bottom: space(sm);
  }
  &__cta {
    display: flex;
    flex-direction: column;
    gap: space(xs);

    &Icon {
      width: space(md);
      height: auto;
      color: color(content-medium);
      margin-right: space(sm);
    }
    &Phone {
      color: color(content-medium);
    }
    &Email {
      color: color(content-medium);
    }
  }
}

.crm-filters-bar {
  position: sticky;
  left: 0;
  display: flex;
  align-items: center;
  gap: space(sm);
  padding: 0 space(lg);

  .swiper-carousel {
    width: auto;
  }

  &__allFilters {
    flex-shrink: 0;
  }

  &Slider {
    position: relative;
    display: grid;
    padding: space(md) 0;

    &__dropdownMaxHeight {
      max-height: calc(100vh - 28rem);
    }
  }

  &__filter {
    flex-shrink: 0;

    & + & {
      margin-left: 0;
    }
  }

  &__reset {
    flex-shrink: 0;
  }

  &--skeleton {
    height: 4.8rem;

    .gx-skeleton {
      height: 3.2rem;
    }
  }
}

.crm-sortable-list {
  padding: 1.2rem;
  font-weight: 600;

  &__title {
    font-size: 1.4rem;
  }

  &Dropdown {
    min-width: auto;
    max-width: auto;
    width: 30rem;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: space(sm);
  }

  &__container {
    display: flex;
    flex-direction: column;
    gap: space(sm);
    max-height: calc(100vh - 36rem);
    overflow-y: auto;
  }

  &__item {
    display: flex;
    align-items: center;
    gap: space(sm);
    width: 100%;
    padding: space(sm);
    border: none;
    border-radius: radius(md);
    color: color(content-high);
    @include typography(body-small);

    &Drag {
      flex-shrink: 0;
      padding: 0;
      background-color: transparent;
      border: none;
      cursor: grab;

      svg {
        @include icon-size(xs);
        color: color(content-medium);
      }
    }

    &:hover {
      background-color: color(background-alt);
    }

    .grab-overlay & {
      background-color: color(background-main);
      box-shadow: elevation(raised);
    }

    .gx-checkbox {
      min-height: 2rem;

      &__text {
        margin-top: 0.2rem;
      }
    }
  }
}

// crm-filter

.crm-filter {
  &__element {
    padding: space(lg) 0;

    &:first-child {
      margin-top: space(sm);
    }

    & + & {
      border-top: 1px solid color(border-main);
    }

    &Title {
      display: flex;
      align-items: center;
      gap: space(sm);
      margin-bottom: space(lg);

      svg {
        flex-shrink: 0;
        @include icon-size(md);
      }
    }

    @include media('>=#{breakpoint(sm)}') {
      &--horizontal {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: space(lg);

        .crm-filter__element {
          &Title {
            margin-bottom: 0;
          }

          &Content {
            width: 50%;
          }
        }
      }

      &Content {
        &--toggles {
          display: flex;
          flex-wrap: wrap;
          gap: space(md);

          > * {
            width: calc(50% - #{space(sm)});
          }
        }
      }
    }
  }

  .chips-wrapper {
    .gx-button + .gx-button {
      margin-left: 0;
    }
  }
}

.visibility-popover {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  color: color(content-medium);

  &__title {
    color: color(content-high);
  }

  &__item {
    display: flex;
    align-items: center;
    gap: space(sm);
  }

  &__footer {
    line-height: 1.2;
    padding-top: 1.2rem;
    border-top: 1px solid color(border-main);
  }
}

.crm-property-item-skeleton {
  display: flex;
  align-items: center;
  gap: space(sm);
  padding: space(md);

  &-head {
    padding-left: space(xl);
  }

  td & {
    padding: 0 0 0 space(xl);
  }

  & + & {
    margin-top: space(md);
  }

  &__image {
    width: 6.4rem;
    height: 6.4rem;
  }

  &__text {
    display: flex;
    flex-direction: column;
    gap: space(sm);

    div {
      width: 8.4rem;
      height: 1.2rem;

      &:last-child {
        width: 12rem;
      }
    }
  }
}
