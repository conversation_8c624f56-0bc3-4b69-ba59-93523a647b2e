// ==========================================================================
// Multiselect - Components
// ==========================================================================
@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$gx-multiselect: gx-multiselect;

@keyframes styled-select-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(359deg);
  }
}

.#{$gx-multiselect} {
  position: relative;

  &__selected {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: ($gx-unit-size * 5);
    padding: space(sm); ////TO-DO: rivedere spaziature
    @include typography(body-small);
    color: color(content-high); //TO-DO: cambiare quando ci sarà il componente select/input
    background-color: color(background-main);
    border: 0.1rem solid color(border-main);
    border-radius: radius(sm);
    cursor: pointer;

    @include media('screen', '<=#{breakpoint(sm)}') {
      @include typography(body);
    }

    .gx-icon {
      flex-shrink: 0;
      transition: transform 0.3s ease-in-out;
      @include icon-size(md);
    }

    // TO-DO: cambiare quando ci sarà il componente select/input
    &.ss-loading-options::after {
      content: '';
      display: block;
      animation: styled-select-spin 1s infinite linear;
      position: absolute;
      top: 0.9rem;
      right: space(xs);
      transform: translateY(-50%);
      height: 2.2rem;
      width: 2.2rem;
      background: none;
      border: 0.3rem rgba(136, 136, 136, 0.25) solid;
      border-top-color: #888888;
      border-radius: radius(rounded);
    }

    &.disabled {
      opacity: 0.65;
      cursor: not-allowed;
    }
  }

  &.is-error {
    .#{$gx-multiselect}__selected {
      border-color: color(border-error);
    }
  }

  &--negative {
    .#{$gx-multiselect}__selected {
      border-color: color(border-error);
      background-color: color(background-error);
    }
  }

  &.open {
    @include z-index(dropdown);

    .#{$gx-multiselect}__selected {
      .gx-icon {
        transform: rotate(180deg);
      }
    }
  }

  &__dropdown {
    width: 100%;
    max-height: 30rem;
    overflow-y: auto;

    label span {
      white-space: normal;
    }
  }
}
