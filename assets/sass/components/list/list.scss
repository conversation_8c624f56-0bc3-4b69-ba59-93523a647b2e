@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.ad-agenzia-immo {
  @include media('screen', '<#{breakpoint(md)}') {
    .gtx-ask-help {
      display: none;
    }
  }
}

.modal-filters {
  .filter-box__section {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    &__item {
      width: 100%;

      & + .filter-box__section__item {
        margin-left: 0;
      }
    }

    @include media('screen', '>=#{breakpoint(md)}') {
      &__item {
        width: calc(50% - #{$gx-unit-size * 2});

        & + .filter-box__section__item {
          margin-left: 0;
        }
      }
    }
  }

  .gx-checkbox {
    width: 100%;
    margin-top: 2.2rem;
  }
}

.improve-list {
  padding: space(sm) space(lg);
  margin: space(lg) 0 space(md);
  border: 0.1rem solid color(border-main);
  border-radius: radius(md);

  &-item {
    display: flex;
    align-items: center;
    padding: space(md) 0;

    & + & {
      border-top: 0.1rem solid color(border-main);
    }

    > svg {
      margin-right: space(md);
      @include icon-size(md);
      flex-shrink: 0;
    }

    &__title {
      @include typography(body-small);
      font-weight: bold;
      margin-bottom: space(xs);
    }

    ul {
      @include typography(body-small);
    }

    &__action {
      margin-left: auto;
    }
  }
}

.improve-alert {
  .gx-title-2 {
    margin-bottom: space(sm);
  }

  p {
    color: color(content-medium);
    margin-bottom: space(md);
  }

  .gx-icon {
    @include icon-size(md);
    margin-right: space(sm);
    color: color(content-medium);
  }

  &__agent {
    @include typography(body-small);
    margin-bottom: space(xs);
    font-weight: 600;
  }

  &__contact {
    > * + * {
      margin-left: space(md);
    }
  }
}

.carousel-detail {
  height: 28rem;

  .nd-carousel {
    &__content {
      padding: 0 0 ($gx-unit-size * 5) 0;
    }

    &__nav {
      display: flex;
      position: absolute;
      bottom: 0;
      left: 0;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: ($gx-unit-size * 5);
    }

    &__arrow {
      position: static;
      margin: 0 space(sm);
      padding: 0;

      &Icon {
        width: ($gx-unit-size * 3);
        min-width: ($gx-unit-size * 3) !important;
        height: ($gx-unit-size * 3);
        border-radius: radius(sm);

        &:hover {
          transform: translateX(0);
        }
      }
    }

    &__dotsNavigation {
      position: static;
      width: auto;
    }
  }
}

// Print detail
.print-property-detail {
  padding: ($gx-unit-size * 4);

  .gx-col-sm-6 {
    flex-basis: calc(100% / 12 * 6);
    flex-grow: 0;
    flex-shrink: 0;
    max-width: 50%;
    padding-right: 1.6rem;
    padding-left: 1.6rem;
  }

  .gx-accordion {
    border-width: 0;
    border-radius: 0;
    page-break-inside: avoid;

    .gx-icon {
      display: none;
    }

    & + .gx-accordion {
      margin-top: ($gx-unit-size * 4);
    }

    &__head {
      margin-bottom: ($gx-unit-size * 2);
      padding-top: ($gx-unit-size * 2);
      padding-right: 0;
      padding-left: 0;
      border-bottom: 0.1rem solid color(border-main);
      background-color: color(background-main);
    }

    &.is-close {
      .gx-accordion__body {
        max-height: 100vh;
        padding-bottom: ($gx-unit-size * 2);
      }
    }

    .gx-col-md-3 {
      flex-basis: calc(100% / 12 * 3);
      flex-grow: 0;
      flex-shrink: 0;
      max-width: 25%;
      padding-right: 1.6rem;
      padding-left: 1.6rem;
    }
  }

  .nd-carousel__nav {
    display: none;
  }
}

// SERVE PER MODALE RANKING (COPIA DA TRUNK) - ELIMINARE APPENA POSSIBILE
.nav-tabs {
  position: relative;

  .btn-filtri {
    position: absolute;
    top: 1.1rem;
    right: 3rem;

    .gtx-form-title {
      line-height: 4rem;
    }
  }
}

.modal-ranking-data {
  .alert {
    margin-right: 0;
    margin-left: 0;
  }

  @include media('screen', '<#{breakpoint(sm)}') {
    .nav-tabs {
      clear: both;
    }
  }
}

//Eccezioni lucchetto reportistica
//TO-DO: capire se mantenere
.gx-text-default {
  color: color(content-medium);

  &.gx-button {
    &:hover,
    &:focus {
      color: color(content-medium);
    }
  }
}

.gx-text-light {
  &.gx-button {
    &:hover,
    &:focus {
      color: color(content-low);
    }
  }
}

.gx-text-warning {
  &.gx-button {
    &:hover,
    &:focus {
      color: color(content-warning);
    }
  }
}

//TO-DO: componentizzare?
.csv-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  height: ($gx-unit-size * 5);
  border-top: 0.1rem solid color(border-main);
  border-bottom: 0.1rem solid color(border-main);

  .gx-icon {
    margin-right: space(xs);
  }
}

.property-ranking-details-modal {
  &__qualityIndicator {
    display: flex;
    align-items: center;
    margin: space(lg) 0;
    font-size: 2.2rem;
  }

  &__body {
    padding: space(md) space(md) 0;

    ul {
      padding-left: space(md);
      list-style: disc;
    }
  }

  &__score {
    & + & {
      margin-top: space(md);
    }
  }

  &__scoreHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: space(md);
    padding-bottom: space(sm);
    border-bottom: 0.1rem solid color(border-main);

    strong {
      font-size: 2rem;
    }
  }

  &__scores {
    & + & {
      margin-top: space(sm);
    }
  }

  &__compare {
    display: flex;
    align-self: center;

    & > div {
      display: flex;
      align-items: center;
    }

    .gx-col-xs-1 {
      justify-content: center;
    }
  }

  &__comparisonData {
    .gx-row {
      padding: space(sm) 0;
      border-bottom: 0.1rem solid color(border-main);

      &:last-of-type {
        padding: space(md) 0;
        border-bottom: 0;
        background-color: color(background-alt);
      }
    }

    .gx-col-xs-2 {
      display: flex;
      justify-content: center;
    }
  }
}


.list-counter-button {
  .gx-notification-badge {
    position: absolute;
    right: -0.5rem;
    top: -0.5rem;
  }
}