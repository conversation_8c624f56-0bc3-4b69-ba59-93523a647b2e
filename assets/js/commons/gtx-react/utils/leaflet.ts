import L, { LatLng } from 'leaflet';

const markerImg = '/bundles/base/getrix/common/img/getrix-pin.png';
const markerRetinaImg = '/bundles/base/getrix/common/img/<EMAIL>';

export const convertToLatLng = (coords: any): LatLng => L.latLng(coords.lat, coords.lng);

export const getMarkerIcon = (config: any) => {
    const icon = L.icon({
        iconUrl: markerImg,
        iconRetinaUrl: markerRetinaImg,
        iconSize: config.iconSize,
        iconAnchor: config.iconAnchor ? config.iconAnchor : null,
        popupAnchor: config.popupAnchor ? config.popupAnchor : [-3, -76],
    });

    return icon;
};
