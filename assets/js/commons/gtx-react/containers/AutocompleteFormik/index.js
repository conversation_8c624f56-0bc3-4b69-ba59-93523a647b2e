import React, { useRef, useEffect, useState } from 'react';
import classNames from 'classnames';
import useAutocomplete from '../../hooks/useAutocomplete';
import { useFormikContext } from 'formik';
import { HelperText } from '@gx-design/helper-text';
import { Icon } from '@gx-design/icon';
import { trans } from '@pepita-i18n/babelfish';
import { validEmail } from '../../utils/utility';

const styles = {
    //general form
    label: '',
    field: 'step__formField',
    container: 'step__formBlock',
    isError: 'gx-input--negative',
    errorMessage: 'gx-error-message',

    //autocomplete
    autocompleteWrap: 'gx-autocompleteWrap',
    autocompleteMobileFull: 'gx-autocompleteMobileFull',
    autocompleteMain: 'gx-autocomplete',
    autocompleteContainer: 'gx-autocomplete__inputContainer',
    autocompleteInput: 'gx-input',
    autocompletelistContainer: 'gx-autocomplete__list is-open',
    autocompletelistContent: 'gx-autocomplete__listContent',
    autocompletelistItem: 'gx-autocomplete__listItem',
    autocompletelistItemActive: 'is-focused',

    //list
    list: 'gx-stack',
    listItem: 'gx-stackItem',
    listItemNotInteractive: 'gx-stack--nonInteractive',
    listItemIcon: 'gx-stackItem__graphic',
    listItemText: 'gx-stackItem__text',

    //input
    input: 'gx-textField__nativeControl',
};

const highlightUserInput = (userInput, itemLabel) => ({
    __html: itemLabel.replace(new RegExp(userInput, 'i'), (match) => `<b>${match}</b>`),
});

const AutocompleteFormik = ({
    defaultValue,
    label = null,
    minLength = 3, // min length to start an API request
    name,
    onChange,
    placeholder = '',
    onBlur,
    onFocus,
    onInputKeyDown,
    onInputBlur,
    getAutocompleteApi,
    selectedLabelKey,
    itemKey,
    extraParams,
    classWrap = styles.autocompleteWrap,
    isMobile,
    formCallback,
    labelMobile = null,
    placeholderMobile = null,
    formatFn = null,
    showRequiredSymbol,
    onChangeRef,
    disabled = false,
    ['data-testid']: testId,
}) => {
    const { setFieldValue, getFieldProps, values, errors } = useFormikContext();
    // This part is bug prone, and this hook should be refactored
    const autocomplete = useAutocomplete({
        getAutocompleteApi,
        selectedLabelKey,
        defaultValue,
        minLength,
        formatFn,
        onChange: (item) => {
            onChange && onChange(name, item);
            formCallback && formCallback(name, item);
        },
        extraParams,
    });

    const [selectedValue, setSelectedValue] = useState('');

    useEffect(() => {
        if (onChangeRef) {
            onChangeRef((item) => {
                autocomplete.setSelected(item);
                setFieldValue(name, item.id);
            });
        }
    }, []);

    useEffect(() => {
        if (values[name] === '') {
            autocomplete.setSelected(null);
        }
    }, [values[name]]);

    const fullscreenMode = autocomplete.focus && isMobile;

    const handleFocus = () => {
        autocomplete.setFocus(true);
        onFocus && onFocus();
    };

    const handleChange = (value) => {
        autocomplete.setValue(value);
        onChange && onChange();
    };

    const handleBlur = () => {
        autocomplete.setFocus(false);

        // Check if input value is a valid email and trigger onInputBlur if provided
        if (onInputBlur) {
            const emailValue = autocomplete.value?.trim();
            if (emailValue && validEmail(emailValue)) {
                onInputBlur(emailValue);
            }
        }

        onBlur && onBlur();
    };

    const handleInputKeyDown = (evt) => {
        // if onInputKeyDown was passed, pass the value
        if (onInputKeyDown) {
            onInputKeyDown(evt, autocomplete.value);
        }
    };

    const content = !autocomplete.open ? null : autocomplete.loading ? (
        <li className={classNames([styles.listItem, styles.autocompletelistItem])}>
            <span className={styles.listItemText}>{`${trans('label.loading')}...`}</span>
        </li>
    ) : autocomplete.items.length > 0 ? (
        autocomplete.items.map((item, i) => (
            <li
                data-testid={`${testId}-item-${i}`}
                key={i}
                onMouseDown={() => {
                    setSelectedValue(item);
                    autocomplete.setSelectedIndex(i);
                }}
                className={classNames([
                    styles.listItem,
                    styles.autocompletelistItem,
                    autocomplete.highlight === i && styles.autocompletelistItemActive,
                ])}
            >
                <span className={styles.listItemIcon}>
                    <Icon name="marker-outline" />
                </span>
                <span
                    className={styles.listItemText}
                    dangerouslySetInnerHTML={highlightUserInput(
                        autocomplete.search,
                        formatFn ? formatFn(item) : item.name
                    )}
                />
            </li>
        ))
    ) : (
        <li className={classNames([styles.listItem, styles.autocompletelistItem])}>
            <span className={styles.listItemText}>{trans('label.no_results')}</span>
        </li>
    );

    const inputRef = useRef();
    const ulRef = useRef();

    useEffect(() => {
        if (!autocomplete.focus && document.activeElement === inputRef.current) {
            inputRef.current.blur();
        }

        if (fullscreenMode) {
            document.documentElement.classList.add('lockScreen');
        } else if (!autocomplete.focus && isMobile) {
            document.documentElement.classList.remove('lockScreen');
        }
    }, [autocomplete.focus]);

    useEffect(() => {
        if (ulRef && ulRef.current) {
            let listElHeight = ulRef.current.firstChild.scrollHeight;
            let listHeight = ulRef.current.clientHeight + 10;

            ulRef.current.scrollTop = Math.round(autocomplete.highlight * listElHeight - listHeight / 2);
        }
    }, [autocomplete.highlight]);

    useEffect(() => {
        setFieldValue(name, selectedValue[itemKey] ?? '');
    }, [selectedValue]);

    useEffect(() => {
        if (!defaultValue || !itemKey) {
            return;
        }

        autocomplete.changeSelected(defaultValue);
        setFieldValue(name, defaultValue[itemKey]);
    }, [defaultValue]);

    const getPlaceholder = () => {
        if (!placeholderMobile || !isMobile) {
            return placeholder;
        }

        if (fullscreenMode && placeholderMobile) {
            return placeholderMobile;
        }

        return placeholder;
    };

    return (
        <div className={classNames([classWrap, autocomplete.focus && styles.autocompleteMobileFull])}>
            {label && (
                <label className={classNames([styles.label], 'gx-label')} htmlFor={name}>
                    {label}
                    {showRequiredSymbol ? <span className="gx-label__required">*</span> : null}
                </label>
            )}
            {fullscreenMode && labelMobile && (
                <label className={classNames([styles.label], 'gx-label')} htmlFor={name}>
                    {labelMobile}
                </label>
            )}
            <div className={styles.autocompleteMain}>
                <div className={styles.autocompleteContainer}>
                    <input id={name} name={name} type="hidden" {...getFieldProps(name)} value={values[name] ?? ''} />
                    <input
                        data-testid={testId}
                        type="text"
                        id={`__${name}_helper`}
                        name={`__${name}_helper`}
                        className={classNames([styles.autocompleteInput, errors && errors[name] && styles.isError])}
                        placeholder={getPlaceholder()}
                        onChange={(e) => {
                            handleChange(e.target.value);
                        }}
                        onFocus={handleFocus}
                        onBlur={handleBlur}
                        autoComplete="off"
                        autoCorrect="off"
                        value={autocomplete.value}
                        ref={inputRef}
                        disabled={disabled}
                        onKeyDown={handleInputKeyDown}
                    />
                    {!autocomplete.focus && autocomplete.value && (
                        <div
                            className="gx-input-clear gx-input-clear--circled"
                            onClick={
                                disabled
                                    ? undefined
                                    : () => {
                                          autocomplete.setSelected(null);
                                          setFieldValue(name, '');
                                          inputRef.current?.focus();
                                      }
                            }
                        >
                            <Icon name="cross-circle--active" />
                        </div>
                    )}
                </div>
                {open && content ? (
                    <ul
                        ref={ulRef}
                        className={classNames([
                            styles.autocompletelistContainer,
                            styles.autocompletelistContent,
                            styles.list,
                            autocomplete.open && autocomplete.items.length == 0 && styles.listItemNotInteractive,
                            autocomplete.focus && isMobile && styles.listItemNotInteractive,
                        ])}
                    >
                        {content}
                    </ul>
                ) : null}
            </div>
            {errors && errors[name] && <HelperText text={errors[name]} style="error" />}
        </div>
    );
};

export default AutocompleteFormik;
