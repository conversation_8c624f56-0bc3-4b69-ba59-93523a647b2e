import { FC } from 'react';
import { useFormikContext, Form, FieldArray } from 'formik';
import {
    GtxTimePicker,
    InputWithAddon,
    Select,
    Textarea,
} from 'gtx-react/components/formik';
import { HelperText } from '@gx-design/helper-text';
import { Icon } from '@gx-design/icon';
import { IRealEstateObj } from './types';
import { ImmovisitaScheduleModalFormFields, ISelectOption } from './types/form';
import { IvSchModalFormPropertyBase } from './IvSchModalFormPropertyBase';
import { Button } from '@gx-design/button';
import { trans } from '@pepita-i18n/babelfish';
import { DatePickerInput } from 'gtx-react/components/DatePicker';

export interface IIvSchModalFormPropertyProps {
    loadingError: boolean;
    onChangeRefClick: () => void;
    loading: boolean;
    realEstateObj: IRealEstateObj | null;
    customClass?: string;
}
interface ImmovisitaScheduleModalFormProps {
    realEstateObj: IRealEstateObj | null;
    loading: boolean;
    loadingError: boolean;
    onChangeRefClick: () => void;
    agents: ISelectOption[];
    isLoadingAgents: boolean;
    propertyBoxComponent?: React.FC<IIvSchModalFormPropertyProps>;
}

export const IvSchModalForm: FC<ImmovisitaScheduleModalFormProps> = ({
    realEstateObj,
    loading,
    loadingError,
    onChangeRefClick,
    agents,
    isLoadingAgents,
    propertyBoxComponent: PropertyBoxComponent,
}) => {
    const { values, getFieldMeta, setFieldValue } =
        useFormikContext<ImmovisitaScheduleModalFormFields>();

    const TODAY = new Date();

    return (
        <>
            {PropertyBoxComponent ? (
                <PropertyBoxComponent
                    loadingError={loadingError}
                    onChangeRefClick={onChangeRefClick}
                    loading={loading}
                    realEstateObj={realEstateObj}
                />
            ) : (
                <IvSchModalFormPropertyBase
                    loadingError={loadingError}
                    onChangeRefClick={onChangeRefClick}
                    loading={loading}
                    realEstateObj={realEstateObj}
                />
            )}
            {getFieldMeta('propertyId').error && (
                <HelperText
                    text={getFieldMeta('propertyId').error as string}
                    style="negative"
                />
            )}
            <Form name="scheduled_visit">
                <div className="gx-scheduled-visit-modal__body">
                    <div className="gx-input-wrapper">
                        <label className="gx-label">
                            {trans(
                                'label.immovisita_scheduled.set_date_and_time'
                            )}{' '}
                            <span className="gx-label__required">*</span>
                        </label>
                        <div className="gx-scheduled-visit-modal__time">
                            <div className="gx-date-input-wrapper">
                                <DatePickerInput
                                    name="scheduled_date"
                                    minDate={TODAY}
                                />
                            </div>
                            <div className="gx-scheduled-visit-modal__date">
                                <GtxTimePicker
                                    id="scheduled_time"
                                    name="scheduled_time"
                                    showErrorMessage
                                />
                            </div>
                        </div>
                    </div>
                    <Select
                        id="agent"
                        name="agent"
                        hasNoSelectOption={false}
                        label={trans('label.agent')}
                        className="gx-scheduled-visit-modal__agentSelect"
                        isLoading={isLoadingAgents}
                        disabled={isLoadingAgents}
                        content={agents}
                        onChange={(ev) => {
                            setFieldValue('agent', parseInt(ev.target.value));
                        }}
                        showRequiredSymbol
                    />
                    <div className="gx-input-wrapper">
                        <label className="gx-label">
                            {trans('label.immovisita_scheduled.guest_email')}{' '}
                            <span className="gx-label__required">*</span>
                        </label>
                        <FieldArray name="guests">
                            {({ remove, push }) => (
                                <>
                                    {values.guests?.map((guest, index) => (
                                        <div
                                            className="gx-scheduled-visit-modal__addUserRow"
                                            key={`guest-${index}`}
                                        >
                                            <InputWithAddon
                                                placeholder={trans(
                                                    'label.mail_address'
                                                )}
                                                name={`guests.${index}.email`}
                                                id={`guests.${index}.email`}
                                                className="gx-input--withAddon"
                                                addon={{
                                                    icon: 'mail',
                                                }}
                                                isRemovable={
                                                    values.guests.length > 1
                                                }
                                                onRemoveClick={() =>
                                                    remove(index)
                                                }
                                            />
                                        </div>
                                    ))}
                                    <Button
                                        className="gx-scheduled-visit-modal__addUser"
                                        onClick={() => push({ email: '' })}
                                    >
                                        <Icon name="plus" />
                                        <span>
                                            {trans(
                                                'btn.immovisita.add_guests.add_guest'
                                            )}
                                        </span>
                                    </Button>
                                </>
                            )}
                        </FieldArray>
                    </div>
                    <div className="gx-input-wrapper">
                        <label className="gx-label">
                            {trans('label.immovisita_scheduled.add_notes')}
                        </label>
                        <Textarea id={`note`} name="note" rows="4" />
                    </div>
                </div>
            </Form>
        </>
    );
};
