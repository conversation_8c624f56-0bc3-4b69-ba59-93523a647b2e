import React from 'react';
import { Input } from '../../../components';
import { FunctionComponent } from 'react';
import { IUseChangeRealEstateFiltersFormValues } from './ChangeRealEstateFilters/useChangeRealEstateFiltersFormValues';
import { useChangeRealEstateSubmitWithAbortOnUnmount } from './useChangeRealEstateSubmitWithAbortOnUnmount';
import { Button } from '@gx-design/button';
import { trans } from '@pepita-i18n/babelfish';

export interface IChangeRealEstateSearch {
    formObject: IUseChangeRealEstateFiltersFormValues;
    openFilters: boolean;
    setOpenFilters: (open: boolean) => void;
}

export const ChangeRealEstateSearch: FunctionComponent<
    IChangeRealEstateSearch
> = ({ openFilters, setOpenFilters, formObject }) => {
    const { freeTextSearch, loadingList } = formObject;

    const onSubmit = useChangeRealEstateSubmitWithAbortOnUnmount(
        formObject.onFreeTextSearchSubmit
    );

    return (
        <div className="iv-changeRealEstate__search">
            <form
                onSubmit={(evt) => {
                    evt.preventDefault();
                }}
                className="iv-changeRealEstate__searchInput"
            >
                {/** @ts-ignore */}
                <Input
                    {...freeTextSearch}
                    className="iv-changeRealEstate__filter"
                    placeholder={trans('label.search_string_form')}
                />
                <Button
                    variant="accent"
                    onClick={() => {
                        onSubmit();
                    }}
                    disabled={loadingList}
                >
                    {trans('label.search')}
                </Button>

                <input type="submit" style={{ display: 'none' }} />
            </form>
            <div className="iv-changeRealEstate__moreFilters">
                {/** @ts-ignore */}
                <Button onClick={() => setOpenFilters(!openFilters)}>
                    {openFilters ? trans('label.close') : trans('label.filter')}
                </Button>
            </div>
        </div>
    );
};
