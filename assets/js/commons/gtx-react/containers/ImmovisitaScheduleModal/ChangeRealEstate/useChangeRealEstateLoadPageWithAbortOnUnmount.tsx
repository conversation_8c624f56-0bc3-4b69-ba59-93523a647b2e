import { useEffect, useRef } from 'react';
import { ISubmitReturn } from './ChangeRealEstateFilters/useChangeRealEstateFiltersFormValues';

export const useChangeRealEstateLoadPageWithAbortOnUnmount = (
    loadPage: (pageNum: number) => ISubmitReturn
): ((pageNum: number) => ISubmitReturn) => {
    const abortLastPageFetch = useRef<(() => void)|null>(null);

    const loadPageByNum = (pageNum: number) => {
        const submitObj = loadPage(pageNum);

        abortLastPageFetch.current = submitObj.abort;

        submitObj.promise.finally(() => {
            abortLastPageFetch.current = null;
        });

        return submitObj;
    };

    //Aborting last fetch if running on cleanup
    useEffect(
        () => () => {
            if (abortLastPageFetch.current) {
                abortLastPageFetch.current();
            }
        },
        []
    );

    return loadPageByNum;
};
