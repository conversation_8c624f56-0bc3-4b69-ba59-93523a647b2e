import { useEffect, useRef } from 'react';
import { ISubmitReturn } from './ChangeRealEstateFilters/useChangeRealEstateFiltersFormValues';

export const useChangeRealEstateSubmitWithAbortOnUnmount = (
    onSubmit: () => ISubmitReturn
): (() => ISubmitReturn) => {
    const abortLastPageFetch = useRef<(() => void) | null>(null);

    const handleSubmit = () => {
        const submitObj = onSubmit();

        abortLastPageFetch.current = submitObj.abort;

        submitObj.promise.finally(() => {
            abortLastPageFetch.current = null;
        });

        return submitObj;
    };

    //Aborting last fetch if running on cleanup
    useEffect(
        () => () => {
            if (abortLastPageFetch.current) {
                abortLastPageFetch.current();
            }
        },
        []
    );

    return handleSubmit;
};
