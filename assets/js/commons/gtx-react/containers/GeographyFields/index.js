import React, { useEffect, useState, useReducer } from 'react';
import PropTypes from 'prop-types';
import { useFormikContext } from 'formik';
import { trans } from '@pepita-i18n/babelfish';
import { lookupApi } from '../../web-api';
import { GxFkSelect } from 'gtx-react/components/gx-formik';

const SOURCES = {};

Object.defineProperties(SOURCES, {
    COUNTRY: {
        enumerable: true,
        configurable: false,
        writable: false,
        value: 'countries',
    },
    REGION: {
        enumerable: true,
        configurable: false,
        writable: false,
        value: 'regions',
    },
    PROVINCE: {
        enumerable: true,
        configurable: false,
        writable: false,
        value: 'provinces',
    },
    CITY: {
        enumerable: true,
        configurable: false,
        writable: false,
        value: 'cities',
    },
    ZONE: {
        enumerable: true,
        configurable: false,
        writable: false,
        value: 'zones',
    },
});

const reducer = (state = initialState, action) => {
    switch (action.type) {
        case 'SET_LOADING':
            return { ...state, ...action.payload };
        default:
            return state;
    }
};

const Wrapper = ({ wrap, children }) => {
    return <>{wrap ? <div className="filter-box__section">{children}</div> : children}</>;
};

const GeographyFields = ({
    hasCountry,
    hasRegion,
    hasProvince,
    hasCity,
    hasZone,
    noPlaceholder,
    noWrapper,
    initialValuesMap,
    fieldLabelsMap,
    customEndpointsMap,
    customLoadersMap,
    onLoadHandlerMap,
    placeholders,
    disabled,
}) => {
    const { values, setFieldValue } = useFormikContext();

    const [countries, setCountries] = useState([]);
    const [regions, setRegions] = useState([]);
    const [provinces, setProvinces] = useState([]);
    const [cities, setCities] = useState([]);
    const [zones, setZones] = useState([]);
    const [loadState, dispatch] = useReducer(reducer, {
        countries: false,
        regions: false,
        provinces: false,
        cities: false,
        zones: false,
    });
    const [init, setInit] = useState(true);

    const stateSetters = {
        setCountries,
        setRegions,
        setProvinces,
        setCities,
        setZones,
    };

    const loadData = (sourceType, param, method) => {
        if (disabled) {
            return;
        }

        const loaders = {
            loader: null,
            data: null,
        };

        if (sourceType in SOURCES) {
            const sourceKey = SOURCES[sourceType];
            const setter = stateSetters[`set${sourceKey.charAt(0).toUpperCase()}${sourceKey.slice(1)}`];
            loaders.loader = customLoadersMap[sourceKey];
            loaders.data = initialValuesMap[sourceKey];
            loaders.endpoint = customEndpointsMap[sourceKey];

            const { loader, endpoint, data } = loaders;

            const remote = () => {
                const resp = loader ? loader(param) : lookupApi(endpoint, param);

                if (resp instanceof Promise) {
                    dispatch({
                        type: 'SET_LOADING',
                        payload: { [sourceKey]: true },
                    });

                    resp.then((resp) => {
                        if (!resp || !resp.data) {
                            dispatch({
                                type: 'SET_LOADING',
                                payload: { [sourceKey]: false },
                            });
                            return;
                        }

                        setter(resp?.data);
                        dispatch({
                            type: 'SET_LOADING',
                            payload: { [sourceKey]: false },
                        });

                        onLoadHandlerMap && onLoadHandlerMap[sourceKey] ? onLoadHandlerMap[sourceKey]() : null;
                    });
                } else if (Array.isArray(resp)) {
                    setter(resp);
                }
            };

            const local = () => setter(data);

            if (method) {
                method === 'remote' && remote();
                method === 'local' && local();
            } else {
                if (loader || endpoint) {
                    remote();
                }

                if (data) {
                    setter(data);
                }
            }
        } else {
            throw new Error(`Supported source type is any of ${Object.keys(SOURCES).join(',')}`);
        }
    };

    useEffect(() => {
        if (!initialValuesMap.hasOwnProperty('countries') || !hasCountry) {
            return;
        }

        if (!initialValuesMap.countries) {
            dispatch({
                type: 'SET_LOADING',
                payload: { [SOURCES.COUNTRIES]: true },
            });
            return;
        }

        dispatch({
            type: 'SET_LOADING',
            payload: { [SOURCES.COUNTRIES]: false },
        });
        loadData('COUNTRY', initialValuesMap.countries, 'local');
    }, [initialValuesMap]);

    useEffect(() => {
        if (hasCountry) {
            const { country } = values;

            setFieldValue('region', '');
            setRegions([]);

            if (hasProvince) {
                setFieldValue('province', '');
                setProvinces([]);
            }

            if (hasCity) {
                setFieldValue('city', '');
                setCities([]);
            }

            if (hasZone) {
                setFieldValue('zones', '');
                setZones([]);
            }

            country && loadData('REGION', country, 'remote');
        }
    }, [values.country]);

    useEffect(() => {
        if (hasRegion) {
            const { region } = values;

            if (hasProvince) {
                setFieldValue('province', '');
                setProvinces([]);
            }

            if (hasCity) {
                setFieldValue('city', '');
                setCities([]);
            }

            region && loadData('PROVINCE', region, 'remote');
        }
    }, [values.region]);

    useEffect(() => {
        if (hasProvince) {
            const { province } = values;

            if (hasCity) {
                setFieldValue('city', '');
                setCities([]);
            }

            if (hasZone) {
                setFieldValue('zones', '');
                setZones([]);
            }

            province && loadData('CITY', province, 'remote');
        }
    }, [values.province]);

    useEffect(() => {
        if (hasCity) {
            const { city } = values;

            if (hasZone) {
                setFieldValue('zones', '');
                setZones([]);
                city && loadData('ZONE', city, 'remote');
            }
        }
    }, [values.city]);

    useEffect(() => {
        if (init) {
            for (let source in SOURCES) {
                loadData(source, null);
            }
            setInit(false);
        }
    }, [init]);

    useEffect(() => {
        if (values?.region && hasRegion) {
            setFieldValue('region', values.region);
        }

        if (values?.province && hasProvince) {
            setFieldValue('province', values.province);
        }

        if (values?.city && hasCity) {
            setFieldValue('city', values.city);
        }

        if (values?.zones && hasZone) {
            setFieldValue('zones', values.zones);
        }
    }, []);

    return (
        <Wrapper wrap={!noWrapper}>
            <>
                {hasCountry && (
                    <div className="filter-box__section__item">
                        <GxFkSelect
                            id="country"
                            name="country"
                            label={fieldLabelsMap?.countries ? fieldLabelsMap.countries : trans('label.nation')}
                            placeholder={placeholders?.country}
                            disabled={disabled || !countries || countries.length === 0}
                            options={countries ? countries : initialValuesMap[SOURCES.COUNTRY] ?? []}
                            isLoading={loadState[SOURCES.COUNTRY]}
                        />
                    </div>
                )}
                {hasRegion && (
                    <div className="filter-box__section__item">
                        <GxFkSelect
                            id="region"
                            name="region"
                            label={fieldLabelsMap?.region ? fieldLabelsMap.region : trans('label.region')}
                            placeholder={placeholders?.region}
                            options={regions ? regions : initialValuesMap[SOURCES.REGION] ?? []}
                            disabled={!regions || regions.length === 0}
                            isLoading={loadState[SOURCES.REGION]}
                        />
                    </div>
                )}
                {hasProvince && (
                    <div className="filter-box__section__item">
                        <GxFkSelect
                            id="province"
                            name="province"
                            label={fieldLabelsMap?.province ? fieldLabelsMap.province : trans('label.province')}
                            placeholder={placeholders?.province}
                            options={provinces ? provinces : initialValuesMap[SOURCES.PROVINCE] ?? []}
                            disabled={disabled || !provinces || provinces.length === 0}
                            isLoading={loadState[SOURCES.PROVINCE]}
                        />
                    </div>
                )}
                {hasCity && (
                    <div className="filter-box__section__item">
                        <GxFkSelect
                            id="city"
                            name="city"
                            label={fieldLabelsMap?.city ? fieldLabelsMap.city : trans('label.municipality')}
                            placeholder={placeholders?.city}
                            options={cities ? cities : initialValuesMap[SOURCES.CITY] ?? []}
                            disabled={disabled || !cities || cities.length === 0}
                            isLoading={loadState[SOURCES.CITY]}
                        />
                    </div>
                )}
                {hasZone && (
                    <div className="filter-box__section__item">
                        <GxFkSelect
                            id="zones"
                            name="zones"
                            label={fieldLabelsMap?.zones ? fieldLabelsMap.zones : trans('label.zone')}
                            placeholder={placeholders?.zone}
                            options={zones ? zones : initialValuesMap[SOURCES.ZONES] ?? []}
                            disabled={disabled || !zones || zones.length === 0}
                            isLoading={loadState[SOURCES.ZONE]}
                        />
                    </div>
                )}
            </>
        </Wrapper>
    );
};

GeographyFields.propTypes = {
    disabled: PropTypes.bool,
    hasCountry: PropTypes.bool,
    hasRegion: PropTypes.bool,
    hasProvince: PropTypes.bool,
    hasCity: PropTypes.bool,
    hasZone: PropTypes.bool,
    noWrapper: PropTypes.bool,
    noPlaceholder: PropTypes.arrayOf(PropTypes.oneOf(['province', 'region', 'city', 'country', 'zones'])),
    fieldLabelsMap: PropTypes.shape({
        province: PropTypes.string,
        region: PropTypes.string,
        city: PropTypes.string,
        country: PropTypes.string,
        zones: PropTypes.string,
    }),
    initialValuesMap: PropTypes.oneOfType([
        PropTypes.bool,
        PropTypes.shape({
            countries: PropTypes.array,
            regions: PropTypes.array,
            provinces: PropTypes.array,
            cities: PropTypes.array,
            zones: PropTypes.array,
        }),
    ]),
    placeholders: PropTypes.oneOfType([
        PropTypes.bool,
        PropTypes.shape({
            country: PropTypes.string,
            region: PropTypes.string,
            province: PropTypes.string,
            city: PropTypes.string,
            zone: PropTypes.string,
        }),
    ]),
    customEndpointsMap: PropTypes.oneOfType([
        PropTypes.bool,
        PropTypes.shape({
            countries: PropTypes.string,
            regions: PropTypes.string,
            provinces: PropTypes.string,
            cities: PropTypes.string,
            zones: PropTypes.string,
        }),
    ]),
    customLoadersMap: PropTypes.oneOfType([
        PropTypes.bool,
        PropTypes.shape({
            countries: PropTypes.func,
            regions: PropTypes.func,
            provinces: PropTypes.func,
            cities: PropTypes.func,
            zones: PropTypes.func,
        }),
    ]),
    onLoadHandlerMap: PropTypes.oneOfType([
        PropTypes.bool,
        PropTypes.shape({
            countries: PropTypes.func,
            regions: PropTypes.func,
            provinces: PropTypes.func,
            cities: PropTypes.func,
            zones: PropTypes.func,
        }),
    ]),
};

GeographyFields.defaultProps = {
    disabled: false,
    hasCountry: true,
    hasRegion: true,
    hasProvince: true,
    hasCity: true,
    hasZone: true,
    initialValuesMap: false,
    placeholders: false,
    customEndpointsMap: false,
    customLoadersMap: false,
    onLoadHandlerMap: false,
    noWrapper: false,
};

export default GeographyFields;
