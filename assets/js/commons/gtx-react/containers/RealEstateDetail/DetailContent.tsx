import { trans } from '@pepita-i18n/babelfish';
import clsx from 'clsx';
import { useMemo } from 'react';

import {
    AUCTION_SECTIONS_DATA,
    NEW_CONSTRUCTION_SECTION_DATA,
    PROPERTY_SECTIONS_DATA,
    SECTIONS_BY_TYPOLOGY_ID,
} from 'constants/realEstateSectionsData';
import { RealEstateDetailProps } from 'gtx-react/containers/RealEstateDetail/RealEstateDetail';
import { DetailPropertyType } from 'lib/REST/types/detail-property';
import { RealEstateCategory } from 'types/portalModal';
import { getUseSectionsArgs } from '../PortalEntityDetail/utils';
import { FeatureSection } from './DetailSections';
import { MediaSection } from './MediaSection';

type TitleProps = {
    category: RealEstateCategory;
    realEstate: DetailPropertyType;
};

const getRealEstateReference: (x: TitleProps) => string | null | undefined = ({
    category,
    realEstate,
}) => {
    switch (category) {
        case 'property':
            return realEstate.properties?.[0]?.reference;
        default:
            return realEstate.code;
    }
};

const getRealEstateTitle: (x: TitleProps) => string | null = ({
    category,
    realEstate,
}) => {
    switch (category) {
        case 'property':
            return `${
                realEstate.properties?.[0]?.subTypology
                    ? realEstate.properties[0].subTypology
                    : realEstate?.typology
            } ${trans('label.in')} ${realEstate.contract}`;
        case 'auction':
        case 'newConstruction':
            return realEstate.subTypology;
        default:
            return '';
    }
};

const HeadSection: React.FC<TitleProps> = ({ category, realEstate }) => {
    const property = realEstate?.properties ? realEstate.properties[0] : null;
    const reference = getRealEstateReference({ category, realEstate });
    return (
        <div className="propertyDetail__head">
            <div>
                <h1 className="gx-title-1 propertyDetail__headTitle">
                    {`${
                        reference
                            ? `${trans(
                                  'label.reference_short'
                              )} ${reference} • `
                            : ''
                    }${getRealEstateTitle({ category, realEstate }) ?? ''}`}
                </h1>
                <span className="gx-title-2">{`${trans('label.ad_id')}: ${
                    realEstate.id
                }`}</span>
                <h2 className="gx-body-small propertyDetail__headSubtitle">
                    {property?.address ? `${property.address.trim()} • ` : null}
                    {property?.city}{' '}
                    {property?.province ? `(${property.province})` : null}
                </h2>
            </div>
            <span className="gx-title-1 propertyDetail__headPrice">
                {realEstate.price}
            </span>
        </div>
    );
};

const getRealEstateSections = ({ categoryId, category, typologyId }) => {
    switch (category) {
        case 'property':
            if (typologyId && SECTIONS_BY_TYPOLOGY_ID.includes(typologyId)) {
                return PROPERTY_SECTIONS_DATA.filter(
                    (section) => section?.typologyId?.includes(typologyId)
                );
            }
            return PROPERTY_SECTIONS_DATA.filter(
                (section) => section.categoryId?.includes(categoryId)
            );
        case 'newConstruction':
            return NEW_CONSTRUCTION_SECTION_DATA;
        case 'auction':
            return AUCTION_SECTIONS_DATA;
        default:
            return [];
    }
};

type DetailContentProps = {
    propertyData?: DetailPropertyType;
    print?: boolean;
    category: RealEstateCategory;
    onTabChange: RealEstateDetailProps['onTabChange'];
};

export const DetailContent: React.FC<DetailContentProps> = ({
    propertyData,
    print = false,
    category,
    onTabChange,
}) => {
    const [categoryId, typologyId] = getUseSectionsArgs({
        adCategory: category,
        propertyData,
    });
    const sections = useMemo(
        () =>
            getRealEstateSections({
                categoryId,
                category,
                typologyId,
            }),
        [category, categoryId, typologyId]
    );

    return propertyData ? (
        <div
            className={clsx([
                'propertyDetail',
                { 'print-property-detail': print },
            ])}
        >
            <MediaSection
                realEstate={propertyData}
                onTabChange={onTabChange}
                category={category}
            />
            <HeadSection realEstate={propertyData} category={category} />
            {sections?.map((section) => (
                <FeatureSection
                    key={`section-${section.name}`}
                    realEstate={propertyData}
                    category={category}
                    {...section}
                />
            ))}
        </div>
    ) : null;
};
