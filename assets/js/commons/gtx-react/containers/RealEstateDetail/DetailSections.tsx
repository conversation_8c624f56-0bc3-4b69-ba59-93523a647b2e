import gtxConstants from '@getrix/common/js/gtx-constants';
import { Alert } from '@gx-design/alert';
import { Badge } from '@gx-design/badge';
import { Button } from '@gx-design/button';
import { Icon, IconProps } from '@gx-design/icon';
import { Tabs } from '@gx-design/tabs';
import { useMediaMatch } from '@gx-design/use-media-match';
import { trans } from '@pepita-i18n/babelfish';
import clsx from 'clsx';
import { FC, useState } from 'react';

import { SKY_VISIBILITY_NAME } from 'constants/propertyVisibilities';
import { SectionData, SectionProps } from 'constants/realEstateSectionsData';
import {
    FLAG_ADDRESS_MAP_MODE_MAPPING,
    FLAG_PIN_TRANSLATIONS_MAP,
    StaticImgMap,
} from 'gtx-react/components/StaticImgMap';
import { SwiperCarousel } from 'gtx-react/components/SwiperCarousel';
import { checkAndOverrideFlagIcon } from 'gtx-react/utils/checkAndOverrideFlagIcon';
import { generateRealEstateEditUrl } from 'lib/property';
import {
    DetailPropertyType,
    PropertyResponse,
} from 'lib/REST/types/detail-property';
import { LanguageMapEntry } from 'types/languagesMap';
import { RealEstateCategory } from 'types/portalModal';
import { VOID_VALUE } from './constants';
import { FeatureItem, generateItemProps } from './FeatureItem';
import {
    formatRange,
    formatWorkspaceFees,
    generateSurfaceIcon,
    getRealEstateVisibilities,
} from './utils';

type SectionBodyProps = {
    realEstate: DetailPropertyType;
    category: RealEstateCategory;
} & Pick<SectionData, 'name' | 'data'>;

// TODO: Ask about energyClass.customIpe
const EnergeticSection: FC<SectionBodyProps> = ({
    realEstate,
    data,
    category,
}) => {
    const energyClass =
        category === 'newConstruction'
            ? realEstate.project?.energyClass
            : realEstate.properties?.[0]?.energyClass;

    if (!energyClass || energyClass?.energyPerformanceNotAvailable) {
        return (
            <div className="gx-body-small">
                <span>
                    {energyClass?.ipeWaiting ||
                        energyClass?.ipeExempt ||
                        VOID_VALUE}
                </span>
            </div>
        );
    }

    return (
        <div className="feature-section__list">
            {data?.map((fieldId) => (
                <FeatureItem
                    key={`featureItem-${fieldId}`}
                    {...generateItemProps(fieldId, realEstate)}
                />
            ))}
        </div>
    );
};

const DescriptionSection: FC<SectionBodyProps> = ({ realEstate, category }) => {
    const [activeTab, setActiveTab] = useState<number>(0);
    const onTabClick = (idx: number) => () => setActiveTab(idx);
    // exception for NewConstructions: descriptions to use (for main ad) are into another field
    const descriptions =
        category === 'newConstruction'
            ? realEstate.project?.descriptions
            : realEstate.descriptions;

    if (!descriptions || !descriptions?.length) {
        return (
            <Alert style="warning">{trans('alert.no_ad_description')}</Alert>
        );
    }

    // This data is always present in the session store, it is set in app-shell.js
    const languagesMap: LanguageMapEntry[] = JSON.parse(
        sessionStorage.getItem(gtxConstants('LANGUAGES_MAP_LOOKUP_KEY')) || '[]'
    );

    // But if an error occurs during the get call, add an alert.
    if (!languagesMap || !languagesMap.length) {
        return <Alert style="warning">{trans('common.generic_error')}</Alert>;
    }

    const newList = descriptions
        .map((elem, idx) => {
            const foundMap = languagesMap.find(
                (mapElem) => mapElem.locale === elem.locale
            );
            if (!foundMap) {
                console.warn(
                    `Found a description language not available into languages map: ${elem.locale}`
                );
                return null;
            }
            const lang = foundMap.lang;
            return {
                text: trans(`label.${lang}`),
                startElement: (
                    <div
                        className={`flag-icon flag-icon-${checkAndOverrideFlagIcon(
                            lang
                        )}`}
                    />
                ),
                active: idx === activeTab,
                onClick: onTabClick(idx),
            };
        })
        .filter((elem) => !!elem);

    return (
        <>
            <Tabs>
                <SwiperCarousel variant="tabs" items={newList} showFade />
            </Tabs>
            <div className="property-description">
                <div className="gx-title-2 property-description__head">
                    {descriptions[activeTab]?.title}
                </div>
                <div className="gx-title-2 property-description__text">
                    {descriptions[activeTab]?.description}
                </div>
            </div>
        </>
    );
};

const ConsistenciesSection: FC<SectionBodyProps> = ({ realEstate }) => {
    if (!realEstate?.properties?.[0]?.consistences?.length) {
        return <Alert style="warning">{trans('surface.error_1')}</Alert>;
    }

    const items = realEstate?.properties[0]?.consistences?.map((elem) => ({
        icon: generateSurfaceIcon(elem.id),
        title: elem.name,
        fields: [
            {
                label: trans('label.floor'),
                value: elem.floor,
            },
            {
                label: trans('label.surface'),
                value: elem.surfaceLabel,
            },
            {
                label: trans('label.counting_at'),
                value: elem.weight,
            },
            {
                label: trans('label.surface_type'),
                value: elem.type,
            },
            {
                label: trans('label.commercial'),
                value: elem.commercialSurfaceLabel,
            },
        ],
    }));
    return <SwiperCarousel variant="cards" items={items} showFade />;
};

const SalesDetailSection: FC<SectionBodyProps> = ({ realEstate }) => {
    if (!realEstate?.auction?.sales?.length) {
        return <Alert style="warning">{trans('label.no_details')}</Alert>;
    }

    const items = realEstate?.auction?.sales?.map((elem) => ({
        fields: [
            {
                label: trans('label.date'),
                value: elem.date,
            },
            {
                label: trans('label.type'),
                value: elem.type,
            },
            {
                label: trans('label.modality'),
                value: elem.mode,
            },
            {
                label: trans('label.base'),
                value: elem.basePrice,
            },
            {
                label: trans('label.status'),
                value: elem.status,
            },
        ],
    }));
    return <SwiperCarousel variant="cards" items={items} showFade />;
};

const MapSection: FC<SectionBodyProps> = ({ realEstate, data }) => {
    const geo = realEstate.properties?.[0]?.geographyInformation;
    const isLargeDesktop = useMediaMatch('largeDesktop');
    const mode = geo ? FLAG_ADDRESS_MAP_MODE_MAPPING[geo?.flagAddress] : null;

    return !geo ? (
        <Alert style="warning">
            <span
                dangerouslySetInnerHTML={{
                    __html: trans('map.popover.not_visible'),
                }}
            />
        </Alert>
    ) : (
        <div>
            <div className="propertyDetail__map">
                <div className="feature-section__list">
                    {data?.map((fieldId) => (
                        <FeatureItem
                            key={`featureItem-${fieldId}`}
                            {...generateItemProps(fieldId, realEstate)}
                        />
                    ))}
                </div>
                {geo?.coordinates && mode ? (
                    // TODO: understand why there are typescript problems here
                    <StaticImgMap
                        {...geo?.coordinates}
                        width={isLargeDesktop ? 470 : 736}
                        height={isLargeDesktop ? 280 : 438}
                        zoom={isLargeDesktop ? 15 : 16}
                        mode={mode}
                    />
                ) : (
                    <div className="propertyDetail__map__noMap">
                        <Icon name="image-off" />
                        <span>{trans('maps.address_not_localizable')}</span>
                    </div>
                )}
            </div>
            <div className="feature-section__list">
                {!geo.showAddress && (
                    <FeatureItem
                        label={trans('label.show_address')}
                        value={trans('insert_map.no_show_address_in_ad')}
                        isTwoColumn
                    />
                )}
                {typeof geo.flagAddress === 'number' && (
                    <FeatureItem
                        label={trans('label.geolocation')}
                        value={trans(
                            FLAG_PIN_TRANSLATIONS_MAP[geo.flagAddress]
                        )}
                        isTwoColumn
                    />
                )}
            </div>
        </div>
    );
};

const VisibilitySection: FC<SectionBodyProps> = ({ realEstate }) => {
    const { isAgencyPage, areAllExtraVisibilitiesActive, visibilities } =
        getRealEstateVisibilities(realEstate);

    return (
        <div className="feature-section__list">
            <FeatureItem
                label={trans('label.agency_page')}
                value={trans(isAgencyPage ? 'label.yes' : 'label.no')}
            />
            <FeatureItem
                label={trans('label.visibility')}
                value={
                    areAllExtraVisibilitiesActive ? (
                        <Badge
                            text={SKY_VISIBILITY_NAME}
                            className="visibility-sky"
                        />
                    ) : visibilities.length ? (
                        visibilities.map(({ icon, label, value }) => (
                            <Badge
                                key={`visibility-${value}`}
                                className={`visibility-${icon}`}
                                text={label}
                            />
                        ))
                    ) : (
                        VOID_VALUE
                    )
                }
            />
        </div>
    );
};

const RealEstateSimpleCard: FC<{ data: PropertyResponse }> = ({ data }) => {
    const iconValues: Partial<
        Record<IconProps['name'], string | number | null>
    > = {
        planimetry: data.composition?.rooms,
        size: data.surface,
        stairs: data.features?.floor,
    };
    return (
        <Card
            imageUrl={data.images?.[0]?.thumbUrl}
            firstRow={`${trans('label.reference_short')} ${
                data.reference || data.id
            }`}
            secondRow={data.price}
            thirdRow={data.typology}
            fourthRow={Object.entries(iconValues).map(([icon, value]) => ({
                icon: icon as IconProps['name'],
                value,
            }))}
        />
    );
};

type CardProps = {
    imageUrl?: string | null;
    firstRow?: string;
    secondRow?: string | null;
    thirdRow?: string | null;
    fourthRow?: Array<{
        icon: IconProps['name'];
        value?: string | number | null;
    }>;
};

const Card: FC<CardProps> = (props) => (
    <div className="related-properties__item">
        <div className="related-properties__itemImage">
            {props.imageUrl ? (
                <img src={props.imageUrl} />
            ) : (
                <Icon name="image-off" />
            )}
        </div>
        <div className="related-properties__itemContent">
            {props.firstRow && (
                <h4 className="gx-display-subtitle">{props.firstRow}</h4>
            )}
            {props.secondRow && (
                <div className="gx-display-subtitle">{props.secondRow}</div>
            )}
            {props.thirdRow && (
                <div className="gx-title-2">{props.thirdRow}</div>
            )}
            {props.fourthRow?.length && (
                <div className="related-properties__itemIcon">
                    {props.fourthRow.map(
                        ({ icon, value }, idx) =>
                            value && (
                                <div key={`related-val-${idx}`}>
                                    <Icon name={icon} />
                                    <span className="gx-body-small">
                                        {value}
                                    </span>
                                </div>
                            )
                    )}
                </div>
            )}
        </div>
    </div>
);

const RelatedRealEstateSection: FC<SectionBodyProps> = ({ realEstate }) => {
    const [shouldSeeAll, setShouldSeeAll] = useState(false);
    const onBtnClick = () => setShouldSeeAll(true);
    const list = realEstate.properties;
    const shortList = realEstate.properties?.slice(0, 3);

    if (!list || !list.length || !shortList) {
        return <Alert style="warning">{trans('label.no_properties')}</Alert>;
    }

    return (
        <div className="propertyDetail-card propertyDetail-card--noPadding">
            <div className="related-properties">
                {(shouldSeeAll ? [...list] : [...shortList]).map((elem) => (
                    <RealEstateSimpleCard
                        key={`related-${elem.id}`}
                        data={elem}
                    />
                ))}
            </div>
            {!shouldSeeAll && list.length > 3 && (
                <div className="related-properties__cta">
                    <Button onClick={onBtnClick}>{`${trans(
                        'label.show_all_2'
                    )} (${list.length})`}</Button>
                </div>
            )}
        </div>
    );
};

const WorkSpacesSection: FC<SectionBodyProps> = ({ realEstate }) => {
    const [shouldSeeAll, setShouldSeeAll] = useState(false);
    const onBtnClick = () => setShouldSeeAll(true);
    // const list = Object.values(realEstate.properties?.[0]?.portions);
    const list = realEstate.properties?.[0]?.portions;
    const shortList = list?.slice(0, 3);

    if (!list || !list.length || !shortList) {
        return (
            <Alert style="warning">{trans('workstation.error.required')}</Alert>
        );
    }

    return (
        <div className="propertyDetail-card propertyDetail-card--noPadding">
            <div className="related-properties">
                {(shouldSeeAll ? [...list] : [...shortList]).map(
                    ({ images, workspace }, i) => {
                        return (
                            <Card
                                key={`workstation-${i}`}
                                imageUrl={images?.[0]?.thumbUrl ?? undefined}
                                secondRow={formatWorkspaceFees(workspace)}
                                thirdRow={`${workspace.qty ?? ''} ${
                                    workspace.type ?? ''
                                }`}
                                fourthRow={[
                                    {
                                        icon: 'person',
                                        value: formatRange({
                                            from: workspace.capacityFrom,
                                            to: workspace.capacityTo,
                                        }),
                                    },
                                ]}
                            />
                        );
                    }
                )}
            </div>
            {!shouldSeeAll && list?.length > 3 && (
                <div className="related-properties__cta">
                    <Button onClick={onBtnClick}>{`${trans(
                        'label.show_all_2'
                    )} (${list.length})`}</Button>
                </div>
            )}
        </div>
    );
};

const NoteSection: FC<SectionBodyProps> = ({ realEstate }) =>
    realEstate.agencyNotes ? (
        <div className="propertyDetail__note">
            <Icon name="write" />
            <span>{realEstate.agencyNotes}</span>
        </div>
    ) : (
        <span>{trans('alert.no_ad_notes')}</span>
    );

const OpeningHoursSection: FC<SectionBodyProps> = ({ realEstate }) =>
    realEstate.properties?.[0]?.coWorkingTimetable?.length ? (
        <div className="feature-section__list">
            {realEstate.properties[0].coWorkingTimetable.map((day, i) => (
                <FeatureItem
                    key={`openingHour-${i}`}
                    label={day.dayOfWeek}
                    value={
                        day.closed
                            ? trans('label.closed')
                            : `${day.openTime?.slice(
                                  0,
                                  5
                              )} - ${day.closeTime?.slice(0, 5)}`
                    }
                />
            ))}
        </div>
    ) : (
        <Alert style="warning">
            {trans('user.exception.EDIT_PASSWORD_MISSING_FIELD')}
        </Alert>
    );

export const FeatureSection: FC<SectionProps> = ({
    title,
    realEstate,
    className,
    ...rest
}) => {
    const href = rest.step
        ? generateRealEstateEditUrl({ realEstate, ...rest })
        : false;
    return (
        <div className={clsx('feature-section', className)}>
            <div className="feature-section__head">
                <h3 className="gx-title-1">{title}</h3>
                {href && (
                    <Button
                        as="a"
                        href={href}
                        target="_blank"
                        size="small"
                        variant="ghost"
                    >
                        <Icon name="pencil" />
                        <span>{trans('label.edit')}</span>
                    </Button>
                )}
            </div>
            <SectionBody realEstate={realEstate} {...rest} />
        </div>
    );
};

export const SectionBody: FC<SectionBodyProps> = (props) => {
    switch (props.name) {
        case 'consistencies':
            return <ConsistenciesSection {...props} />;
        case 'energeticEfficiency':
            return <EnergeticSection {...props} />;
        case 'descriptions':
            return <DescriptionSection {...props} />;
        case 'map':
            return <MapSection {...props} />;
        case 'visibilities':
            return <VisibilitySection {...props} />;
        case 'relatedRealEstates':
            return <RelatedRealEstateSection {...props} />;
        case 'salesDetails':
            return <SalesDetailSection {...props} />;
        case 'note':
            return <NoteSection {...props} />;
        case 'openingHours':
            return <OpeningHoursSection {...props} />;
        case 'workstations':
            return <WorkSpacesSection {...props} />;
        default:
            return (
                <div className="feature-section__list">
                    {props.data?.map((fieldId) => (
                        <FeatureItem
                            key={`featureItem-${fieldId}`}
                            {...generateItemProps(fieldId, props.realEstate)}
                        />
                    ))}
                </div>
            );
    }
};
