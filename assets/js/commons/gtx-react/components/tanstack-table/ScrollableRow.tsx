import { Icon } from '@gx-design/icon';
import React, { useEffect, useRef } from 'react';
import { Navigation } from 'swiper/modules';
import { Swiper, SwiperClass, SwiperProps, SwiperSlide } from 'swiper/react';

/** Navigation mechanism */
const NAVIGATION: SwiperProps['navigation'] = {
    nextEl: '.swiper-carousel__button-next',
    prevEl: '.swiper-carousel__button-prev',
    disabledClass: 'disabled',
};

type NavButtonProps = {
    type: 'prev' | 'next';
};
const NavButton: React.FC<NavButtonProps> = ({ type }) => (
    <div
        className={`swiper-carousel__button-container swiper-carousel__button-container_${type} fadeMargin`}
    >
        <button className={`swiper-carousel__button-${type}`}>
            <Icon name={`arrow-${type === 'prev' ? 'left' : 'right'}`} />
        </button>
    </div>
);

type ScrollableRowProps = {
    children?: React.ReactNode;
    wrapperClassname?: string;
};

export const ScrollableRow: React.FC<ScrollableRowProps> = ({
    children,
    wrapperClassname = '',
}) => {
    const [swiper, setSwiper] = React.useState<SwiperClass | null>(null);

    useEffect(() => {
        const wasEnd = swiper?.isEnd;

        if (swiper) {
            swiper.update();
        }

        if (swiper && wasEnd) {
            swiper.slideTo(swiper.slides.length - 1, 0);
        }
    }, [children, swiper]);

    return (
        <Swiper
            className="swiper-carousel"
            modules={[Navigation]}
            slidesPerView="auto"
            spaceBetween={8}
            navigation={NAVIGATION}
            wrapperClass={`swiper-carousel__slideWrapper ${wrapperClassname}`}
            simulateTouch={false}
            onSwiper={setSwiper}
        >
            {React.Children.map(children, (child, index) => {
                if (!React.isValidElement(child)) {
                    return null;
                }

                return (
                    <SwiperSlide
                        className="swiper-carousel__slide"
                        key={`swiper-carousel-slide-${index}`}
                    >
                        {child}
                    </SwiperSlide>
                );
            })}
            <NavButton type="prev" />
            <NavButton type="next" />
        </Swiper>
    );
};
