import React, { Component } from 'react';
import PropTypes from 'prop-types';
import {
    setQueryString,
    deleteQueryString,
    isEmpty,
    areEqualShallowCompare,
    removeObjectProps,
} from '../../lib/utility';

export class QueryStringHandler extends Component {
    static defaultProps = {
        paramsToRemove: [],
    };

    static propTypes = {
        basePath: PropTypes.string.isRequired,
        params: PropTypes.object.isRequired,
        paramsToRemove: PropTypes.array,
    };

    componentDidUpdate(prevProps) {
        let oldParams = prevProps.params;
        let newParams = this.props.params;

        if (!areEqualShallowCompare(oldParams, newParams)) {
            if (isEmpty(newParams)) {
                deleteQueryString(this.props.basePath);
            } else {
                let qs = this.props.paramsToRemove
                    ? removeObjectProps(newParams, this.props.paramsToRemove)
                    : newParams;
                setQueryString(this.props.basePath, qs);
            }
        }
    }

    render() {
        return null;
    }
}
