import React, { Component } from 'react';
import PropTypes from 'prop-types';

export class GlobalHandler extends Component {
    static propTypes = {
        event: PropTypes.string.isRequired,
        on: PropTypes.oneOf(['window', 'document']),
        callback: PropTypes.func.isRequired,
        options: PropTypes.object,
    };

    observed = this.props.on === 'document' ? document : window;

    componentDidMount() {
        this.observed.addEventListener(this.props.event, this.props.callback, this.props.options);
    }

    componentWillUnmount() {
        this.observed.removeEventListener(
            this.props.event,
            this.props.callback,
            this.props.options
        );
    }

    render() {
        return this.props.children;
    }
}
