import React, { Component } from 'react';

export class VisibilitySwitch extends Component {
    state = {
        open: this.props.defaultOpen,
    };

    static getDerivedStateFromProps(props, state) {
        const prevProps = state.prevProps || {};

        if (!props.close || prevProps.close === props.close) {
            return {
                prevProps: props,
            };
        }

        return {
            prevProps: props,
            open: false,
        }
    }

    open = () => {
        this.setState({ open: true });
    };

    close = () => {
        this.setState({ open: false });
    };

    toggle = () => {
        this.setState(prevState => ({open: !prevState.open}));
    };

    render() {
        return this.props.children({ isOpen: this.state.open, open: this.open, close: this.close, toggle: this.toggle });
    }
}

