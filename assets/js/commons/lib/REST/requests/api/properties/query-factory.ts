import { queryOptions } from '@tanstack/react-query';
import {
    getBillboard,
    getQualityDetails,
    getProperties,
    getPerformancesByProperties,
    getStatsByProperties,
    getPropertyDetail,
    getCounters,
    getValidationErrors,
} from './properties';
import { RESTArgsOf } from 'lib/REST/types/shared';

const prefix = ['api', 'properties'] as const;

export const createBillboardQueryOptions = (args: RESTArgsOf<typeof getBillboard>) => {
    return queryOptions({
        queryKey: [...prefix, args.params.propertyId, 'billboard'] as const,
        queryFn: () => getBillboard(args),
    });
};

export const propertiesOptions = [...prefix, 'list'] as const;

export type CreateGetPropertiesOptionsType = RESTArgsOf<typeof getProperties>;

export const createGetPropertiesOptions = (args: CreateGetPropertiesOptionsType) => {
    return queryOptions({
        queryKey: [...propertiesOptions, args.params.status, args.query] as const,
        // Workaround: decorate response with status, for convenience
        queryFn: async () => {
            const response = await getProperties(args);

            return {
                ...response,
                filterStatus: args.params.status,
            };
        },
    });
};

export const createGetQualityDetailsOptions = (args: RESTArgsOf<typeof getQualityDetails>) => {
    return queryOptions({
        queryKey: [...prefix, 'quality-details', args.params.propertyId] as const,
        queryFn: () => getQualityDetails(args),
    });
};

export const createGetPerformancesByPropertiesOptions = (args: RESTArgsOf<typeof getPerformancesByProperties>) => {
    return queryOptions({
        queryKey: [...prefix, 'performance', args.query] as const,
        queryFn: () => getPerformancesByProperties(args),
    });
};

export const createGetStatsByPropertiesOptions = (args: RESTArgsOf<typeof getStatsByProperties>) => {
    return queryOptions({
        queryKey: [...prefix, 'stats', args.query] as const,
        queryFn: () => getStatsByProperties(args),
    });
};

export const createGetPropertyDetailOptions = (args: RESTArgsOf<typeof getPropertyDetail>) => {
    return queryOptions({
        queryKey: [...prefix, 'detail', args.params.id] as const,
        queryFn: () => getPropertyDetail(args),
    });
};

export const getCountersOptions = queryOptions({
    queryKey: [...prefix, 'counters'] as const,
    queryFn: getCounters,
});

export const getValidationErrorsOptions = queryOptions({
    queryKey: [...prefix, 'validation-errors'] as const,
    queryFn: getValidationErrors,
});
