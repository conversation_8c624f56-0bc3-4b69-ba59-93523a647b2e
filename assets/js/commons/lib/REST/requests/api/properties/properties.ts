import { validateT<PERSON><PERSON><PERSON> } from 'lib/REST/helpers/utils';
import { RESTArgs } from 'lib/REST/types/shared';
import { BillboardType, PropertyType, EditorOptionsType } from 'lib/REST/types/billboard';
import { SAVE_BILLBOARD_PATH } from '../../../../../../base/pages/property-billboard/web-api/endpoints';
import { withFormData, withFormUrlEncodedData, withSearchParams } from 'lib/api';
import { ListProperties, ListPropertiesFilter } from './types/list';
import { QualityDetails } from './types/quality';
import { PropertyPerformanceList } from './types/performance';
import { StatsList } from './types/stats';
import { IPortalPropertiesListItem } from 'types/api/property';
import { DetailPropertyType } from 'lib/REST/types/detail-property';
import { PropertiesDeletion } from '../../../types/operation';
import { ListPropertiesCounters } from './types/counters';

getBillboard.endpoint = '/api/properties/:propertyId/billboard' as const;

export async function getBillboard(args: RESTArgs<{ params: { propertyId: string | number } }>) {
    return validateToJson<{ options: EditorOptionsType; property: PropertyType; billboard: BillboardType }>(
        fetch(getBillboard.endpoint.replace(':propertyId', `${args.params.propertyId}`))
    );
}

export async function saveBillboard({
    propertyId,
    billboard,
}: {
    propertyId: string;
    billboard: BillboardType;
}): Promise<BillboardType> {
    return validateToJson<BillboardType>(
        fetch(SAVE_BILLBOARD_PATH.replace(':propertyId', propertyId), {
            method: 'POST',
            body: withFormData(billboard),
        })
    );
}

getQualityDetails.endpoint = '/api/properties/:propertyId/quality-details' as const;

export async function getQualityDetails(args: RESTArgs<{ params: { propertyId: string } }>) {
    return validateToJson<QualityDetails>(
        fetch(getQualityDetails.endpoint.replace(':propertyId', args.params.propertyId))
    );
}

getProperties.endpoint = '/api/properties/list/:status' as const;

export async function getProperties(
    args: RESTArgs<{
        params: {
            status: 'active' | 'archived' | 'draft' | 'favourite' | 'sold';
        };
        query?: ListPropertiesFilter & {
            page?: string | number;
            results?: string | number;
            sort?: string;
        };

        // TODO: Add type for the response
    }>
) {
    return validateToJson<ListProperties>(
        fetch(withSearchParams(getProperties.endpoint.replace(':status', `${args.params.status}`), args.query))
    );
}

getPerformancesByProperties.endpoint = '/api/properties/performance' as const;

export async function getPerformancesByProperties(
    args: RESTArgs<{
        query: {
            propertyIds: Array<string> | Array<number>;
        };
    }>
) {
    return validateToJson<PropertyPerformanceList>(
        fetch(
            withSearchParams(getPerformancesByProperties.endpoint, args.query, {
                mode: 'php',
            })
        )
    );
}

getStatsByProperties.endpoint = '/api/properties/stats' as const;

export function getStatsByProperties(
    args: RESTArgs<{
        query: {
            propertyIds: string[] | number[];
            /** date in string format `yyyy-MM-dd` */
            start: string;
            /** date in string format `yyyy-MM-dd` */
            end: string;
            statTypes: string;
            details?: boolean;
        };
    }>
) {
    return validateToJson<StatsList>(
        fetch(withSearchParams(getStatsByProperties.endpoint, args.query, { mode: 'php' }))
    );
}

deleteProperties.endpoint = '/api/properties/delete' as const;

export function deleteProperties(
    args: RESTArgs<{
        query: {
            ids: string[] | number[];
        };
    }>
) {
    return validateToJson<PropertiesDeletion>(
        fetch(deleteProperties.endpoint, {
            method: 'PATCH',
            body: withFormUrlEncodedData(args.query),
        })
    );
}

convertToSold.endpoint = `/api/properties/:id/convert-to-sold` as const;

export async function convertToSold(args: RESTArgs<{ params: { id: string | number } }>) {
    const response = await fetch(convertToSold.endpoint.replace(':id', args.params.id.toString()), { method: 'PATCH' });

    if (response.status === 422) {
        return {
            status: 'error',
            data: 'REQUIRE_CONFIRMATION',
        } as const;
    }

    if (!response.ok) {
        return {
            status: 'error',
            data: 'GENERIC_ERROR',
        } as const;
    }

    return {
        status: 'success',
        data: null,
    } as const;
}

convertToRent.endpoint = `/api/properties/:id/convert-to-rented` as const;

export async function convertToRent(args: RESTArgs<{ params: { id: string | number } }>) {
    const response = await fetch(convertToRent.endpoint.replace(':id', args.params.id.toString()), { method: 'PATCH' });

    if (response.status === 422) {
        return {
            status: 'error',
            data: 'REQUIRE_CONFIRMATION',
        } as const;
    }

    if (!response.ok) {
        return {
            status: 'error',
            data: 'GENERIC_ERROR',
        } as const;
    }

    return {
        status: 'success',
        data: null,
    } as const;
}

changePropertyVisibility.endpoint = '/api/properties/:id/visibilities' as const;

export function changePropertyVisibility(
    args: RESTArgs<{
        params: {
            id: string | number;
        };
        query: {
            status: boolean;
        };
    }>
) {
    return validateToJson<boolean>(
        fetch(changePropertyVisibility.endpoint.replace(':id', args.params.id.toString()), {
            method: 'PUT',
            body: withFormUrlEncodedData(args.query),
        })
    );
}

setFavouriteStatus.endpoint = '/api/properties/:id/favourite';
export function setFavouriteStatus(
    args: RESTArgs<{
        params: {
            id: string | number;
        };
        query: {
            status: boolean;
        };
    }>
) {
    return validateToJson<boolean>(
        fetch(setFavouriteStatus.endpoint.replace(':id', `${args.params.id}`), {
            method: 'PATCH',
            body: withFormUrlEncodedData({ status: args.query.status }),
        })
    );
}

changePropertyIntegrationFlag.endpoint = '/api/properties/:id/integration-flag' as const;

export function changePropertyIntegrationFlag(
    args: RESTArgs<{
        params: {
            id: string | number;
        };
        query: {
            status: boolean;
        };
    }>
) {
    return validateToJson<boolean>(
        fetch(changePropertyIntegrationFlag.endpoint.replace(':id', args.params.id.toString()), {
            method: 'PATCH',
            body: withFormUrlEncodedData({ status: args.query.status }),
        })
    );
}

getPropertyDetail.endpoint = '/api/properties/:id' as const;

export function getPropertyDetail(
    args: RESTArgs<{
        params: {
            id: IPortalPropertiesListItem['id'];
        };
    }>
) {
    return validateToJson<DetailPropertyType>(
        fetch(getPropertyDetail.endpoint.replace(':id', args.params.id), {
            method: 'GET',
        })
    );
}
getCounters.endpoint = '/api/properties/counters' as const;

export function getCounters() {
    return validateToJson<ListPropertiesCounters>(fetch(getCounters.endpoint));
}

getValidationErrors.endpoint = '/api/properties/validation-errors' as const;

export function getValidationErrors() {
    return validateToJson<{ errors: Record<string, Record<string, string>>; defaults: Record<string, string> }>(
        fetch(getValidationErrors.endpoint)
    );
}
