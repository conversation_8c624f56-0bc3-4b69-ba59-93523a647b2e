import { RESTArgsOf } from 'lib/REST/types/shared';
import { queryOptions } from '@tanstack/react-query';
import { getRankingDetail } from './get-ranking-detail';
import { getDataChanges } from './get-data-changes';

const prefix = ['v2', 'lista-annunci'];

export const createRankingDetailQueryOptions = (args: RESTArgsOf<typeof getRankingDetail>) => {
    return queryOptions({
        queryKey: [...prefix, 'get-ranking-detail', args?.query],
        queryFn: () => getRankingDetail(args),
    });
};

export const createDataVariationQueryOptions = (args: RESTArgsOf<typeof getDataChanges>) => {
    return queryOptions({
        queryKey: [...prefix, 'get-data-changes', args?.query],
        queryFn: async () => {
            const data = await getDataChanges(args);

            if (!data.success) {
                throw new Error('Data changes request failed');
            }

            return data;
        },
    });
};
