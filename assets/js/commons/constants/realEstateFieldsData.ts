import { IconProps } from '@gx-design/icon';
import { trans } from '@pepita-i18n/babelfish';
import { CONTRACT, PARKING_TYPOLOGIES } from 'constants/property';

export type FieldData = Readonly<{
    /** Path containing the field value, relative to the Real Estate root object */
    path: Array<string | number>;
    /** Field's label */
    label: string;
    /** Optional field's icon */
    icon?: IconProps['name'];
    validFor?: Array<{
        path: FieldData['path'];
        value: Array<string | number | boolean>;
    }>;
    isTwoColumn?: boolean;
    component?: 'url' | 'energeticTag';
}>;

export const REAL_ESTATE_FIELDS_DATA = {
    // section 1: tipologia dell'immobile
    category: {
        label: trans('label.category'),
        path: ['category'],
        icon: 'building',
    },
    typology: {
        label: trans('label.typology'),
        path: ['typology'],
        icon: 'palaces',
    },
    subjectSaleRent: {
        label: trans('label.what_to_sell_or_rent'),
        path: ['properties', 0, 'shop', 'subjectSaleRent'],
        icon: 'certificate',
    },
    license: {
        label: trans('label.type_of_business'),
        path: ['properties', 0, 'shop', 'license'],
        icon: 'shop',
        validFor: [
            {
                path: ['properties', 0, 'shop', 'commercialActivity'],
                value: [true],
            },
        ],
    },
    // duplicate field, difference is only label, based on condition
    lastLicense: {
        label: trans('label.last_type_of_business'),
        path: ['properties', 0, 'shop', 'license'],
        icon: 'shop',
        validFor: [
            {
                path: ['properties', 0, 'shop', 'commercialActivity'],
                value: [false],
            },
        ],
    },
    subTypology: {
        // special field for room
        label: trans('label.typology'),
        path: ['subTypology'],
        icon: 'palaces',
    },
    propertyTypology: {
        label: trans('label.typology'),
        path: ['properties', 0, 'typology'],
        icon: 'palaces',
    },
    // section 2: contratto prezzo e costi di gestione
    contract: {
        label: trans('label.contract'),
        path: ['contract'],
        icon: 'certificate',
    },
    propertyOwnership: {
        label: trans('label.property_type'),
        path: ['properties', 0, 'propertyOwnership'],
        icon: 'palaces',
    },
    price: {
        label: trans('label.price'),
        path: ['price'],
        icon: 'money',
    },
    heatingFees: {
        label: trans('label.heating_fees'),
        path: ['properties', 0, 'composition', 'heatingCosts'],
        icon: 'money',
    },
    freeNow: {
        label: trans('label.free'),
        path: ['properties', 0, 'freeNow'],
        icon: 'lock',
    },
    rentToBuy: {
        label: trans('label.rent_with_redemption'),
        path: ['properties', 0, 'rentToBuy'],
        icon: 'clock',
        validFor: [
            {
                path: ['contractId'],
                value: [CONTRACT.SALE],
            },
        ],
    },
    incomeProducingProperty: {
        // previous label was `label.at_income`
        label: trans('label.income_property'),
        path: ['properties', 0, 'incomeProducingProperty'],
        icon: 'money',
        validFor: [
            {
                path: ['contractId'],
                value: [CONTRACT.SALE],
            },
            {
                path: ['properties', 0, 'shop', 'commercialActivity'],
                value: [false],
            },
        ],
    },
    rentContractType: {
        label: trans('label.contract_type'),
        path: ['properties', 0, 'rentContractType'],
        icon: 'certificate',
        validFor: [
            {
                path: ['contractId'],
                value: [CONTRACT.RENT],
            },
        ],
    },
    condominiumCosts: {
        label: trans('label.condominium_fees'),
        path: ['properties', 0, 'condominiumCosts'],
        icon: 'money',
    },
    availability: {
        // wrong label.available before
        label: trans('label.availability'),
        path: ['properties', 0, 'availability'],
        icon: 'calendar',
        validFor: [
            {
                path: ['properties', 0, 'freeNow'],
                value: [trans('label.no')],
            },
        ],
    },
    deposit: {
        label: trans('label.deposit'),
        path: ['properties', 0, 'deposit'],
        icon: 'money',
        validFor: [
            {
                path: ['contractId'],
                value: [CONTRACT.RENT],
            },
        ],
    },
    availableForStudents: {
        // HR exclusive field
        label: trans('label.available_for_students'),
        path: ['properties', 0, 'availableForStudents'],
        icon: 'desk',
        validFor: [
            {
                path: ['contractId'],
                value: [CONTRACT.RENT],
            },
        ],
    },
    // section 3: superfici
    surface: {
        label: trans('label.surface'),
        path: ['properties', 0, 'surface'],
        icon: 'size',
    },
    garageDoorWidth: {
        label: trans('label.garage_door_width'),
        path: ['properties', 0, 'garage', 'garageDoorWidth'],
        icon: 'garage',
        validFor: [
            {
                path: ['typologyId'],
                value: [PARKING_TYPOLOGIES.GARAGE],
            },
        ],
    },
    consistenceFloor: {
        // not mentioned before, useful for garage (before wrong 'floor' was used)
        label: trans('label.floor'),
        path: ['properties', 0, 'consistences', 0, 'floor'],
        icon: 'stairs',
    },
    buildableSurface: {
        label: trans('label.building_area'),
        path: ['properties', 0, 'land', 'buildableSurface'],
        icon: 'size',
    },
    buildableHeight: {
        label: trans('label.max_building_height'),
        path: ['properties', 0, 'land', 'buildableHeight'],
        icon: 'align-vertical-top',
    },
    // section 4: composizione dell'immobile
    bedRooms: {
        label: trans('label.bedrooms'),
        path: ['properties', 0, 'composition', 'bedRooms'],
        icon: 'bed',
    },
    otherRooms: {
        label: trans('label.other_rooms_2'),
        path: ['properties', 0, 'composition', 'otherRooms'],
        icon: 'planimetry',
    },
    rooms: {
        label: trans('label.total_rooms'),
        path: ['properties', 0, 'composition', 'rooms'],
        icon: 'planimetry',
    },
    bathrooms: {
        label: trans('label.bathrooms'),
        path: ['properties', 0, 'composition', 'bathrooms'],
        icon: 'bath',
    },
    kitchenType: {
        label: trans('label.kitchen_type'),
        path: ['properties', 0, 'composition', 'kitchenType'],
        icon: 'kitchen',
    },
    garden: {
        label: trans('label.garden'),
        path: ['properties', 0, 'composition', 'garden'],
        icon: 'tree',
    },
    garage: {
        label: trans('label.box_auto'),
        path: ['properties', 0, 'composition', 'garage'],
        icon: 'garage',
    },
    parkingSpaces: {
        label: trans('label.parking_spaces'),
        path: ['properties', 0, 'composition', 'parkingSpaces'],
        icon: 'car',
    },
    parkingSpacesInGarage: {
        label: trans('label.garage_parking_spaces'),
        path: ['properties', 0, 'composition', 'parkingSpacesInGarage'],
        icon: 'garage',
    },
    balconies: {
        label: trans('label.balcony_plural'),
        path: ['properties', 0, 'features', 'balconies'],
        icon: 'balcony',
    },
    terrace: {
        label: trans('label.terrace'),
        path: ['properties', 0, 'features', 'terraces'],
        icon: 'beach-umbrella',
    },
    fittedWardrobe: {
        label: trans('label.fitted_wardrobes'),
        path: ['properties', 0, 'composition', 'fittedWardrobe'],
        icon: 'wardrobe',
    },
    basement: {
        label: trans('label.cellar'),
        path: ['properties', 0, 'features', 'basement'],
        icon: 'shelf',
    },
    mansard: {
        label: trans('label.mansard'),
        path: ['properties', 0, 'composition', 'mansard'],
        icon: 'mansard',
    },
    tavern: {
        // missing from previous mapping
        label: trans('label.tavern'),
        path: ['properties', 0, 'composition', 'tavern'],
        icon: 'tavern',
    },
    furniture: {
        label: trans('label.furniture'),
        path: ['properties', 0, 'composition', 'furniture'],
        icon: 'couch-lamp',
    },
    externalFixtures: {
        label: trans('label.external_fixtures'),
        path: ['properties', 0, 'features', 'externalFixtures'],
        icon: 'window',
    },
    tvSystem: {
        label: trans('label.tv_system'),
        path: ['properties', 0, 'features', 'tvSystem'],
        icon: 'monitor',
    },
    conciergeService: {
        label: trans('label.concierge_service'),
        path: ['properties', 0, 'features', 'conciergeService'],
        icon: 'reception',
    },
    securityDoor: {
        label: trans('label.security_door'),
        path: ['properties', 0, 'composition', 'securityDoor'],
        icon: 'door-lock',
    },
    antiTheftDevice: {
        label: trans('label.alarm_system'),
        path: ['properties', 0, 'composition', 'antiTheftDevice'],
        icon: 'surveillance-camera',
    },
    electricGate: {
        label: trans('label.electric_gate'),
        path: ['properties', 0, 'composition', 'electricGate'],
        icon: 'gate-lightning',
    },
    videoDoorPhone: {
        label: trans('label.video_entryphone'),
        path: ['properties', 0, 'composition', 'videoDoorPhone'],
        icon: 'video-intercom',
    },
    opticalFiber: {
        label: trans('label.optic_fiber'),
        path: ['properties', 0, 'composition', 'opticalFiber'],
        icon: 'optical-fiber',
    },
    firePlace: {
        label: trans('label.fireplace_1'),
        path: ['properties', 0, 'composition', 'firePlace'],
        icon: 'fireplace',
    },
    hydroMassage: {
        label: trans('label.hydromassage'),
        path: ['properties', 0, 'composition', 'hydroMassage'],
        icon: 'hot-tub',
    },
    swimmingPool: {
        label: trans('label.pool'),
        path: ['properties', 0, 'features', 'swimmingPool'],
        icon: 'ladder',
    },
    tennisCourt: {
        label: trans('label.tennis_court'),
        path: ['properties', 0, 'composition', 'tennisCourt'],
        icon: 'tennis',
    },
    disabledPersonsBathroom: {
        label: trans('label.bathroom_for_disabled'),
        path: ['properties', 0, 'composition', 'disabledPersonsBathroom'],
        icon: 'disabled',
    },
    reception: {
        label: trans('label.reception'),
        path: ['properties', 0, 'composition', 'reception'],
        icon: 'reception',
    },
    motorcycleParkingSpaces: {
        label: trans('label.motocycle_spaces'),
        path: ['properties', 0, 'garage', 'motorcycleParkingSpaces'],
        icon: 'motorbike',
    },
    chargingStations: {
        label: trans('label.electric_car_charging'),
        path: ['properties', 0, 'garage', 'chargingStations'],
        icon: 'electric-charging',
    },
    cctv: {
        label: trans('label.cctv'),
        path: ['properties', 0, 'composition', 'cctv'],
        icon: 'surveillance-camera',
    },
    electricGarageDoor: {
        // missing in previous mapping
        label: trans('label.electric_garage_door'),
        path: ['properties', 0, 'garage', 'electricGate'],
        icon: 'garage',
        validFor: [
            {
                path: ['typologyId'],
                value: [PARKING_TYPOLOGIES.GARAGE],
            },
        ],
    },
    garageParkingSpaces: {
        // missing in previous mapping
        label: trans('label.car_spaces'),
        path: ['properties', 0, 'garage', 'parkingSpaces'],
        icon: 'car',
    },
    ceilingHeight: {
        label: trans('label.ceilings_height'),
        path: ['properties', 0, 'shop', 'ceilingHeight'],
        icon: 'align-vertical-top',
    },
    storefrontsCount: {
        label: trans('label.shop_windows_number'),
        path: ['properties', 0, 'shop', 'storefrontsCount'],
        icon: 'showcase',
    },
    storefrontsSurface: {
        label: trans('label.shop_windows_surface'),
        path: ['properties', 0, 'shop', 'storefrontsSurface'],
        icon: 'showcase',
    },
    orientations: {
        label: trans('label.orientation'),
        path: ['properties', 0, 'composition', 'orientations'],
        icon: 'compass',
    },
    vacuumSystem: {
        // "aspirazione fumi", missing in previous mapping
        label: trans('label.smoke_aspirations'),
        path: ['properties', 0, 'composition', 'vacuumSystem'],
        icon: 'smoke-extractor',
    },
    flue: {
        label: trans('label.flue'),
        path: ['properties', 0, 'shop', 'flue'],
        icon: 'smoke-extractor',
    },
    activityEquipment: {
        // missing into previous mapping
        label: trans('label.equipment'),
        path: ['properties', 0, 'shop', 'activityEquipment'],
        icon: 'gear',
        validFor: [
            {
                path: ['properties', 0, 'shop', 'commercialActivity'],
                value: [true],
            },
        ],
    },
    mechanicsVentilation: {
        label: trans('label.mechanical_ventilation'),
        path: ['properties', 0, 'composition', 'mechanicsVentilation'],
        icon: 'fan',
    },
    wired: {
        label: trans('label.wired'),
        path: ['properties', 0, 'composition', 'wired'],
        icon: 'optical-fiber',
    },
    floatingFloor: {
        label: trans('label.floating_floor'),
        path: ['properties', 0, 'composition', 'floatingFloor'],
        icon: 'parquet',
    },
    counterTop: {
        label: trans('label.countertop'),
        path: ['properties', 0, 'composition', 'counterTop'],
        icon: 'mansard',
    },
    accessControl: {
        label: trans('label.access_control'),
        path: ['properties', 0, 'composition', 'accessControl'],
        icon: 'badge',
    },
    manualShutter: {
        label: trans('label.manual_gate_valves'),
        path: ['properties', 0, 'composition', 'manualShutter'],
        icon: 'garage',
    },
    automaticShutter: {
        label: trans('label.automatic_gate_valves'),
        path: ['properties', 0, 'composition', 'automaticShutter'],
        icon: 'garage',
    },
    fireSensor: {
        label: trans('label.fire_detectors'),
        path: ['properties', 0, 'composition', 'fireSensor'],
        icon: 'smoke-alarm',
    },
    rainSensor: {
        label: trans('label.rain_fire'),
        path: ['properties', 0, 'composition', 'rainSensor'],
        icon: 'drop',
    },
    fireHydrant: {
        label: trans('label.fire_hydrants'),
        path: ['properties', 0, 'composition', 'fireHydrant'],
        icon: 'fire-hydrant',
    },
    fireBreakDoor: {
        label: trans('label.cut_fire_door'),
        path: ['properties', 0, 'composition', 'fireBreakDoor'],
        icon: 'door',
    },
    emergencyExit: {
        label: trans('label.emergency_exit'),
        path: ['properties', 0, 'composition', 'emergencyExit'],
        icon: 'exit',
    },
    emergencyLightings: {
        label: trans('label.emergency_lights'),
        path: ['properties', 0, 'composition', 'emergencyLightings'],
        icon: 'light-bulb',
    },
    driveway: {
        label: trans('label.driveway'),
        path: ['properties', 0, 'composition', 'driveway'],
        icon: 'block-sign',
    },
    warehouseHeight: {
        label: trans('label.warehouse_height'),
        path: ['properties', 0, 'composition', 'roomHeight'],
        icon: 'align-vertical-top',
    },
    enclosed: {
        label: trans('label.enclosed'),
        path: ['properties', 0, 'composition', 'enclosed'],
        icon: 'fence',
    },
    shedHeight: {
        label: trans('label.shed_height'),
        path: ['properties', 0, 'industrial', 'shedHeight'],
        icon: 'align-vertical-top',
    },
    subbeamHeight: {
        label: trans('label.underpass_height'),
        path: ['properties', 0, 'industrial', 'subbeamHeight'],
        icon: 'align-vertical-top',
    },
    pillars: {
        label: trans('label.shed_with_pillars'),
        path: ['properties', 0, 'industrial', 'pillars'],
        icon: 'shed',
    },
    loadingDocks: {
        label: trans('label.loading_docks'),
        path: ['properties', 0, 'industrial', 'loadingDocks'],
        icon: 'garage',
    },
    cranes: {
        label: trans('label.crane_plural'),
        path: ['properties', 0, 'industrial', 'cranes'],
        icon: 'hook',
    },
    loadCapacity: {
        label: trans('label.load_capacity'),
        path: ['properties', 0, 'industrial', 'loadCapacity'],
        icon: 'libra',
    },
    spans: {
        label: trans('label.span_plural'),
        path: ['properties', 0, 'industrial', 'spans'],
        icon: 'shed',
    },
    officeSurface: {
        label: trans('label.offices_surface'),
        path: ['properties', 0, 'industrial', 'officeSurface'],
        icon: 'size',
    },
    dressingRoom: {
        label: trans('label.locker_room'),
        path: ['properties', 0, 'composition', 'dressingRoom'],
        icon: 'locker',
    },
    shower: {
        label: trans('label.shower'),
        path: ['properties', 0, 'composition', 'shower'],
        icon: 'shower',
    },
    canteen: {
        label: trans('label.canteen'),
        path: ['properties', 0, 'composition', 'canteen'],
        icon: 'kitchen',
    },
    kitchen: {
        label: trans('label.kitchen'),
        path: ['properties', 0, 'composition', 'kitchen'],
        icon: 'kitchen',
    },
    terraces: {
        label: trans('label.terrace'),
        path: ['properties', 0, 'features', 'terraces'],
        icon: 'beach-umbrella',
    },
    bikeParking: {
        label: trans('label.bike_parking'),
        path: ['properties', 0, 'composition', 'bikeParking'],
        icon: 'bike',
    },
    warehouse: {
        label: trans('label.warehouse'),
        path: ['properties', 0, 'composition', 'warehouse'],
        icon: 'warehouse',
    },
    broadbandInternet: {
        label: trans('label.broadband_internet'),
        path: ['properties', 0, 'composition', 'broadBandInternet'],
        icon: 'world',
    },
    multifunctionPrinter: {
        label: trans('label.multifunction_printer'),
        path: ['properties', 0, 'composition', 'multifunctionPrinter'],
        icon: 'print',
    },
    hotDrinks: {
        label: trans('label.hot_drinks'),
        path: ['properties', 0, 'composition', 'hotDrinks'],
        icon: 'kettle',
    },
    convivialityRoom: {
        label: trans('label.conviviality_room'),
        path: ['properties', 0, 'composition', 'convivialityRoom'],
        icon: 'couch-lamp',
    },
    microwave: {
        label: trans('label.microwave'),
        path: ['properties', 0, 'composition', 'microwave'],
        icon: 'microwave',
    },
    cleaning: {
        label: trans('label.cleaning'),
        path: ['properties', 0, 'composition', 'cleaning'],
        icon: 'broom-bucket',
    },
    waitingRoom: {
        label: trans('label.waiting_room'),
        path: ['properties', 0, 'composition', 'waitingRoom'],
        icon: 'clock',
    },
    fridge: {
        label: trans('label.fridge'),
        path: ['properties', 0, 'composition', 'fridge'],
        icon: 'fridge',
    },
    collectionStorageService: {
        label: trans('label.parcel_collection_storage_service'),
        path: ['properties', 0, 'composition', 'collectionStorageService'],
        icon: 'shelf',
    },
    dishwasher: {
        label: trans('label.dishwasher'),
        path: ['properties', 0, 'composition', 'dishwasher'],
        icon: 'dishwasher',
    },
    phoneCallSpaces: {
        label: trans('label.spaces_for_talking_on_phone'),
        path: ['properties', 0, 'composition', 'phoneCallSpaces'],
        icon: 'phone',
    },
    domiciliation: {
        label: trans('label.domiciliation'),
        path: ['properties', 0, 'composition', 'domiciliation'],
        icon: 'marker',
    },
    badge: {
        label: trans('label.badge'),
        path: ['properties', 0, 'composition', 'badge'],
        icon: 'badge',
    },
    music: {
        label: trans('label.background_music'),
        path: ['properties', 0, 'composition', 'music'],
        icon: 'music-note',
    },
    tv: {
        label: trans('label.tv_screen'),
        path: ['properties', 0, 'composition', 'tv'],
        icon: 'monitor',
    },
    locker: {
        label: trans('label.locker'),
        path: ['properties', 0, 'composition', 'locker'],
        icon: 'locker',
    },
    newspapers: {
        label: trans('label.newspapers'),
        path: ['properties', 0, 'composition', 'newspapers'],
        icon: 'newspaper',
    },
    fruit: {
        label: trans('label.fruit'),
        path: ['properties', 0, 'composition', 'fruit'],
        icon: 'fruit',
    },
    coffeeBar: {
        label: trans('label.coffee_bar'),
        path: ['properties', 0, 'composition', 'coffeeBar'],
        icon: 'cup',
    },
    guardian: {
        label: trans('label.guardian'),
        path: ['properties', 0, 'composition', 'guardian'],
        icon: 'shield',
    },
    barber: {
        label: trans('label.barber'),
        path: ['properties', 0, 'composition', 'barber'],
        icon: 'scissors-comb',
    },
    asylum: {
        label: trans('label.asylum'),
        path: ['properties', 0, 'composition', 'asylum'],
        icon: 'rocking-horse',
    },
    fitnessRoom: {
        label: trans('label.fitness_room'),
        path: ['properties', 0, 'composition', 'fitnessRoom'],
        icon: 'dumbbells',
    },
    otherServices: {
        label: trans('label.other_services'),
        path: ['properties', 0, 'composition', 'otherServices'],
        icon: 'plus-circle',
    },
    landDeclivity: {
        label: trans('label.ground_difference_height'),
        path: ['properties', 0, 'land', 'landDeclivity'],
        icon: 'layers',
    },
    waterConnection: {
        label: trans('label.water_connection_available'),
        path: ['properties', 0, 'composition', 'waterConnection'],
        icon: 'drop',
    },
    gasConnection: {
        label: trans('label.gas_connection_available'),
        path: ['properties', 0, 'composition', 'gasConnection'],
        icon: 'fire',
    },
    electricityConnection: {
        label: trans('label.electricity_connection_available'),
        path: ['properties', 0, 'composition', 'electricityConnection'],
        icon: 'light-bulb',
    },
    sewersConnection: {
        label: trans('label.sewers_connection_available'),
        path: ['properties', 0, 'composition', 'sewersConnection'],
        icon: 'drop',
    },
    publicLighting: {
        label: trans('label.public_lighting'),
        path: ['properties', 0, 'composition', 'publicLighting'],
        icon: 'light-bulb',
    },
    accessType: {
        label: trans('label.street_access'),
        path: ['properties', 0, 'land', 'accessType'],
        icon: 'road',
    },
    landFeatures: {
        label: trans('label.land_property'),
        path: ['properties', 0, 'composition', 'landFeatures'],
        icon: 'tree',
    },
    // section 5: caratteristiche dell'immobile
    constructionYear: {
        label: trans('label.year_built'),
        path: ['properties', 0, 'features', 'constructionYear'],
        icon: 'calendar',
    },
    propertyClass: {
        label: trans('label.class_estate'),
        path: ['properties', 0, 'features', 'propertyClass'],
        icon: 'apartment',
    },
    propertyCondition: {
        label: trans('label.state'),
        path: ['properties', 0, 'features', 'propertyCondition'],
        icon: 'helmet',
    },
    floor: {
        label: trans('label.floor'),
        path: ['properties', 0, 'features', 'floor'],
        icon: 'stairs',
    },
    totalBuildingFloors: {
        label: trans('label.total_building_floors'),
        path: ['properties', 0, 'features', 'totalBuildingFloors'],
        icon: 'building',
    },
    disabledAccess: {
        label: trans('label.access_for_disabled'),
        path: ['properties', 0, 'composition', 'disabledAccess'],
        icon: 'disabled',
    },
    openSides: {
        label: trans('label.free_sides'),
        path: ['properties', 0, 'features', 'openSides'],
        icon: 'wall',
    },
    exposure: {
        label: trans('label.exposure'),
        path: ['properties', 0, 'features', 'overlooking'],
        icon: 'window',
    },
    heating: {
        label: trans('label.heating'),
        path: ['properties', 0, 'composition', 'heating'],
        icon: 'fire',
    },
    heatingType: {
        label: trans('label.heating_system_type'),
        path: ['properties', 0, 'composition', 'heatingType'],
        icon: 'fire',
    },
    heatingSupply: {
        label: trans('label.heating_supply'),
        path: ['properties', 0, 'composition', 'heatingSupply'],
        icon: 'fire',
    },
    airConditioning: {
        label: trans('label.air_conditioning_system'),
        path: ['properties', 0, 'composition', 'airConditioning'],
        icon: 'air-conditioning',
    },
    airConditioningType: {
        label: trans('label.air_conditioning_system_type'),
        path: ['properties', 0, 'composition', 'airConditioningType'],
        icon: 'air-conditioning',
    },
    elevators: {
        label: trans('label.lift_plural'),
        path: ['properties', 0, 'features', 'elevators'],
        icon: 'elevator',
    },
    buildingUsages: {
        label: trans('label.building_use'),
        path: ['properties', 0, 'features', 'buildingUsages'],
        icon: 'apartment',
    },
    urbanQualification: {
        // duplicate of `buildingUsages` with a different label, used by `buildableLand` typology
        label: trans('label.urban_qualification'),
        path: ['properties', 0, 'features', 'buildingUsages'],
        icon: 'apartment',
    },
    internalExposition: {
        label: trans('label.internal_exposition'),
        path: ['properties', 0, 'composition', 'internalExposition'],
        icon: 'wall',
    },
    externalExposition: {
        label: trans('label.external_exposition'),
        path: ['properties', 0, 'composition', 'externalExposition'],
        icon: 'wall',
    },
    garageType: {
        label: trans('label.garage_type'),
        path: ['properties', 0, 'garage', 'garageType'],
        icon: 'garage',
    },
    dayAccessibility: {
        // previous mapping was wrong
        label: trans('label.24_hour_accessibility'),
        path: ['properties', 0, 'composition', 'dayAccessibility'],
        icon: 'clock',
    },
    cornerPosition: {
        label: trans('label.corner_position'),
        path: ['properties', 0, 'composition', 'cornerPosition'],
        icon: 'expand',
    },
    doubleEntrance: {
        label: trans('label.double_entry'),
        path: ['properties', 0, 'composition', 'doubleEntrance'],
        icon: 'door',
    },
    yearsOfActivity: {
        label: trans('label.years_of_commercial_activity'),
        path: ['properties', 0, 'shop', 'yearsOfActivity'],
        icon: 'calendar',
        validFor: [
            {
                path: ['properties', 0, 'shop', 'commercialActivity'],
                value: [true],
            },
        ],
    },
    lastYearRevenue: {
        label: trans('label.last_year_revenue'),
        path: ['properties', 0, 'shop', 'revenue'],
        icon: 'money',
        validFor: [
            {
                path: ['properties', 0, 'shop', 'commercialActivity'],
                value: [true],
            },
        ],
    },
    interiorSubdivision: {
        // missing in previous mapping
        label: trans('label.interior_layout'),
        path: ['properties', 0, 'composition', 'interiorSubdivision'],
        icon: 'wall',
    },
    views: {
        // HR exclusive field
        label: trans('label.view'),
        path: ['properties', 0, 'features', 'views'],
        icon: 'image',
    },
    distanceFromSea: {
        // HR exclusive field
        label: trans('label.distance_from_the_sea'),
        path: ['properties', 0, 'features', 'distanceFromSea'],
        icon: 'beach-front',
    },
    solarWaterHeating: {
        // HR exclusive field
        label: trans('label.solar_water_heating'),
        path: ['properties', 0, 'composition', 'solarWaterHeating'],
        icon: 'solar-panel',
    },

    // rooms
    roomType: {
        // missing in previous mapping
        label: trans('label.room_typology'),
        path: ['properties', 0, 'sharedApartmentInformation', 'roomType'],
        icon: 'bed',
    },
    // room - caratteristiche immobile
    roomBalcony: {
        label: trans('label.balcony'),
        path: ['properties', 0, 'sharedApartmentInformation', 'balcony'],
        icon: 'balcony',
    },
    roomKitchen: {
        label: trans('label.kitchen'),
        path: ['properties', 0, 'sharedApartmentInformation', 'kitchen'],
        icon: 'kitchen',
    },
    roomGarden: {
        label: trans('label.garden'),
        path: ['properties', 0, 'sharedApartmentInformation', 'garden'],
        icon: 'tree',
    },
    roomGym: {
        label: trans('label.gym'),
        path: ['properties', 0, 'sharedApartmentInformation', 'gym'],
        icon: 'dumbbells',
    },
    roomSwimmingPool: {
        label: trans('label.pool'),
        path: ['properties', 0, 'sharedApartmentInformation', 'swimmingPool'],
        icon: 'ladder',
    },
    roomUtilityRoom: {
        label: trans('label.utility_room'),
        path: ['properties', 0, 'sharedApartmentInformation', 'utilityRoom'],
        icon: 'broom-bucket',
    },
    roomTerrace: {
        label: trans('label.terrace'),
        path: ['properties', 0, 'sharedApartmentInformation', 'terrace'],
        icon: 'beach-umbrella',
    },
    // property accessories (room)
    roomFireplace: {
        label: trans('label.fireplace'),
        path: ['properties', 0, 'sharedApartmentInformation', 'firePlace'],
        icon: 'fireplace',
    },
    roomTennisCourt: {
        label: trans('label.tennis_court'),
        path: ['properties', 0, 'sharedApartmentInformation', 'tennisCourt'],
        icon: 'tennis',
    },
    roomInternet: {
        label: trans('label.internet_connection'),
        path: ['properties', 0, 'sharedApartmentInformation', 'internet'],
        icon: 'world',
    },
    roomDishwasher: {
        label: trans('label.dishwasher'),
        path: ['properties', 0, 'sharedApartmentInformation', 'dishwasher'],
        icon: 'dishwasher',
    },
    roomWashingMachine: {
        label: trans('label.dishwasher'),
        path: ['properties', 0, 'sharedApartmentInformation', 'washingMachine'],
        icon: 'washing-machine',
    },
    roomMicrowave: {
        label: trans('label.microwave'),
        path: ['properties', 0, 'sharedApartmentInformation', 'microwave'],
        icon: 'microwave',
    },
    roomConciergeService: {
        label: trans('label.concierge_service'),
        path: ['properties', 0, 'sharedApartmentInformation', 'conciergeService'],
        icon: 'reception',
    },
    roomAntiTheftSystem: {
        label: trans('label.alarm_system'),
        path: ['properties', 0, 'sharedApartmentInformation', 'alarmSystem'],
        icon: 'surveillance-camera',
    },
    roomSatelliteTv: {
        label: trans('label.satellite_dish'),
        path: ['properties', 0, 'sharedApartmentInformation', 'satelliteTv'],
        icon: 'satellite-dish',
    },
    // room features
    roomAirConditioned: {
        label: trans('label.air_conditioned'),
        path: ['properties', 0, 'sharedApartmentInformation', 'airConditioned'],
        icon: 'air-conditioning',
    },
    roomWardrobe: {
        label: trans('label.wardrobes'),
        path: ['properties', 0, 'sharedApartmentInformation', 'wardrobe'],
        icon: 'wardrobe',
    },
    roomPhone: {
        label: trans('label.phone_connection'),
        path: ['properties', 0, 'sharedApartmentInformation', 'phone'],
        icon: 'phone',
    },
    roomPrivateBathroom: {
        label: trans('label.private_bathroom'),
        path: ['properties', 0, 'sharedApartmentInformation', 'privateBathRoom'],
        icon: 'bath',
    },
    roomSharedBathroom: {
        label: trans('label.shared_bathroom'),
        path: ['properties', 0, 'sharedApartmentInformation', 'sharedBathroom'],
        icon: 'bath',
    },
    roomLaundry: {
        label: trans('label.with_laundry'),
        path: ['properties', 0, 'sharedApartmentInformation', 'laundry'],
        icon: 'towel',
    },
    roomPrivateEntrance: {
        label: trans('label.private_entrance'),
        path: ['properties', 0, 'sharedApartmentInformation', 'privateEntrance'],
        icon: 'door',
    },
    roomMoquette: {
        label: trans('label.moquette'),
        path: ['properties', 0, 'sharedApartmentInformation', 'moquette'],
        icon: 'moquette',
    },
    roomParquet: {
        label: trans('label.parquet'),
        path: ['properties', 0, 'sharedApartmentInformation', 'parquet'],
        icon: 'parquet',
    },
    roomTv: {
        label: trans('label.tv'),
        path: ['properties', 0, 'sharedApartmentInformation', 'tv'],
        icon: 'monitor',
    },
    // section 7: Dati catastali
    cadastralSection: {
        label: trans('label.section'),
        path: ['properties', 0, 'cadastralData', 'section'],
    },
    cadastralSheet: {
        label: trans('label.sheet'),
        path: ['properties', 0, 'cadastralData', 'sheet'],
    },
    cadastralParcel: {
        label: trans('label.particle'),
        path: ['properties', 0, 'cadastralData', 'parcel'],
    },
    cadastralSubordinate: {
        label: trans('label.subaltern'),
        path: ['properties', 0, 'cadastralData', 'subordinate'],
    },
    cadastralCategory: {
        label: trans('label.cadastral_category'),
        path: ['properties', 0, 'cadastralData', 'cadastralClass', 'name'],
    },
    cadastralIncome: {
        label: trans('label.cadastral_income'),
        path: ['properties', 0, 'cadastralData', 'cadastralIncome'],
    },
    cadastralOwnershipShares: {
        label: trans('label.share_ownership'),
        path: ['properties', 0, 'cadastralData', 'ownershipShares'],
    },
    otherCadastralData: {
        label: trans('label.other_cadastral_data'),
        path: ['properties', 0, 'cadastralData', 'otherCadastralData'],
    },
    // Dati intermediazione
    agentName: {
        label: trans('label.agent'),
        path: ['brokerageData', 'agentName'],
    },
    collaboration: {
        label: trans('label.cooperation'),
        path: ['brokerageData', 'collaboration'],
    },
    mandateType: {
        label: trans('label.mandate_type'),
        path: ['brokerageData', 'mandateType'],
    },
    mandateExpireDate: {
        label: trans('label.mandate_expiry'),
        path: ['brokerageData', 'mandateExpireDate'],
    },
    reference: {
        label: trans('label.reference'),
        path: ['brokerageData', 'reference'],
    },
    ownerName: {
        label: trans('label.owner'),
        path: ['brokerageData', 'ownerName'],
    },
    workProgress: {
        // missing in previous mapping
        // only if propertyCondition = new/under construction
        label: trans('label.work_progress'),
        path: ['brokerageData', 'workProgress'],
        validFor: [
            {
                path: ['properties', 0, 'features', 'propertyCondition'],
                value: [trans('db_status.id_1')],
            },
        ],
    },
    buildProposer: {
        // missing in previous mapping
        // only if propertyCondition = new/under construction
        label: trans('label.proponent'),
        path: ['brokerageData', 'proposer'],
        validFor: [
            {
                // TODO: use ['properties', 0, 'propertyCondition', 'id'] to check against the id "1"
                path: ['properties', 0, 'features', 'propertyCondition'],
                value: [trans('db_status.id_1')],
            },
        ],
    },
    // Nuove costruzioni, Info principali
    projectName: {
        label: trans('label.project_name'),
        path: ['project', 'projectName'],
    },
    proposer: {
        label: trans('label.proponent'),
        path: ['project', 'proposer'],
    },
    residentialUnits: {
        label: trans('label.housing_units'),
        path: ['project', 'residentialUnits'],
    },
    commercialUnits: {
        label: trans('label.commercial_units'),
        path: ['project', 'commercialUnits'],
    },
    constructionStartDate: {
        label: trans('label.work_start'),
        path: ['project', 'constructionStartDate'],
    },
    expectedDeliveryDate: {
        label: trans('label.work_end'),
        path: ['project', 'expectedDeliveryDate'],
    },
    parkingSpacesNC: {
        label: trans('label.parking_spaces'),
        path: ['project', 'composition', 'parkingSpaces'],
    },
    parkingSpacesInGarageNC: {
        label: trans('label.garage_parking_spaces'),
        path: ['project', 'composition', 'parkingSpacesInGarage'],
    },
    workProgressNC: {
        label: trans('label.work_status'),
        path: ['project', 'workProgress'],
        isTwoColumn: true,
    },
    // Nuove costruzioni, caratteristiche dell'immobile
    projectHeating: {
        icon: 'fire',
        label: trans('label.heating'),
        path: ['project', 'composition', 'heating'],
    },
    projectHeatingType: {
        icon: 'fire',
        label: trans('label.heating_system_type'),
        path: ['project', 'composition', 'heatingType'],
    },
    projectHeatingSupply: {
        icon: 'fire',
        label: trans('label.heating_supply'),
        path: ['project', 'composition', 'heatingSupply'],
    },
    projectAirConditioning: {
        label: trans('label.air_conditioning_system'),
        path: ['project', 'composition', 'airConditioning'],
        icon: 'air-conditioning',
    },
    projectAirConditioningType: {
        label: trans('label.air_conditioning_system_type'),
        path: ['project', 'composition', 'airConditioningType'],
        icon: 'air-conditioning',
    },
    projectExternalFixtures: {
        label: trans('label.external_fixtures'),
        path: ['project', 'features', 'externalFixtures'],
        icon: 'window',
    },
    projectDisabledAccess: {
        label: trans('label.access_for_disabled'),
        path: ['project', 'composition', 'disabledAccess'],
        icon: 'disabled',
    },
    projectCommonGarden: {
        label: trans('label.common_garden'),
        path: ['project', 'composition', 'garden'],
        icon: 'tree',
    },
    projectBikeParking: {
        label: trans('label.bike_parking'),
        path: ['project', 'composition', 'bikeParking'],
        icon: 'bike',
    },
    // Nuove costruzioni - Ufficio vendite
    salesOfficeRegion: {
        label: trans('label.region'),
        path: ['project', 'salesOffice', 'city', 'province', 'region', 'name'],
    },
    salesOfficeProvince: {
        label: trans('label.province'),
        path: ['project', 'salesOffice', 'city', 'province', 'name'],
    },
    salesOfficeCity: {
        label: trans('label.municipality'),
        path: ['project', 'salesOffice', 'city', 'name'],
    },
    salesOfficeAddress: {
        label: trans('label.address'),
        path: ['project', 'salesOffice', 'address', 'street'],
    },
    salesOfficePhone: {
        label: trans('label.phone'),
        path: ['project', 'salesOffice', 'phone'],
    },
    salesOfficeMobilePhone: {
        label: trans('label.mobile_phone'),
        path: ['project', 'salesOffice', 'mobilePhone'],
    },
    // Auction lot detail
    procedureBatchNumber: {
        label: trans('label.batch'),
        path: ['auction', 'procedureBatchNumber'],
    },
    procedureBatchCategory: {
        label: trans('label.batch_category'),
        path: ['auction', 'procedureBatchCategory'],
    },
    /**
     * Auction procedure detail
     */
    auctionCourt: {
        label: trans('label.court'),
        path: ['auction', 'court'],
    },
    auctionProcedureType: {
        label: trans('label.procedure_type'),
        path: ['auction', 'procedureType'],
    },
    auctionProcedureNumber: {
        label: trans('label.procedure_number'),
        path: ['auction', 'procedureNumber'],
    },
    auctionProcedureYear: {
        label: trans('label.procedure_year'),
        path: ['auction', 'procedureYear'],
    },
    auctionProcedureProceeding: {
        label: trans('label.procedure_proceeding'),
        path: ['auction', 'procedureRitual'],
    },
    auctionProcedureCode: {
        label: trans('label.procedure_code'),
        path: ['auction', 'procedureCode'],
    },
    auctionInsertionType: {
        label: trans('label.insertion_type'),
        path: ['auction', 'procedureAdType'],
    },
    auctionRegister: {
        label: trans('label.register'),
        path: ['auction', 'procedureRegister'],
    },
    auctionPublicSalePortalUrl: {
        label: trans('label.public_sale_portal_url'),
        path: ['auction', 'urlPvp'],
        component: 'url',
        isTwoColumn: true,
    },
    auctionProcedureRepresentative: {
        label: trans('label.procedure_representative'),
        path: ['auction', 'procedureReferent'],
    },
    /**
     * Auction participation modality
     */
    auctionSaleLocality: {
        label: trans('label.sale_locality'),
        path: ['auction', 'saleLocation'],
    },
    auctionPresentationLocality: {
        label: trans('label.presentation_locality'),
        path: ['auction', 'presentationLocation'],
    },
    auctionPresentationEnd: {
        label: trans('label.presentation_end'),
        path: ['auction', 'expirationDate'],
    },
    auctionReferent: {
        label: trans('label.referent'),
        path: ['auction', 'saleReferent'],
    },
    auctionBasePrice: {
        label: trans('label.base_price'),
        path: ['auction', 'price'],
    },
    auctionMinimumPrice: {
        label: trans('label.minimum_price'),
        path: ['auction', 'minimumOffer'],
    },
    auctionReportValue: {
        label: trans('label.report_value'),
        path: ['auction', 'expertiseValue'],
    },
    auctionSecurityDeposit: {
        label: trans('label.security_deposit'),
        path: ['auction', 'securityDeposit'],
    },
    auctionExpenseAmountDeposit: {
        label: trans('label.expense_amount_deposit'),
        path: ['auction', 'expenseAccountDeposit'],
    },
    auctionMinimumRise: {
        label: trans('label.minimum_rise'),
        path: ['auction', 'minimumRise'],
    },
    auctionReservedDebitExpense: {
        label: trans('label.reserverd_debit_expense'),
        path: ['auction', 'debtReservedExpenses'],
    },
    auctionNotRequiredDuty: {
        label: trans('label.not_required_duty'),
        path: ['auction', 'contributionNotDue'],
    },
    auctionBidMinimumRise: {
        label: trans('label.bid_minimun_rise'),
        path: ['auction', 'auctionMinimumRise'],
    },
    auctionExpenseDeposit: {
        label: trans('label.expense_deposit'),
        path: ['auction', 'depositExpenses'],
    },
    auctionExemptionReason: {
        label: trans('label.exemption_reason'),
        path: ['auction', 'exemptionReason'],
        isTwoColumn: true,
    },
    auctionDepositModality: {
        label: trans('label.deposit_modality'),
        path: ['auction', 'depositMode'],
        isTwoColumn: true,
    },
    auctionNotes: {
        label: trans('label.note'),
        path: ['auction', 'partecipationNotes'],
        isTwoColumn: true,
    },
    /**
     * Roommate
     */
    roommateMinAge: {
        label: trans('label.min_age'),
        path: ['properties', 0, 'sharedApartmentInformation', 'minAge'],
    },
    roommateMaxAge: {
        label: trans('label.max_age'),
        path: ['properties', 0, 'sharedApartmentInformation', 'maxAge'],
    },
    roommateGender: {
        label: trans('label.gender'),
        path: ['properties', 0, 'sharedApartmentInformation', 'gender'],
    },
    roommateSmoker: {
        label: trans('label.smoker'),
        path: ['properties', 0, 'sharedApartmentInformation', 'smoker'],
    },
    roommateCouple: {
        label: trans('label.couples'),
        path: ['properties', 0, 'sharedApartmentInformation', 'couple'],
    },
    roommateAnimals: {
        label: trans('label.animals'),
        path: ['properties', 0, 'sharedApartmentInformation', 'animals'],
    },
    /**
     * Room prices and availability
     */
    roomRentCost: {
        label: trans('label.rent_canon'),
        path: ['price'],
        icon: 'money',
    },
    roomServicesCosts: {
        label: trans('label.services_cost'),
        path: ['properties', 0, 'sharedApartmentInformation', 'servicesCosts'],
        icon: 'money',
    },
    roomOtherCosts: {
        label: trans('label.other_costs'),
        path: ['properties', 0, 'sharedApartmentInformation', 'otherCosts'],
        icon: 'money',
    },
    roomMinimumStay: {
        label: trans('label.minimum_stay'),
        path: ['properties', 0, 'sharedApartmentInformation', 'minimumStay'],
        icon: 'calendar',
    },
    roomAvailableFrom: {
        label: trans('label.available_from'),
        path: ['properties', 0, 'sharedApartmentInformation', 'availableFrom'],
        icon: 'calendar',
    },
    roomElectricityCosts: {
        label: trans('label.electricity_costs'),
        path: ['properties', 0, 'sharedApartmentInformation', 'electricityCostsIncluded'],
        icon: 'light-bulb',
    },
    roomGasCosts: {
        label: trans('label.gas_costs'),
        path: ['properties', 0, 'sharedApartmentInformation', 'gasCostsIncluded'],
        icon: 'fire',
    },
    roomInternetCosts: {
        label: trans('label.internet_costs'),
        path: ['properties', 0, 'sharedApartmentInformation', 'internetCostsIncluded'],
        icon: 'optical-fiber',
    },
    roomPhoneCosts: {
        label: trans('label.phone_costs'),
        path: ['properties', 0, 'sharedApartmentInformation', 'phoneCostsIncluded'],
        icon: 'phone',
    },
    // Energetic class
    energyClass: {
        label: trans('label.energy_class'),
        path: ['properties', 0, 'energyClass', 'energyClass'],
        component: 'energeticTag',
    },
    ipe: {
        label: trans('label.not_renewable_global_ep'),
        path: ['properties', 0, 'energyClass', 'ipe'],
        validFor: [
            {
                path: ['properties', 0, 'energyClass', 'isRenewable'],
                value: [true],
            },
        ],
    },
    renewableIpe: {
        label: trans('label.renewable_global_ep'),
        path: ['properties', 0, 'energyClass', 'renewableIpe'],
        validFor: [
            {
                path: ['properties', 0, 'energyClass', 'isRenewable'],
                value: [true],
            },
        ],
    },
    summerEnergyPerformance: {
        label: trans('label.energy_performance_summer_2'),
        path: ['properties', 0, 'energyClass', 'summerEnergyPerformance'],
        validFor: [
            {
                path: ['properties', 0, 'energyClass', 'isRenewable'],
                value: [true],
            },
        ],
    },
    winterEnergyPerformance: {
        label: trans('label.energy_performance_winter_2'),
        path: ['properties', 0, 'energyClass', 'winterEnergyPerformance'],
        validFor: [
            {
                path: ['properties', 0, 'energyClass', 'isRenewable'],
                value: [true],
            },
        ],
    },
    zeroEnergyBuilding: {
        label: trans('label.building_energy_0'),
        path: ['properties', 0, 'energyClass', 'zeroEnergyBuilding'],
        validFor: [
            {
                path: ['properties', 0, 'energyClass', 'isRenewable'],
                value: [true],
            },
        ],
    },
    energyPerformance: {
        label: trans('label.energy_performance_1'),
        path: ['properties', 0, 'energyClass', 'energyPerformance'],
        validFor: [
            {
                path: ['properties', 0, 'energyClass', 'isRenewable'],
                value: [false],
            },
        ],
    },
    // Energetic class for NEW CONSTRUCTION
    energyClassNC: {
        label: trans('label.energy_class'),
        path: ['project', 'energyClass', 'energyClass'],
        component: 'energeticTag',
    },
    ipeNC: {
        label: trans('label.not_renewable_global_ep'),
        path: ['project', 'energyClass', 'ipe'],
        validFor: [
            {
                path: ['project', 'energyClass', 'isRenewable'],
                value: [true],
            },
        ],
    },
    renewableIpeNC: {
        label: trans('label.renewable_global_ep'),
        path: ['project', 'energyClass', 'renewableIpe'],
        validFor: [
            {
                path: ['project', 'energyClass', 'isRenewable'],
                value: [true],
            },
        ],
    },
    summerEnergyPerformanceNC: {
        label: trans('label.energy_performance_summer_2'),
        path: ['project', 'energyClass', 'summerEnergyPerformance'],
        validFor: [
            {
                path: ['project', 'energyClass', 'isRenewable'],
                value: [true],
            },
        ],
    },
    winterEnergyPerformanceNC: {
        label: trans('label.energy_performance_winter_2'),
        path: ['project', 'energyClass', 'winterEnergyPerformance'],
        validFor: [
            {
                path: ['project', 'energyClass', 'isRenewable'],
                value: [true],
            },
        ],
    },
    zeroEnergyBuildingNC: {
        label: trans('label.building_energy_0'),
        path: ['project', 'energyClass', 'zeroEnergyBuilding'],
        validFor: [
            {
                path: ['project', 'energyClass', 'isRenewable'],
                value: [true],
            },
        ],
    },
    energyPerformanceNC: {
        label: trans('label.energy_performance_1'),
        path: ['project', 'energyClass', 'energyPerformance'],
        validFor: [
            {
                path: ['project', 'energyClass', 'isRenewable'],
                value: [false],
            },
        ],
    },
    // Map section
    country: {
        label: trans('label.nation'),
        path: ['properties', 0, 'geographyInformation', 'city', 'province', 'region', 'country', 'name'],
    },
    region: {
        label: trans('label.region'),
        path: ['properties', 0, 'geographyInformation', 'city', 'province', 'region', 'name'],
    },
    province: {
        label: trans('label.province'),
        path: ['properties', 0, 'geographyInformation', 'city', 'province', 'name'],
    },
    city: {
        label: trans('label.municipality'),
        path: ['properties', 0, 'geographyInformation', 'city', 'name'],
    },
    address: {
        label: trans('label.address'),
        path: ['properties', 0, 'address'],
    },
    // new Dates section
    createdAt: {
        label: trans('label.creation_date'),
        path: ['created'],
    },
    updatedAt: {
        label: trans('label.last_update_date'),
        path: ['modified'],
    },
    firstPublicationDate: {
        label: trans('label.first_publication_date'),
        path: ['firstPublicationDate'],
    },
} satisfies Record<string, FieldData>;

export type FieldId = keyof typeof REAL_ESTATE_FIELDS_DATA;
