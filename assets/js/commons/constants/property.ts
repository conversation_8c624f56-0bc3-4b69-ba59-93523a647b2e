//@ts-ignore
import gtxConstants from 'gtx-constants';
import { RealEstateCategory } from 'types/portalModal';

export const PROPERTY_ITEM_VOID_CONTENT = '---';

export const PROPERTY_IMAGE_PLACEHOLDER = '/bundles/base/getrix/common/img/img-placeholder.png';

const TYPOLOGY_RESIDENTIAL = 1;
const NEW_RESIDENTIAL_TYPOLOGIES = {
    appartamento: 4,
    atticoMansarda: 5,
    boxGarage: 6,
    casaIndipendente: 7,
    palazzoStabile: 10,
    rusticoCasale: 11,
    villa: 12,
    villettaSchiera: 13,
    altro: 14,
    loftOpenSpace: 31,
} as const;

const TYPOLOGY_OFFICE = 256;
const NEW_TYPOLOGY_OFFICE = 104;

const TYPOLOGY_LAND = 4;
const NEW_TYPOLOGY_LAND = 28;

const TYPOLOGY_BUILDINGS = 244;
const NEW_TYPOLOGY_BUILDINGS = 100;

const TYPOLOGY_ROOM = 5;
const NEW_TYPOLOGY_ROOM = 81;

const TYPOLOGY_FARMING_LAND = 183;
const NEW_TYPOLOGY_FARMING_LAND = 106;

const TYPOLOGY_BUILDABLE_LAND = 184;
const NEW_TYPOLOGY_BUILDABLE_LAND = 107;

const TYPOLOGY_WAREHOUSE = 247;
const NEW_TYPOLOGY_WAREHOUSE = 101;

const TYPOLOGY_GARAGE = 250;
const NEW_TYPOLOGY_GARAGE = {
    garage: 102,
    postoAuto: 103,
} as const;

const TYPOLOGY_COWORKING = 257;
const NEW_TYPOLOGY_COWORKING = 105;

const TYPOLOGY_NOT_BUILDABLE_LAND = 260;
const NEW_TYPOLOGY_NOT_BUILDABLE_LAND = 108;

const TYPOLOGY_SHED = 263;
const NEW_TYPOLOGY_SHED = 109;

const TYPOLOGY_SHOP = 266;

const TYPOLOGY_NEW_CONSTRUCTION = 275;

export const NEW_TYPOLOGY_SHOP = 110;

export const OLD_CATEGORIES_TO_NEW_TIPOLOGIES_MAPPING = {
    [TYPOLOGY_RESIDENTIAL]: [
        NEW_RESIDENTIAL_TYPOLOGIES.appartamento,
        NEW_RESIDENTIAL_TYPOLOGIES.atticoMansarda,
        NEW_RESIDENTIAL_TYPOLOGIES.casaIndipendente,
        NEW_RESIDENTIAL_TYPOLOGIES.palazzoStabile,
        NEW_RESIDENTIAL_TYPOLOGIES.rusticoCasale,
        NEW_RESIDENTIAL_TYPOLOGIES.villa,
        NEW_RESIDENTIAL_TYPOLOGIES.villettaSchiera,
        NEW_RESIDENTIAL_TYPOLOGIES.altro,
        NEW_RESIDENTIAL_TYPOLOGIES.loftOpenSpace,
    ],
    [TYPOLOGY_OFFICE]: [NEW_TYPOLOGY_OFFICE],
    [TYPOLOGY_LAND]: [NEW_TYPOLOGY_LAND],
    [TYPOLOGY_BUILDINGS]: [NEW_TYPOLOGY_BUILDINGS],
    [TYPOLOGY_SHED]: [NEW_TYPOLOGY_SHED],
    [TYPOLOGY_WAREHOUSE]: [NEW_TYPOLOGY_WAREHOUSE],
    [TYPOLOGY_GARAGE]: [NEW_TYPOLOGY_GARAGE.garage, NEW_TYPOLOGY_GARAGE.postoAuto],
    [TYPOLOGY_SHOP]: [NEW_TYPOLOGY_SHOP],
    [TYPOLOGY_ROOM]: [NEW_TYPOLOGY_ROOM],
} as const;

export const OLD_TYPOLOGIES_TO_NEW_TIPOLOGIES_MAPPING = {
    [TYPOLOGY_OFFICE]: [NEW_TYPOLOGY_OFFICE],
    [TYPOLOGY_COWORKING]: [NEW_TYPOLOGY_COWORKING],
    [TYPOLOGY_FARMING_LAND]: [NEW_TYPOLOGY_FARMING_LAND],
    [TYPOLOGY_BUILDABLE_LAND]: [NEW_TYPOLOGY_BUILDABLE_LAND],
    [TYPOLOGY_NOT_BUILDABLE_LAND]: [NEW_TYPOLOGY_NOT_BUILDABLE_LAND],
} as const;

const HOUSES_TYPOLOGIES_ENUM = {
    terracieloTerratetto: 10,
    villaVilletta: 11,
    rusticoCasale: 12,
    terratettoUnifamiliare: 21,
    terratettoPlurifamiliare: 22,
    villaUnifamiliare: 23,
    villaBifamiliare: 24,
    villaPlurifamiliare: 25,
    villaASchiera: 26,
    appartamentoInVilla: 27,
    rustico: 28,
    casale: 29,
    cascina: 30,
    casaColonica: 31,
    baita: 32,
    chalet: 33,
    trullo: 34,
    dammuso: 35,
    nuraghe: 36,
    maso: 37,
    baglio: 38,
    rifugio: 39,
    sasso: 40,
    masseria: 41,
} as const;

export const HOUSES_TYPOLOGIES: number[] = [
    HOUSES_TYPOLOGIES_ENUM.terracieloTerratetto,
    HOUSES_TYPOLOGIES_ENUM.villaVilletta,
    HOUSES_TYPOLOGIES_ENUM.rusticoCasale,
    HOUSES_TYPOLOGIES_ENUM.terratettoUnifamiliare,
    HOUSES_TYPOLOGIES_ENUM.terratettoPlurifamiliare,
    HOUSES_TYPOLOGIES_ENUM.villaUnifamiliare,
    HOUSES_TYPOLOGIES_ENUM.villaBifamiliare,
    HOUSES_TYPOLOGIES_ENUM.villaPlurifamiliare,
    HOUSES_TYPOLOGIES_ENUM.villaASchiera,
    HOUSES_TYPOLOGIES_ENUM.appartamentoInVilla,
    HOUSES_TYPOLOGIES_ENUM.rustico,
    HOUSES_TYPOLOGIES_ENUM.casale,
    HOUSES_TYPOLOGIES_ENUM.cascina,
    HOUSES_TYPOLOGIES_ENUM.casaColonica,
    HOUSES_TYPOLOGIES_ENUM.baita,
    HOUSES_TYPOLOGIES_ENUM.chalet,
    HOUSES_TYPOLOGIES_ENUM.trullo,
    HOUSES_TYPOLOGIES_ENUM.dammuso,
    HOUSES_TYPOLOGIES_ENUM.nuraghe,
    HOUSES_TYPOLOGIES_ENUM.maso,
    HOUSES_TYPOLOGIES_ENUM.baglio,
    HOUSES_TYPOLOGIES_ENUM.rifugio,
    HOUSES_TYPOLOGIES_ENUM.sasso,
    HOUSES_TYPOLOGIES_ENUM.masseria,
];

[
    {
        idTipologiaV2: 7,
        nome: 'Appartamento',
    },
    {
        idTipologiaV2: 8,
        nome: 'Loft o Open Space',
    },
    {
        idTipologiaV2: 14,
        nome: 'Appartamento',
    },
    {
        idTipologiaV2: 15,
        nome: 'Attico',
    },
    {
        idTipologiaV2: 16,
        nome: 'Mansarda',
    },
    {
        idTipologiaV2: 17,
        nome: 'Loft',
    },
    {
        idTipologiaV2: 18,
        nome: 'Open space',
    },
];

const APARTAMENTS_TYPOLOGIES_ENUM = {
    appartamento: 7 | 14,
    loftOpenSpace: 8,
    attico: 15,
    mansarda: 16,
    loft: 17,
    openSpace: 18,
} as const;

export const APARTAMENTS_TYPOLOGIES = [
    APARTAMENTS_TYPOLOGIES_ENUM.appartamento,
    APARTAMENTS_TYPOLOGIES_ENUM.loftOpenSpace,
    APARTAMENTS_TYPOLOGIES_ENUM.attico,
    APARTAMENTS_TYPOLOGIES_ENUM.mansarda,
    APARTAMENTS_TYPOLOGIES_ENUM.loft,
    APARTAMENTS_TYPOLOGIES_ENUM.openSpace,
];

export const TYPOLOGIES = {
    residential: TYPOLOGY_RESIDENTIAL,
    land: TYPOLOGY_LAND,
    farmingLand: TYPOLOGY_FARMING_LAND,
    buildableLand: TYPOLOGY_BUILDABLE_LAND,
    notBuildableLand: TYPOLOGY_NOT_BUILDABLE_LAND,
    shed: TYPOLOGY_SHED,
    buildings: TYPOLOGY_BUILDINGS,
    garage: TYPOLOGY_GARAGE,
    shop: TYPOLOGY_SHOP,
    office: TYPOLOGY_OFFICE,
    coworking: TYPOLOGY_COWORKING,
    room: TYPOLOGY_ROOM,
    newConstruction: TYPOLOGY_NEW_CONSTRUCTION,
    warehouse: TYPOLOGY_WAREHOUSE,
};

const NEW_CATEGORIES = {
    residential: 1,
    land: 24,
    auctions: 14,
    shed: 25,
    shop: 26,
    buildings: 20,
    projects: 27,
    internationalSearches: 5,
    turistic: 3,
    garage: 22,
    commercial: 2,
    officeCoworking: 23,
    room: 4,
    newConstruction: 6,
    warehouse: 21,
} as const;

export const NOT_CONVERTIBLE_CATEGORIES = [
    gtxConstants('CATEGORIA_ASTE'),
    gtxConstants('CATEGORIA_VACANZE'),
    TYPOLOGY_ROOM,
];

export const NOT_CONVERTIBLE_TYPOLOGIES = [gtxConstants('TIPOLOGIA_V2_COWORKING')];

export const RANKING_GOOD_QUALITY_THRESHOLD = 60;

export const ENDPOINTS = {
    propertyAdd: '/inserimento_annuncio.php',
    auctionAdd: '/inserimento_asta.php',
    saleRentPropertyAdd: '/inserimento_venduto_affittato.php',
    newConstructionAdd: '/v2/nuove-costruzioni',
};

export const CITY_WITH_URBAN_AREAS = 2;
export const CITY_WITH_MACROZONES = 1;
export const NEW_CONSTRUCTIONS_CATEGORIES: number[] = [NEW_CATEGORIES.newConstruction, NEW_CATEGORIES.projects];
export const DELETED_STATUS_ID = 999;
export const SHOW_CADASTRAL_DATA = Boolean(gtxConstants('SHOW_CADASTRAL_DATA'));

export const SHOW_FIRST_PAGE_TAG = Boolean(gtxConstants('SHOW_FIRST_PAGE_TAG'));
export const FIRST_PAGE_TAG_THRESHOLD = gtxConstants('FIRST_PAGE_TAG_THRESHOLD');
export const COCKADE_ICON_THRESHOLD = gtxConstants('COCKADE_ICON_THRESHOLD');
export const CONTRACT = {
    SALE: gtxConstants('CONTRATTO_VENDITA'),
    RENT: gtxConstants('CONTRATTO_AFFITTO'),
} as const;

/**
 * value of fkStatoAnnuncio which identify a secret property
 */
export const SECRET_PROPERTY_STATUS_ID = 14;

export const PARKING_TYPOLOGIES = {
    GARAGE: 251,
    PARKING_SPACE: 252,
} as const;

/** Constant which contains the path for "ad-insert" edit form */
export const REAL_ESTATE_AD_EDIT_URL: Record<RealEstateCategory, string> = {
    auction: '/inserimento_annuncio.php',
    property: '/inserimento_annuncio.php',
    newConstruction: '/v2/nuove-costruzioni',
};
