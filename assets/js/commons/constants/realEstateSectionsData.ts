import gtxConstants from '@getrix/common/js/gtx-constants';
import { trans } from '@pepita-i18n/babelfish';

import { SHOW_CADASTRAL_DATA, TYPOLOGIES } from 'constants/property';
import { DetailPropertyType } from 'lib/REST/types/detail-property';
import { RealEstateCategory } from 'types/portalModal';
import { FieldId } from './realEstateFieldsData';

export type SectionProps = SectionData & {
    realEstate: DetailPropertyType;
    category: RealEstateCategory;
};

export type SectionData = Readonly<{
    /** Section's "unique" identifier */
    name: string;
    /** Section's title (translation) */
    title?: string;
    /** Step which cointains the section into ad-inser multi-step form */
    step?: string;
    /** Anchor to jump into the section; optional, because some steps cointains only one sections (like 'visibility') */
    anchor?: string;
    /** Optional boolean to indicate if a section is not for the "parent's real estate" but for its main child (useful for "auction") */
    forMainChildren?: boolean;
    /** Array of category for which this section is valid; optional because "newConstruction" and "auction" don't handle multiple categories */
    categoryId?: Array<number>;
    /** Array of typologies for which this section is valid (please take a look into `SECTIONS_BY_TYPOLOGY_ID` constant); optional because "newConstruction" and "auction" don't handle multiple categories */
    typologyId?: Array<number>;
    /** Array of fields that the section contains, useful to auto-generate section's content into modal; optional becase special section could not have this (and they are manually handled). */
    data?: Array<FieldId>;
    /** Classname to be applied to the wrapper of the whole section into the modal. */
    className?: string;
}>;

export const SECTIONS_BY_TYPOLOGY_ID = [
    TYPOLOGIES.farmingLand,
    TYPOLOGIES.buildableLand,
    TYPOLOGIES.office,
    TYPOLOGIES.coworking,
    TYPOLOGIES.notBuildableLand,
];

export const NEW_CONSTRUCTION_SECTION_DATA = [
    // Nota
    // sezione speciale
    {
        name: 'note',
        title: trans('label.note'),
        step: '2',
        anchor: 'note',
    },
    /** new "Dates" section */
    {
        name: 'dates',
        data: ['createdAt', 'firstPublicationDate', 'updatedAt'],
    },
    /** Informazioni principali */
    {
        name: 'mainInfo',
        title: trans('label.main_infos'),
        step: '2',
        anchor: 'informazioni',
        data: [
            'projectName',
            'proposer',
            'residentialUnits',
            'commercialUnits',
            'constructionStartDate',
            'expectedDeliveryDate',
            'parkingSpacesNC',
            'parkingSpacesInGarageNC',
            'agentName',
            'collaboration',
            'workProgressNC',
        ],
    },
    // Caratteristiche dell'immobile
    {
        name: 'features',
        title: trans('label.property_features'),
        step: '2',
        anchor: 'caratteristiche',
        data: [
            'projectHeating',
            'projectHeatingType',
            'projectHeatingSupply',
            'projectAirConditioning',
            'projectAirConditioningType',
            'projectExternalFixtures',
            'projectDisabledAccess',
            'projectCommonGarden',
            'projectBikeParking',
        ],
    },
    /**
     * Sales office
     */
    {
        name: 'salesOffice',
        title: trans('label.sales_office'),
        step: '2',
        anchor: 'ufficio',
        data: [
            'salesOfficeRegion',
            'salesOfficeProvince',
            'salesOfficeCity',
            'salesOfficeAddress',
            'salesOfficePhone',
            'salesOfficeMobilePhone',
        ],
    },
    // Efficienza energetica
    // sezione speciale
    {
        name: 'energeticEfficiency',
        title: trans('label.energy_certification'),
        step: '2',
        anchor: 'certificazione-energetica',
        data: [
            'energyClassNC',
            'energyPerformanceNC',
            'ipeNC',
            'renewableIpeNC',
            'summerEnergyPerformanceNC',
            'winterEnergyPerformanceNC',
            'zeroEnergyBuildingNC',
        ],
    },
    {
        name: 'descriptions',
        title: trans('label.description'),
        step: '2',
        anchor: 'descrizione',
    },
    // Mappa
    // sezione speciale (non prevista)
    {
        name: 'map',
        title: trans('label.map'),
        step: '3',
        data: ['country', 'region', 'province', 'city', 'address'],
    },
    // Visibilità
    // sezione speciale (non prevista)
    {
        name: 'visibilities',
        title: trans('label.visibility_options'),
        step: '5',
    },
    // Immobili (associati)
    // sezione speciale
    {
        name: 'relatedRealEstates',
        title: trans('label.estates'),
        step: '4',
    },
] as const satisfies readonly SectionData[];

export type NewConstructionSectionName = (typeof NEW_CONSTRUCTION_SECTION_DATA)[number]['name'];

export const AUCTION_SECTIONS_DATA = [
    // Nota
    // sezione speciale
    {
        name: 'note',
        title: trans('label.note'),
        step: '2',
        anchor: 'note',
    },
    /** new "Dates" section */
    {
        name: 'dates',
        data: ['createdAt', 'firstPublicationDate', 'updatedAt'],
    },
    // Lot detail
    {
        name: 'lotDetail',
        title: trans('label.batch_detail'),
        step: '2',
        anchor: 'lotto',
        data: ['procedureBatchNumber', 'procedureBatchCategory'],
    },
    /**
     * Procedure detail
     */
    {
        name: 'procedureDetail',
        title: trans('label.procedure_detail'),
        step: '2',
        anchor: 'procedura',
        data: [
            'auctionCourt',
            'auctionProcedureType',
            'auctionProcedureNumber',
            'auctionProcedureYear',
            'auctionProcedureProceeding',
            'auctionProcedureCode',
            'auctionInsertionType',
            'auctionRegister',
            'auctionPublicSalePortalUrl',
            'auctionProcedureRepresentative',
        ],
    },
    /**
     * Participation modality
     */
    {
        name: 'participationModality',
        title: trans('label.participation_modality'),
        step: '2',
        anchor: 'modalita-partecipazione',
        data: [
            'auctionSaleLocality',
            'auctionPresentationLocality',
            'auctionPresentationEnd',
            'auctionReferent',
            'auctionBasePrice',
            'auctionMinimumPrice',
            'auctionReportValue',
            'auctionSecurityDeposit',
            'auctionExpenseAmountDeposit',
            'auctionMinimumRise',
            'auctionReservedDebitExpense',
            'auctionNotRequiredDuty',
            'auctionBidMinimumRise',
            'auctionExpenseDeposit',
            'auctionExemptionReason',
            'auctionDepositModality',
            'auctionNotes',
        ],
    },
    // Dettaglio vendite
    // sezione speciale
    {
        name: 'salesDetails',
        title: trans('label.sales_detail'),
        step: '2',
        anchor: 'vendite',
        className: 'propertyDetail__surface',
    },
    /**
     * intermediation data
     */
    {
        name: 'intermediation',
        title: trans('label.intermediation_data'),
        step: '2',
        anchor: 'intermediazione',
        data: ['reference', 'agentName', 'collaboration'],
    },
    {
        name: 'descriptions',
        title: trans('label.description'),
        step: '3b',
        anchor: 'descrizione',
        forMainChildren: true,
    },
    // Mappa
    // sezione speciale (non prevista)
    {
        name: 'map',
        title: trans('label.map'),
        step: '3b',
        anchor: 'indirizzo',
        forMainChildren: true,
        data: ['country', 'region', 'province', 'city', 'address'],
    },
    // Visibilità
    // sezione speciale (non prevista)
    {
        name: 'visibilities',
        title: trans('label.visibility_options'),
        step: '4',
    },
    // Immobili (associati)
    // sezione speciale
    {
        name: 'relatedRealEstates',
        title: trans('label.auction_estates'),
        step: '3b',
    },
] as const satisfies readonly SectionData[];

export type AuctionSectionName = (typeof AUCTION_SECTIONS_DATA)[number]['name'];

export const PROPERTY_SECTIONS_DATA = [
    // Nota
    // sezione speciale
    {
        name: 'note',
        title: trans('label.note'),
        categoryId: [
            TYPOLOGIES.residential,
            TYPOLOGIES.buildings,
            TYPOLOGIES.garage,
            TYPOLOGIES.shop,
            TYPOLOGIES.warehouse,
            TYPOLOGIES.shed,
            TYPOLOGIES.room,
        ],
        typologyId: [
            TYPOLOGIES.coworking,
            TYPOLOGIES.office,
            TYPOLOGIES.buildableLand,
            TYPOLOGIES.farmingLand,
            TYPOLOGIES.notBuildableLand,
        ],
        step: '2',
        anchor: 'note',
    },
    /** new "Dates" section */
    {
        name: 'dates',
        categoryId: [
            TYPOLOGIES.residential,
            TYPOLOGIES.buildings,
            TYPOLOGIES.garage,
            TYPOLOGIES.shop,
            TYPOLOGIES.warehouse,
            TYPOLOGIES.shed,
            TYPOLOGIES.room,
        ],
        typologyId: [
            TYPOLOGIES.coworking,
            TYPOLOGIES.office,
            TYPOLOGIES.buildableLand,
            TYPOLOGIES.farmingLand,
            TYPOLOGIES.notBuildableLand,
        ],
        data: ['createdAt', 'firstPublicationDate', 'updatedAt'],
    },
    /** Tipologia dell'immobile */
    {
        name: 'typology',
        title: trans('label.type_of_property'),
        categoryId: [
            TYPOLOGIES.residential,
            TYPOLOGIES.buildings,
            TYPOLOGIES.garage,
            TYPOLOGIES.warehouse,
            TYPOLOGIES.shed,
        ],
        typologyId: [
            TYPOLOGIES.coworking,
            TYPOLOGIES.office,
            TYPOLOGIES.buildableLand,
            TYPOLOGIES.farmingLand,
            TYPOLOGIES.notBuildableLand,
        ],
        step: '2',
        anchor: 'tipologia',
        data: ['category', 'propertyTypology'],
    },
    {
        name: 'typology',
        title: trans('label.type_of_property'),
        categoryId: [TYPOLOGIES.shop],
        data: ['category', 'typology', 'subjectSaleRent', 'license', 'lastLicense'],
        step: '2',
        anchor: 'tipologia',
    },
    {
        name: 'typology',
        title: trans('label.type_of_property'),
        categoryId: [TYPOLOGIES.room],
        data: ['category', 'propertyTypology', 'subTypology'],
        step: '2',
        anchor: 'tipologia',
    },
    /** Main info (ROOMS only) */
    {
        categoryId: [TYPOLOGIES.room],
        name: 'mainInfo',
        title: trans('label.main_infos'),
        step: '2',
        anchor: 'info-principali',
        data: ['propertyClass', 'propertyOwnership', 'rooms', 'bathrooms', 'roomType', 'floor', 'totalBuildingFloors'],
    },
    // Contract, prices and fees
    {
        name: 'contractPricesAndFees',
        title: trans('label.contract_prices'),
        categoryId: [TYPOLOGIES.residential],
        step: '2',
        anchor: 'prezzi',
        data: [
            'contract',
            'propertyOwnership',
            'price',
            'deposit',
            'condominiumCosts',
            'heatingFees',
            'freeNow',
            'availability',
            'rentToBuy',
            'incomeProducingProperty',
            'rentContractType',
            ...(gtxConstants('PROPERTY_FIELD_AVAILABLE_FOR_STUDENTS_ENABLED')
                ? (['availableForStudents'] as FieldId[])
                : []),
        ],
    },
    {
        name: 'contractPricesAndFees',
        title: trans('label.contract_prices'),
        categoryId: [TYPOLOGIES.buildings],
        typologyId: [TYPOLOGIES.buildableLand, TYPOLOGIES.farmingLand, TYPOLOGIES.notBuildableLand],
        step: '2',
        anchor: 'prezzi',
        data: ['contract', 'price', 'deposit', 'freeNow', 'availability', 'rentToBuy', 'incomeProducingProperty'],
    },
    {
        name: 'contractPricesAndFees',
        title: trans('label.contract_prices'),
        categoryId: [TYPOLOGIES.garage],
        step: '2',
        anchor: 'prezzi',
        data: ['condominiumCosts', 'deposit', 'freeNow', 'availability', 'rentToBuy', 'incomeProducingProperty'],
    },
    {
        name: 'contractPricesAndFees',
        title: trans('label.contract_prices'),
        categoryId: [TYPOLOGIES.shop, TYPOLOGIES.warehouse, TYPOLOGIES.shed],
        typologyId: [TYPOLOGIES.office],
        step: '2',
        anchor: 'prezzi',
        data: [
            'contract',
            'price',
            'deposit',
            'condominiumCosts',
            'rentToBuy',
            'incomeProducingProperty',
            'freeNow',
            'availability',
        ],
    },
    // Superficie
    // sezione speciale
    {
        name: 'consistencies',
        title: trans('label.surface'),
        categoryId: [
            TYPOLOGIES.residential,
            TYPOLOGIES.buildings,
            TYPOLOGIES.shop,
            TYPOLOGIES.warehouse,
            TYPOLOGIES.shed,
            TYPOLOGIES.room,
        ],
        step: '2',
        anchor: 'superficie',
        typologyId: [TYPOLOGIES.office],
        className: 'propertyDetail__surface',
    },
    {
        name: 'surface',
        title: trans('label.surface_detail'),
        categoryId: [TYPOLOGIES.garage],
        step: '2',
        anchor: 'superficie',
        data: ['surface', 'consistenceFloor', 'garageDoorWidth'],
    },
    {
        name: 'surface',
        title: trans('label.surface_detail'),
        typologyId: [TYPOLOGIES.coworking],
        data: ['surface', 'floor'],
        step: '2',
        anchor: 'surface-coworking',
    },
    {
        name: 'surface',
        title: trans('label.surface_detail'),
        typologyId: [TYPOLOGIES.buildableLand],
        step: '2',
        anchor: 'superficie',
        data: ['surface', 'buildableSurface', 'buildableHeight'],
    },
    {
        name: 'surface',
        title: trans('label.surface_detail'),
        typologyId: [TYPOLOGIES.farmingLand, TYPOLOGIES.notBuildableLand],
        step: '2',
        anchor: 'superficie',
        data: ['surface'],
    },
    // Composizione dell'immobile
    {
        name: 'composition',
        title: trans('label.property_composition'),
        categoryId: [TYPOLOGIES.residential],
        step: '2',
        anchor: 'composizione',
        data: [
            'bedRooms',
            'otherRooms',
            'rooms',
            'bathrooms',
            'kitchenType',
            'garden',
            'garage',
            'parkingSpaces',
            'balconies',
            'terrace',
            'fittedWardrobe',
            'basement',
            'mansard',
            'tavern',
            'furniture',
            'externalFixtures',
            'tvSystem',
            'conciergeService',
            'securityDoor',
            'antiTheftDevice',
            'electricGate',
            'videoDoorPhone',
            'opticalFiber',
            'firePlace',
            'hydroMassage',
            'swimmingPool',
            'tennisCourt',
        ],
    },
    {
        name: 'composition',
        title: trans('label.property_composition'),
        categoryId: [TYPOLOGIES.buildings],
        step: '2',
        anchor: 'composizione',
        data: [
            'rooms',
            'bathrooms',
            'disabledPersonsBathroom',
            'balconies',
            'parkingSpaces',
            'parkingSpacesInGarage',
            'antiTheftDevice',
            'reception',
            'externalFixtures',
        ],
    },
    {
        name: 'composition',
        title: trans('label.property_composition'),
        categoryId: [TYPOLOGIES.garage],
        step: '2',
        anchor: 'composizione',
        data: [
            'antiTheftDevice',
            'garageParkingSpaces',
            'motorcycleParkingSpaces',
            'electricGarageDoor',
            'chargingStations',
            'cctv',
        ],
    },
    {
        name: 'composition',
        title: trans('label.property_composition'),
        categoryId: [TYPOLOGIES.shop],
        step: '2',
        anchor: 'composizione',
        data: [
            'rooms',
            'bathrooms',
            'disabledPersonsBathroom',
            'furniture',
            'activityEquipment',
            'ceilingHeight',
            'storefrontsCount',
            'storefrontsSurface',
            'orientations',
            'floatingFloor',
            'vacuumSystem',
            'flue',
            'mechanicsVentilation',
            'wired',
            'manualShutter',
            'automaticShutter',
            'fireSensor',
            'rainSensor',
            'fireHydrant',
            'fireBreakDoor',
            'emergencyExit',
            'emergencyLightings',
            'driveway',
            'parkingSpaces',
            'parkingSpacesInGarage',
            'antiTheftDevice',
            'cctv',
        ],
    },
    {
        name: 'composition',
        title: trans('label.property_composition'),
        categoryId: [TYPOLOGIES.warehouse],
        step: '2',
        anchor: 'composizione',
        data: [
            'warehouseHeight',
            'bathrooms',
            'disabledPersonsBathroom',
            'driveway',
            'parkingSpaces',
            'parkingSpacesInGarage',
            'antiTheftDevice',
            'cctv',
            'enclosed',
        ],
    },
    {
        name: 'composition',
        title: trans('label.property_composition'),
        categoryId: [TYPOLOGIES.shed],
        step: '2',
        anchor: 'composizione',
        data: [
            'shedHeight',
            'subbeamHeight',
            'pillars',
            'loadingDocks',
            'cranes',
            'loadCapacity',
            'spans',
            'rooms',
            'bathrooms',
            'disabledPersonsBathroom',
            'dressingRoom',
            'shower',
            'canteen',
            'officeSurface',
            'parkingSpaces',
            'parkingSpacesInGarage',
            'antiTheftDevice',
            'cctv',
            'driveway',
            'reception',
            'enclosed',
        ],
    },
    {
        name: 'composition',
        title: trans('label.property_composition'),
        typologyId: [TYPOLOGIES.coworking],
        step: '2',
        anchor: 'composizione-coworking',
        data: [
            'bathrooms',
            'disabledPersonsBathroom',
            'kitchen',
            'balconies',
            'terraces',
            'parkingSpaces',
            'garage',
            'bikeParking',
            'warehouse',
            'reception',
            'externalFixtures',
            'broadbandInternet',
            'multifunctionPrinter',
            'garden',
            'hotDrinks',
            'convivialityRoom',
            'microwave',
            'cleaning',
            'waitingRoom',
            'fridge',
            'collectionStorageService',
            'dishwasher',
            'phoneCallSpaces',
            'domiciliation',
            'badge',
            'music',
            'tv',
            'locker',
            'newspapers',
            'fruit',
            'coffeeBar',
            'shower',
            'guardian',
            'barber',
            'asylum',
            'fitnessRoom',
            'otherServices',
        ],
    },
    {
        name: 'composition',
        title: trans('label.property_composition'),
        typologyId: [TYPOLOGIES.office],
        step: '2',
        anchor: 'composizione-office',
        data: [
            'rooms',
            'bathrooms',
            'disabledPersonsBathroom',
            'kitchen',
            'furniture',
            'interiorSubdivision',
            'externalFixtures',
            'orientations',
            // internal/external esposition is a single field into ad-insert
            'internalExposition',
            'externalExposition',
            'wired',
            'broadbandInternet',
            'floatingFloor',
            'counterTop',
            'securityDoor',
            'accessControl',
            'parkingSpaces',
            'parkingSpacesInGarage',
            'bikeParking',
            'reception',
            'antiTheftDevice',
            'fireSensor',
            'rainSensor',
            'fireHydrant',
            'fireBreakDoor',
            'emergencyExit',
            'emergencyLightings',
        ],
    },
    {
        name: 'composition',
        title: trans('label.property_composition'),
        typologyId: [TYPOLOGIES.notBuildableLand, TYPOLOGIES.buildableLand, TYPOLOGIES.farmingLand],
        step: '2',
        anchor: 'composizione',
        data: [
            'landDeclivity',
            'waterConnection',
            'electricityConnection',
            'gasConnection',
            'sewersConnection',
            'publicLighting',
            'enclosed',
            'accessType',
            ...(gtxConstants('PROPERTY_FIELD_VIEW_ENABLED') ? (['views'] as FieldId[]) : []),
        ],
    },
    // Urban qualification
    {
        name: 'urbanQualification',
        title: trans('label.urban_qualification'),
        typologyId: [TYPOLOGIES.buildableLand],
        step: '2',
        anchor: 'qualificazione-urbanistica',
        data: ['urbanQualification'],
    },
    // Land features
    {
        name: 'landFeatures',
        title: trans('label.land_property'),
        typologyId: [TYPOLOGIES.farmingLand],
        step: '2',
        anchor: 'proprieta',
        data: ['landFeatures'],
    },
    // Caratteristiche dell'immobile
    {
        name: 'features',
        title: trans('label.property_features'),
        categoryId: [TYPOLOGIES.residential],
        step: '2',
        anchor: 'caratteristiche',
        data: [
            'constructionYear',
            'propertyClass',
            'propertyCondition',
            'floor',
            'totalBuildingFloors',
            'disabledAccess',
            'openSides',
            'exposure',
            'heating',
            'heatingType',
            'heatingSupply',
            'airConditioning',
            'airConditioningType',
            ...(gtxConstants('PROPERTY_FIELD_VIEW_ENABLED') ? (['views'] as FieldId[]) : []),
            ...(gtxConstants('PROPERTY_FIELD_DISTANCE_FROM_SEA_ENABLED') ? (['distanceFromSea'] as FieldId[]) : []),
            ...(gtxConstants('PROPERTY_FIELD_SOLAR_WATER_HEATING_ENABLED') ? (['solarWaterHeating'] as FieldId[]) : []),
        ],
    },
    {
        name: 'features',
        title: trans('label.property_features'),
        categoryId: [TYPOLOGIES.buildings],
        step: '2',
        anchor: 'caratteristiche',
        data: [
            'propertyClass',
            'propertyCondition',
            'constructionYear',
            'disabledAccess',
            'elevators',
            'openSides',
            'buildingUsages',
            'internalExposition',
            'externalExposition',
            'heating',
            'heatingType',
            'heatingSupply',
            'airConditioning',
            'airConditioningType',
            ...(gtxConstants('PROPERTY_FIELD_VIEW_ENABLED') ? (['views'] as FieldId[]) : []),
        ],
    },
    {
        name: 'features',
        title: trans('label.property_features'),
        categoryId: [TYPOLOGIES.garage],
        step: '2',
        anchor: 'caratteristiche',
        data: ['garageType', 'dayAccessibility', 'disabledAccess'],
    },
    {
        name: 'features',
        title: trans('label.property_features'),
        categoryId: [TYPOLOGIES.shop],
        step: '2',
        anchor: 'caratteristiche',
        data: [
            'propertyCondition',
            'constructionYear',
            'totalBuildingFloors',
            'disabledAccess',
            'cornerPosition',
            'doubleEntrance',
            'yearsOfActivity',
            'lastYearRevenue',
            'heating',
            'heatingType',
            'heatingSupply',
            'airConditioning',
            'airConditioningType',
        ],
    },
    {
        name: 'features',
        title: trans('label.property_features'),
        categoryId: [TYPOLOGIES.warehouse],
        step: '2',
        anchor: 'caratteristiche',
        data: ['propertyCondition', 'constructionYear', 'disabledAccess'],
    },
    {
        name: 'features',
        title: trans('label.property_features'),
        categoryId: [TYPOLOGIES.shed],
        step: '2',
        anchor: 'caratteristiche',
        data: [
            'propertyCondition',
            'constructionYear',
            'disabledAccess',
            'heating',
            'heatingType',
            'heatingSupply',
            'airConditioning',
            'airConditioningType',
        ],
    },
    {
        name: 'features',
        title: trans('label.property_features'),
        categoryId: [TYPOLOGIES.room],
        step: '2',
        anchor: 'caratteristiche-immobile',
        data: [
            'roomBalcony',
            'roomKitchen',
            'roomGarden',
            'roomGym',
            'roomSwimmingPool',
            'roomUtilityRoom',
            'roomTerrace',
            ...(gtxConstants('PROPERTY_FIELD_VIEW_ENABLED') ? (['views'] as FieldId[]) : []),
        ],
    },
    {
        name: 'features',
        title: trans('label.property_features'),
        typologyId: [TYPOLOGIES.coworking],
        step: '2',
        anchor: 'caratteristiche',
        data: [
            'totalBuildingFloors',
            'disabledAccess',
            'elevators',
            'heating',
            'heatingType',
            'heatingSupply',
            'airConditioning',
            'airConditioningType',
        ],
    },
    {
        name: 'features',
        title: trans('label.property_features'),
        typologyId: [TYPOLOGIES.office],
        step: '2',
        anchor: 'caratteristiche',
        data: [
            'propertyClass',
            'propertyCondition',
            'constructionYear',
            'totalBuildingFloors',
            'buildingUsages', // here this is a string, on ad-insert it's a list of checkboxes
            'disabledAccess',
            'elevators',
            'heating',
            'heatingType',
            'heatingSupply',
            'airConditioning',
            'airConditioningType',
        ],
    },
    // Accessori - sezione solo per STANZE
    {
        name: 'propertyAccessories',
        title: trans('label.property_accessories'),
        categoryId: [TYPOLOGIES.room],
        step: '2',
        anchor: 'accessori-immobile',
        data: [
            'roomFireplace',
            'roomTennisCourt',
            'roomInternet',
            'roomDishwasher',
            'roomWashingMachine',
            'roomMicrowave',
            'roomConciergeService',
            'roomAntiTheftSystem',
            'roomSatelliteTv',
        ],
    },
    // Caratteristiche stanza - solo per STANZE
    {
        name: 'roomFeatures',
        title: trans('label.room_features'),
        categoryId: [TYPOLOGIES.room],
        step: '2',
        anchor: 'caratteristiche-stanza',
        data: [
            'roomAirConditioned',
            'roomWardrobe',
            'roomPhone',
            'roomPrivateBathroom',
            'roomSharedBathroom',
            'roomLaundry',
            'roomPrivateEntrance',
            'roomMoquette',
            'roomParquet',
            'roomTv',
        ],
    },
    // Coinquilino - solo per STANZE
    {
        name: 'roommate',
        title: trans('label.roommate'),
        categoryId: [TYPOLOGIES.room],
        step: '2',
        anchor: 'coinquilino',
        data: [
            'roommateMinAge',
            'roommateMaxAge',
            'roommateGender',
            'roommateSmoker',
            'roommateCouple',
            'roommateAnimals',
        ],
    },
    // Stanza - prezzi e disponibilità
    {
        name: 'roomPrices',
        title: trans('label.prices_and_availability'),
        categoryId: [TYPOLOGIES.room],
        step: '2',
        anchor: 'prezzi',
        data: [
            'condominiumCosts',
            'roomServicesCosts',
            'roomOtherCosts',
            'roomRentCost',
            'deposit',
            'roomAvailableFrom',
            'roomMinimumStay',
            'roomElectricityCosts',
            'roomGasCosts',
            'roomInternetCosts',
            'roomPhoneCosts',
        ],
    },
    // Efficienza energetica
    // sezione speciale
    {
        name: 'energeticEfficiency',
        title: trans('label.energy_certification'),
        categoryId: [TYPOLOGIES.residential, TYPOLOGIES.buildings, TYPOLOGIES.shop, TYPOLOGIES.shed, TYPOLOGIES.room],
        typologyId: [TYPOLOGIES.coworking, TYPOLOGIES.office],
        step: '2',
        anchor: 'certificazione-energetica',
        data: [
            'energyClass',
            'energyPerformance',
            'ipe',
            'renewableIpe',
            'summerEnergyPerformance',
            'winterEnergyPerformance',
            'zeroEnergyBuilding',
        ],
    },
    // Opening Hours
    {
        name: 'openingHours',
        title: trans('label.opening_hours'),
        typologyId: [TYPOLOGIES.coworking],
        step: '2',
        anchor: 'orari',
    },
    // Dati catastali
    ...(SHOW_CADASTRAL_DATA
        ? ([
              {
                  name: 'cadastralData',
                  title: trans('label.cadastral_data'),
                  categoryId: [
                      TYPOLOGIES.residential,
                      TYPOLOGIES.buildings,
                      TYPOLOGIES.garage,
                      TYPOLOGIES.shop,
                      TYPOLOGIES.warehouse,
                      TYPOLOGIES.shed,
                  ],
                  typologyId: [
                      TYPOLOGIES.coworking,
                      TYPOLOGIES.office,
                      TYPOLOGIES.buildableLand,
                      TYPOLOGIES.farmingLand,
                      TYPOLOGIES.notBuildableLand,
                  ],
                  step: '2',
                  anchor: 'datiCatastali',
                  data: [
                      'cadastralSection',
                      'cadastralSheet',
                      'cadastralParcel',
                      'cadastralSubordinate',
                      'cadastralCategory',
                      'cadastralIncome',
                      'cadastralOwnershipShares',
                      'otherCadastralData',
                  ],
              },
          ] as SectionData[])
        : []),
    {
        name: 'cadastralAndBrokerageData',
        title: trans(SHOW_CADASTRAL_DATA ? 'label.cadastral_and_intermediation_data' : 'label.intermediation_data'),
        categoryId: [TYPOLOGIES.room],
        step: '2',
        anchor: 'intermediazione',
        data: [
            'reference',
            'agentName',
            'collaboration',
            ...(SHOW_CADASTRAL_DATA ? (['cadastralIncome', 'cadastralCategory'] as FieldId[]) : []),
        ],
    },
    // Descrizione
    // sezione speciale
    {
        name: 'descriptions',
        title: trans('label.description'),
        categoryId: [
            TYPOLOGIES.residential,
            TYPOLOGIES.buildings,
            TYPOLOGIES.garage,
            TYPOLOGIES.shop,
            TYPOLOGIES.warehouse,
            TYPOLOGIES.shed,
            TYPOLOGIES.room,
        ],
        typologyId: [
            TYPOLOGIES.coworking,
            TYPOLOGIES.office,
            TYPOLOGIES.buildableLand,
            TYPOLOGIES.farmingLand,
            TYPOLOGIES.notBuildableLand,
        ],
        step: '2',
        anchor: 'descrizione',
    },
    // Dati intermediazione
    {
        name: 'intermediationData',
        title: trans('label.intermediation_data'),
        categoryId: [
            TYPOLOGIES.residential,
            TYPOLOGIES.buildings,
            TYPOLOGIES.shop,
            TYPOLOGIES.warehouse,
            TYPOLOGIES.shed,
        ],
        typologyId: [TYPOLOGIES.office],
        step: '2',
        anchor: 'intermediazione',
        data: [
            'reference',
            'agentName',
            'ownerName',
            'collaboration',
            'mandateType',
            'mandateExpireDate',
            'workProgress',
            'buildProposer',
        ],
    },
    {
        name: 'intermediationData',
        title: trans('label.intermediation_data'),
        categoryId: [TYPOLOGIES.garage],
        typologyId: [
            TYPOLOGIES.coworking,
            TYPOLOGIES.buildableLand,
            TYPOLOGIES.farmingLand,
            TYPOLOGIES.notBuildableLand,
        ],
        step: '2',
        anchor: 'intermediazione',
        data: ['reference', 'agentName', 'ownerName', 'collaboration', 'mandateType', 'mandateExpireDate'],
    },
    // Mappa
    // sezione speciale
    {
        name: 'map',
        title: trans('label.map'),
        categoryId: [
            TYPOLOGIES.residential,
            TYPOLOGIES.buildings,
            TYPOLOGIES.garage,
            TYPOLOGIES.shop,
            TYPOLOGIES.warehouse,
            TYPOLOGIES.shed,
            TYPOLOGIES.room,
        ],
        typologyId: [
            TYPOLOGIES.coworking,
            TYPOLOGIES.office,
            TYPOLOGIES.buildableLand,
            TYPOLOGIES.farmingLand,
            TYPOLOGIES.notBuildableLand,
        ],
        step: '3',
        anchor: 'indirizzo',
        data: ['country', 'region', 'province', 'city', 'address'],
    },
    // Visibilità
    // sezione speciale
    {
        name: 'visibilities',
        title: trans('label.visibility_options'),
        categoryId: [
            TYPOLOGIES.residential,
            TYPOLOGIES.buildings,
            TYPOLOGIES.garage,
            TYPOLOGIES.shop,
            TYPOLOGIES.warehouse,
            TYPOLOGIES.shed,
            TYPOLOGIES.room,
        ],
        typologyId: [
            TYPOLOGIES.coworking,
            TYPOLOGIES.office,
            TYPOLOGIES.buildableLand,
            TYPOLOGIES.farmingLand,
            TYPOLOGIES.notBuildableLand,
        ],
        step: '4',
    },
    // Workstations
    {
        name: 'workstations',
        title: trans('label.workstations'),
        typologyId: [TYPOLOGIES.coworking],
        step: '3',
        anchor: 'workstations',
    },
] as const satisfies readonly SectionData[];

// this type is not working, array is too big
// export type PropertySectionName = typeof PROPERTY_SECTIONS_DATA[number]['name']
