const $ = require('jquery');
const gtxConstants = require('@getrix/common/js/gtx-constants');
const AskHelpView = require('@getrix/common/js/ask-help');

const salesforceOfflineFormEnabled = gtxConstants('SALESFORCE_OFFLINE_FORM_ENABLED') === 1;
const salesforceLiveChatEnabled = gtxConstants('SALESFORCE_LIVE_CHAT_ENABLED') === 1;

function mailXhr(url, data) {
    return $.ajax({
        url: url,
        type: 'POST',
        dataType: 'json',
        data: data,
    });
}

if (!salesforceLiveChatEnabled) {
    let askHelp = new AskHelpView({
        submitAction: mailXhr,
        external: salesforceOfflineFormEnabled ? 'salesforce' : null,
    });
    askHelp.initialize($('.gtx-ask-help'));
}
