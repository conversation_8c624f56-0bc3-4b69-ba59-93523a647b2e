const isInternalPath = require('../../commons/lib/is-internal-path');

const LANGUAGES_MAP_LOOKUP_KEY = window.gtxConstants.LANGUAGES_MAP_LOOKUP_KEY;
const languagesMapLookupStorage = sessionStorage.getItem(LANGUAGES_MAP_LOOKUP_KEY);

if (!languagesMapLookupStorage && isInternalPath()) {
    fetch('/api/lookup/languages/map')
        .then((response) => response.json())
        .then((resJson) => {
            if (resJson?.status === 'success') {
                const data = Object.values(resJson.data);
                sessionStorage.setItem(LANGUAGES_MAP_LOOKUP_KEY, JSON.stringify(data));
            } else {
                throw new Error('Failed to fetch languages map');
            }
        })
        .catch((error) => {
            console.error('Request failed:', error);
        });
}
