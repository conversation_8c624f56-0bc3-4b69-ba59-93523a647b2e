const Sentry = require('@sentry/browser');

const mixpanel = require('lib/mixpanel');
const gtxConstants = require('@getrix/common/js/gtx-constants');
const isInternalPath = require('../../../commons/lib/is-internal-path');

class MixpanelManager {
    constructor() {
        this.localStorageKey = 'webPushEventSent';
    }

    initialize() {
        mixpanel.init();
    }

    track() {
        if (Boolean(gtxConstants('MIXPANEL_IDENTIFY_TRACKING')) && isInternalPath()) {
            const webPushEventSent = window.localStorage.getItem(this.localStorageKey);

            if (!webPushEventSent) {
                let permissionMapped = 'unsupported';

                if ('Notification' in window) {
                    switch (Notification.permission) {
                        case 'granted':
                            permissionMapped = 'active';
                            break;
                        case 'denied':
                            permissionMapped = 'consent denied';
                            break;
                        case 'default':
                            permissionMapped = 'waiting for consent';
                    }
                }

                const url = `/api/tracking/identify`;
                const data = {
                    webPushState: permissionMapped,
                };

                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data),
                })
                    .then((response) => {
                        if (!response.ok) {
                            // eslint-disable-next-line no-undef
                            if (globalVars.SENTRY_ENABLED) {
                                Sentry.captureException(new Error(`HTTP error! status: ${response.status}`));
                            }
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        window.localStorage.setItem(this.localStorageKey, 'true');
                    })
                    .catch((error) => {
                        // eslint-disable-next-line no-undef
                        if (globalVars.SENTRY_ENABLED) {
                            Sentry.captureException(error);
                        }
                        console.error('Request failed:', error);
                    });
            }
        }
    }
}

module.exports = MixpanelManager;
