const braze = require('@braze/web-sdk');
const gtxConstants = require('@getrix/common/js/gtx-constants');
const gtxLoggedUser = require('@getrix/common/js/gtx-logged-user');
const isInternalPath = require('../../../commons/lib/is-internal-path');

class BrazeManager {
    constructor() {}

    isLoggingEnabled() {
        const logEnabledUsers = gtxConstants('BRAZE_LOGGING_USERS')
            .split(',')
            .filter((item) => item);

        if (Number(gtxConstants('BRAZE_LOGGING')) === 1 && logEnabledUsers.length === 0) {
            return true;
        }

        if (Number(gtxConstants('BRAZE_LOGGING')) === 1 && logEnabledUsers.length > 0) {
            return logEnabledUsers.indexOf(gtxLoggedUser('agencyId')) !== -1;
        }

        return false;
    }

    initialize() {
        if (
            (Number(gtxConstants('BRAZE_IN_APP_MESSAGES')) || Number(gtxConstants('BRAZE_PUSH_NOTIFICATIONS'))) &&
            isInternalPath()
        ) {
            const brazeConfigs = {
                baseUrl: gtxConstants('BRAZE_SDK_ENDPOINT'),
                allowUserSuppliedJavascript: true,
                enableLogging: this.isLoggingEnabled(),
            };

            if (Number(gtxConstants('BRAZE_PUSH_NOTIFICATIONS'))) {
                brazeConfigs.serviceWorkerLocation = '/braze-service-worker.js';
            }

            braze.initialize(gtxConstants('BRAZE_API_KEY'), brazeConfigs);
            braze.changeUser(gtxLoggedUser('agentUuid'));

            if (Number(gtxConstants('BRAZE_IN_APP_MESSAGES'))) {
                braze.automaticallyShowInAppMessages();
            }

            braze.openSession();
        }
    }
}

module.exports = BrazeManager;
