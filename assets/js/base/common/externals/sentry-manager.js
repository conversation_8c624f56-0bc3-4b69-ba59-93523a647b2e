const Sentry = require('@sentry/browser');

class SentryManager {
    constructor() {}

    initialize() {
        // eslint-disable-next-line no-undef
        if (globalVars.SENTRY_ENABLED) {
            Sentry.init({
                // eslint-disable-next-line no-undef
                dsn: SENTRY_DSN,
                debug: true,
                // eslint-disable-next-line no-undef
                release: SENTRY_VERSION,
                // eslint-disable-next-line no-undef
                environment: globalVars.SENTRY_ENV,
                tracesSampleRate: 1.0,
                replaysSessionSampleRate: 0.1,
                replaysOnErrorSampleRate: 1.0,
                ignoreErrors: ['Failed to fetch', /^generic\/main\/[a-zA-Z0-9._-]+\/open-builder$/],
                integrations: [
                    Sentry.thirdPartyErrorFilterIntegration({
                        filterKeys: ['mls-site'],
                        behaviour: 'drop-error-if-contains-third-party-frames',
                    }),
                ],
            });
        }
    }
}

module.exports = SentryManager;
