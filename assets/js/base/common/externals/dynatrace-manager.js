const gtxConstants = require('@getrix/common/js/gtx-constants');
const gtxLoggedUser = require('@getrix/common/js/gtx-logged-user');
const isInternalPath = require('../../../commons/lib/is-internal-path');

class DynatraceManager {
    constructor() {}

    identifyUser() {
        window.addEventListener('load', () => {
            if (!isInternalPath() || gtxConstants('DYNATRACE_ENABLED') === 0 || typeof window.dtrum === 'undefined') {
                return;
            }
            // Identify univocally a user into Dynatrace RUMS
            window.dtrum.identifyUser(gtxLoggedUser('idAgente'));
        });
    }
}

module.exports = DynatraceManager;
