const gtxConstants = require('@getrix/common/js/gtx-constants');
const gtxLoggedUser = require('@getrix/common/js/gtx-logged-user');
const isInternalPath = require('../../../commons/lib/is-internal-path');

class BeamerManager {
    constructor() {
        this.selector = window.innerWidth < 1024 ? '#menu-link-news, #new-menu-link-news' : 'header-link-news';
        this.productId = gtxConstants('BEAMER_PRODUCT_ID');
        this.language = gtxConstants('DEFAULT_LANG');
        this.userName = gtxLoggedUser('name');
        this.userSurname = gtxLoggedUser('surname');
        this.userEmail = gtxLoggedUser('email');
        this.idAgent = gtxLoggedUser('idAgente');
    }

    initialize() {
        if (gtxConstants('BEAMER_NEWS') !== '1' || !isInternalPath()) {
            return;
        }

        /* eslint-disable camelcase */
        window.beamer_config = {
            product_id: this.productId,
            selector: this.selector,
            display: 'right',
            language: this.language.toUpperCase(),
            filter: 'admin',
            lazy: false,
            alert: true,
            //---------------Visitor Information---------------
            user_id: this.idAgent,
            user_firstname: this.userName,
            user_lastname: this.userSurname,
            user_email: this.userEmail,
        };

        const beamerScriptElement = document.createElement('script');
        beamerScriptElement.src = 'https://app.getbeamer.com/js/beamer-embed.js';
        beamerScriptElement.defer = true;
        document.body.appendChild(beamerScriptElement);
    }
}

module.exports = BeamerManager;
