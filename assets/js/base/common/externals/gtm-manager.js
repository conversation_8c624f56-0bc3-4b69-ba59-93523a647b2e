const { serialize, join } = require('@pepita/querystring');

class GtmManager {
    constructor() {
        this.isLoaded = false;
        if (typeof window !== 'undefined') {
            window.dataLayer = window.dataLayer || [];
        }
        this.delay =
            typeof requestIdleCallback !== 'undefined' ? requestIdleCallback : (fn) => requestAnimationFrame(fn);
    }

    load(config) {
        if (typeof window === 'undefined') {
            return Promise.resolve();
        }

        return new Promise((resolve) => {
            if (this.isLoaded) {
                resolve(undefined);
            } else if (config && config.id) {
                window.dataLayer.push({
                    'gtm.start': new Date().getTime(),
                    event: 'gtm.js',
                });

                const firstScriptElement = document.getElementsByTagName('script')[0];
                const tagManagerScriptElement = document.createElement('script');
                tagManagerScriptElement.async = true;
                tagManagerScriptElement.src = join(
                    'https://www.googletagmanager.com/gtm.js',
                    serialize({
                        id: config.id,
                        // eslint-disable-next-line camelcase
                        gtm_auth: config.auth,
                        // eslint-disable-next-line camelcase
                        gtm_preview: config.preview,
                    })
                );
                firstScriptElement.parentNode.insertBefore(tagManagerScriptElement, firstScriptElement);

                tagManagerScriptElement.onload = () => {
                    resolve(undefined);
                };

                this.isLoaded = true;
            }
        });
    }

    sendData(data) {
        if (typeof window !== 'undefined') {
            this.delay(() => {
                requestAnimationFrame(() => {
                    window.dataLayer.push(data);
                });
            });
        }
    }
}

module.exports = GtmManager;
