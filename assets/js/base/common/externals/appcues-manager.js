const gtxConstants = require('@getrix/common/js/gtx-constants');
const gtxLoggedUser = require('@getrix/common/js/gtx-logged-user');
const isInternalPath = require('../../../commons/lib/is-internal-path');

class AppcuesManager {
    constructor() {
        this.language = gtxConstants('DEFAULT_LANG');
        this.userName = gtxLoggedUser('name');
        this.userSurname = gtxLoggedUser('surname');
        this.userEmail = gtxLoggedUser('email');
        this.agentUuid = gtxLoggedUser('agentUuid');
        this.agencyId = gtxLoggedUser('agencyId');
    }

    initialize() {
        if (gtxConstants('APPCUES_ENABLED') !== '1' || !isInternalPath()) {
            return;
        }

        window.AppcuesSettings = {
            enableURLDetection: true,
        };

        const appCuesScriptElement = document.createElement('script');
        appCuesScriptElement.src = 'https://fast.appcues.com/140570.js';
        appCuesScriptElement.defer = true;
        document.body.appendChild(appCuesScriptElement);

        appCuesScriptElement.onload = () => {
            if (window.Appcues && typeof window.Appcues.identify === 'function') {
                window.Appcues.identify(this.agentUuid, {
                    // eslint-disable-next-line camelcase
                    first_name: this.userName,
                    agencyId: this.agencyId,
                    // eslint-disable-next-line camelcase
                    last_name: this.userSurname,
                    email: this.userEmail,
                    language: this.language,
                });
            }
        };
    }
}

module.exports = AppcuesManager;
