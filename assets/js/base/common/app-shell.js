const GtmManager = require('./externals/gtm-manager');
const AppcuesManager = require('./externals/appcues-manager');
const BeamerManager = require('./externals/beamer-manager');
const BrazeManager = require('./externals/braze-manager');
const MixpanelManager = require('./externals/mixpanel-manager');
const DynatraceManager = require('./externals/dynatrace-manager');
const SentryManager = require('./externals/sentry-manager');

// Polyfills
require('../../commons/polyfills/fill');
require('../../commons/polyfills/fromEntries');
require('../../commons/polyfills/toBlob');
require('../../commons/polyfills/resizeObserver');

// Global variables
require('@getrix/common/js/main');

// XHR and Fetch wrappers
require('@getrix/common/js/xhr-wrapper');
require('@getrix/common/js/fetch-wrapper');

// Ask help
require('./help');
// Get languages mapping
require('./get-languages');

// Google Tag Manager
const gtmManager = new GtmManager();
gtmManager.load({
    // eslint-disable-next-line no-undef
    id: globalVars.GTM_ID,
});

// Sentry initialization
const sentryManager = new SentryManager();
sentryManager.initialize();

// Appcues SDK
const appcuesManager = new AppcuesManager();
appcuesManager.initialize();

// Braze SDK
const beamerManager = new BeamerManager();
beamerManager.initialize();

// Dynatrace RUMS
const dynatraceManager = new DynatraceManager();
dynatraceManager.identifyUser();

// Braze SDK
let brazeManager = new BrazeManager();
brazeManager.initialize();

// Mixpanel SDK
const mixpanelManager = new MixpanelManager();
mixpanelManager.initialize();
mixpanelManager.track();
