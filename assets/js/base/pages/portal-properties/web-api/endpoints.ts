import { ENDPOINTS } from 'constants/property';

const PROPERTIES_PATH_PREFIX = '/immobili/portale';
export const BASE_PATH = `${PROPERTIES_PATH_PREFIX}/lista`;
export const PERFORMANCE_PATH = `${PROPERTIES_PATH_PREFIX}/:id/prestazioni`;
const API_AGENCIES_BASE_PATH = `/api/agencies`;
const API_AGENTS_BASE_PATH = `/api/agents`;
const API_PROPERTIES_BASE_PATH = `/api/properties`;
const API_PORTAL_PROPERTIES_BASE_PATH = `/api/portal-properties`;
const API_LOOKUP_BASE_PATH = '/api/lookup';
const PROPERTIES_LOOKUP_BASE_PATH = '/immobili/lookup';
const SEARCHES_BASE_PATH = '/api/searches';

export const endpoints = {
    AGENCY: `${API_AGENCIES_BASE_PATH}/agency`,
    AGENTS: `${API_AGENTS_BASE_PATH}`,
    ACTIVATE_PROPERTY: `${API_PORTAL_PROPERTIES_BASE_PATH}/{id}/activate`,
    ACTIVATE_PROPERTIES: `${API_PORTAL_PROPERTIES_BASE_PATH}/activate`,
    ARCHIVE_PROPERTY: `${API_PORTAL_PROPERTIES_BASE_PATH}/{id}/archive`,
    ARCHIVE_PROPERTIES: `${API_PORTAL_PROPERTIES_BASE_PATH}/archive`,
    CATEGORIES_LOOKUP: `${API_LOOKUP_BASE_PATH}/properties/categories`,
    CITIES_LOOKUP: `${PROPERTIES_LOOKUP_BASE_PATH}/get-cities`,
    COUNTRIES_LOOKUP: `${PROPERTIES_LOOKUP_BASE_PATH}/get-countries`,
    CONTRACTS_LOOKUP: `${PROPERTIES_LOOKUP_BASE_PATH}/get-contracts`,
    CONVERT_PROPERTY_TO_SOLD: `${API_PROPERTIES_BASE_PATH}/{id}/convert-to-sold`,
    CONVERT_PROPERTY_TO_RENTED: `${API_PROPERTIES_BASE_PATH}/{id}/convert-to-rented`,
    COUNT_PROPERTIES_BY_STATUS: `${API_PORTAL_PROPERTIES_BASE_PATH}/count-by-status`,
    CONTENTS: `${BASE_PATH}/contents`,
    DELETE_PROPERTY: `${API_PROPERTIES_BASE_PATH}/{id}`,
    DELETE_PROPERTIES: `${API_PROPERTIES_BASE_PATH}/delete`,
    DOWNLOAD_PROPERTIES_CSV: `/v2/annunci-portale/csv`,
    ECOMMERCE_PRODUCTS: `${API_AGENCIES_BASE_PATH}/ecommerce-products`,
    FAVOURITE_PROPERTY: `${API_PROPERTIES_BASE_PATH}/{id}/favourite`,
    PAGINATION_OPTIONS: `${API_LOOKUP_BASE_PATH}/pagination-options`,
    PROPERTY_ADD: ENDPOINTS.propertyAdd,
    PROPERTY_EDIT: `${ENDPOINTS.propertyAdd}?step={step}&idAnnuncio={id}`,
    PROPERTY_DETAIL: `${API_PROPERTIES_BASE_PATH}/{id}`,
    CAN_OBTAIN_CADASTRAL_DATA: `/youdomus/api/check-enabled/{id}`,
    PROPERTY_DETAIL_PRINT: `${PROPERTIES_PATH_PREFIX}/stampa/{id}`,
    PROPERTY_INTEGRATION_FLAG: `${API_PROPERTIES_BASE_PATH}/{id}/integration-flag`,
    PROPERTY_PERFORMANCES_LOOKUP: `${API_LOOKUP_BASE_PATH}/properties/performance-relative-index-range`,
    PROPERTY_PERFORMANCE_DETAILS: `${API_PROPERTIES_BASE_PATH}/{id}/performance`,
    PROPERTY_PREVIOUS_STATS: `${API_PROPERTIES_BASE_PATH}/stats`,
    PROPERTY_QUALITY_DETAILS: `${API_PROPERTIES_BASE_PATH}/{id}/quality-details`,
    PROPERTIES: `${API_PORTAL_PROPERTIES_BASE_PATH}/properties`,
    PROPERTIES_SPACES: `${API_PORTAL_PROPERTIES_BASE_PATH}/properties-spaces`,
    PROPERTIES_PERFORMANCE_DETAILS: `${API_PROPERTIES_BASE_PATH}/performance`,
    PROVINCES_LOOKUP: `${PROPERTIES_LOOKUP_BASE_PATH}/get-provinces`,
    REGIONS_LOOKUP: `${PROPERTIES_LOOKUP_BASE_PATH}/get-regions`,
    TOGGLE_PROPERTY_PREMIUM_VISIBILITY: `${API_PROPERTIES_BASE_PATH}/{id}/visibilities`,
    VALIDATION_ERRORS: `${API_PROPERTIES_BASE_PATH}/validation-errors`,
    ZONES_LOOKUP: `${PROPERTIES_LOOKUP_BASE_PATH}/get-zones-localities`,
    MATCHES_AND_THREADS_COUNTERS: `${SEARCHES_BASE_PATH}/matches/ads/stats?propertyIds={ids}`,
};
