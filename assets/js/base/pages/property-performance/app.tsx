import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { FeaturesReady, useFeatureIsOn } from '@growthbook/growthbook-react';
import { createPortal } from 'react-dom';
import { BrowserRouter, Route, Routes, useParams } from 'react-router-dom';

import {
    MswDeveloperBoundary,
    onlyInDevelopment,
} from 'gtx-react/components/MswDeveloperBoundary';
import {
    REACT_QUERY_DEV_TOOLS_CLOSE_BTN,
    REACT_QUERY_DEV_TOOLS_TOGGLE_BTN,
} from 'gtx-react/constants';
import { Header } from 'gtx-react/containers/Header';
import { MixpanelProvider } from 'gtx-react/contexts/MixpanelProvider';
import { CheckPropertyPerformance } from './components/CheckPropertyPerformance';
import { PrintProvider } from './components/PrintProvider';
import { MIXPANEL_CONFIG } from './constants';
import { PERFORMANCE_PATH } from './web-api/endpoints';
import { createQueryClient } from 'lib/queryClient';
import { NotifyProvider } from '@gx-design/snackbar';
import { Loader } from '@gx-design/loader';
import { GrowthBookProvider } from 'gtx-react/components/GrowthBookProvider';
import { SidebarViewWrapper } from './components/Sidebar/SidebarViewWrapper';
import { getAppLocale } from 'lib/languages';
import { ConfigProvider } from '../unified-properties/components/ConfigProvider';
import { featureExists } from '@getrix/common/js/featureToggleConfigs';
import { DialogsProvider } from '../unified-properties/components/dialogs';
import { LoggedUserProvider } from '../unified-properties/components/LoggedUserProvider';
import { PropertyPerformanceHeader } from './components/PropertyPerformanceHeader';

const worker = onlyInDevelopment(() => import('./utils/worker'));

const queryClient = createQueryClient({
    defaultOptions: {
        queries: {
            staleTime: 600 * 1000,
        },
    },
});

// headersx
const DefaultTopHeaderPortal = () =>
    createPortal(<Header />, document.getElementById('header'));

// performance
const PerformanceContentPortal = ({
    propertyId,
    onBack,
    performancesData,
    isPropertiesListFeatureOn,
    isPerformanceLeftSidebarOn,
}) => {
    const isSecretPropertyEnabled = useFeatureIsOn('crm_secret_property');

    return createPortal(
        <NotifyProvider>
            <PrintProvider>
                <QueryClientProvider client={queryClient}>
                    <ConfigProvider
                        value={{
                            appLocale: getAppLocale(),
                            allExtraVisibilityEnabled: Boolean(
                                window.gtxConstants
                                    .VISIBILITIES_ALL_EXTRA_VISIBILITIES_PRODUCT
                            ),
                            secretPropertyEnabled: Boolean(
                                isSecretPropertyEnabled
                            ),
                            hasConvertibleAd: featureExists('convertible_ad'),
                            isGetrix: window.gtxConstants.GETRIX_MODULE_ENABLED,
                            firstPageTagThreshold:
                                window.gtxConstants.FIRST_PAGE_TAG_THRESHOLD,
                            shouldShowFirstPageTag:
                                window.gtxConstants.SHOW_FIRST_PAGE_TAG,
                            cockadeIconThreshold:
                                window.gtxConstants.COCKADE_ICON_THRESHOLD,
                            adPortal: window.gtxConstants.AD_PORTAL,
                            performanceFeatureEnabled: Boolean(
                                window.gtxConstants.PROPERTY_LIST_PERFORMANCE
                            ),
                        }}
                    >
                        <ReactQueryDevtools
                            toggleButtonProps={REACT_QUERY_DEV_TOOLS_TOGGLE_BTN}
                            closeButtonProps={REACT_QUERY_DEV_TOOLS_CLOSE_BTN}
                            initialIsOpen={false}
                        />
                        <LoggedUserProvider
                            value={{
                                isFromBackoffice:
                                    window.gtxLoggedUser.roles
                                        ?.ROLE_USER_BACKOFFICE ?? false,
                            }}
                        >
                            {isPropertiesListFeatureOn &&
                            isPerformanceLeftSidebarOn ? (
                                <SidebarViewWrapper
                                    propertyId={propertyId}
                                    actions={performancesData}
                                >
                                    {propertyId && (
                                        <CheckPropertyPerformance
                                            propertyId={propertyId}
                                            minimal={!performancesData}
                                        />
                                    )}
                                </SidebarViewWrapper>
                            ) : undefined}
                            {!isPropertiesListFeatureOn ||
                            !isPerformanceLeftSidebarOn ? (
                                <>
                                    <PropertyPerformanceHeader
                                        onBack={onBack}
                                        propertyId={propertyId}
                                        actions={performancesData}
                                    />
                                    {propertyId && (
                                        <CheckPropertyPerformance
                                            propertyId={propertyId}
                                            minimal={!performancesData}
                                        />
                                    )}{' '}
                                </>
                            ) : undefined}
                            <DialogsProvider />
                        </LoggedUserProvider>
                    </ConfigProvider>
                </QueryClientProvider>
            </PrintProvider>
        </NotifyProvider>,
        document.getElementById('content')
    );
};

function Performance() {
    const params = useParams();
    const isPropertiesListFeatureOn = useFeatureIsOn('crm_properties_list');
    const isPerformanceLeftSidebarOn = useFeatureIsOn(
        'crm_performance_left_sidebar'
    );

    function handleBack() {
        if (window.history.length > 1) {
            window.history.back();
        } else {
            window.close();
        }
    }

    const hasSimilarProperties = () => {
        let fewSimilarPropertiesId = localStorage.getItem(
            'fewSimilarPropertiesId'
        );

        if (!fewSimilarPropertiesId) {
            return true;
        }

        return fewSimilarPropertiesId !== params.id;
    };

    return (
        <MixpanelProvider mixpanelConfig={MIXPANEL_CONFIG}>
            <DefaultTopHeaderPortal />
            <PerformanceContentPortal
                propertyId={params.id}
                onBack={handleBack}
                performancesData={hasSimilarProperties()}
                isPropertiesListFeatureOn={isPropertiesListFeatureOn}
                isPerformanceLeftSidebarOn={isPerformanceLeftSidebarOn}
            />
        </MixpanelProvider>
    );
}

function EntryPoint() {
    return (
        <MswDeveloperBoundary isDisabled worker={worker}>
            <GrowthBookProvider>
                <FeaturesReady timeout={500} fallback={<Loader />}>
                    <BrowserRouter>
                        <Routes>
                            <Route
                                path={PERFORMANCE_PATH}
                                Component={Performance}
                            />
                        </Routes>
                    </BrowserRouter>
                </FeaturesReady>
            </GrowthBookProvider>
        </MswDeveloperBoundary>
    );
}

export default EntryPoint;
