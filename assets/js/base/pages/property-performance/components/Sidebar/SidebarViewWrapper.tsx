import { Button } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { useMediaMatch } from '@gx-design/use-media-match';
import { trans } from '@pepita-i18n/babelfish';
import { FC, PropsWithChildren } from 'react';

import { SidebarLeft } from 'gtx-react/components/Sidebar/Sidebar';
import { PAGE_WRAPPER_ID } from '../../constants';
import { SidebarContentWithSuspense } from './SidebarContentWithSuspense';
import { SidebarHeader } from './SidebarHeader';

interface SidebarViewWrapperProps {
    propertyId: string;
    actions: any;
}
export const SidebarViewWrapper: FC<
    PropsWithChildren<SidebarViewWrapperProps>
> = ({ children: viewComponent, propertyId, actions }) => {
    const isDesktopVersion = useMediaMatch('largeDesktop');

    const handleBack = () => {
        window.close();
    };

    return (
        <SidebarLeft initialValue={true} section="property-performance">
            <div className="crm-section">
                <SidebarLeft.Bar>
                    <div className="crm-sidebar__header">
                        <Button
                            size="small"
                            variant="ghost"
                            iconOnly
                            onClick={handleBack}
                        >
                            <Icon name="arrow-long-left" />
                        </Button>
                        <h3 className="gx-title-2">
                            {trans('label.back_to_the_list')}
                        </h3>
                    </div>
                    <div className="crm-sidebar__content">
                        <SidebarContentWithSuspense propertyId={propertyId} />
                    </div>
                </SidebarLeft.Bar>
                <div className="crm-section__content" id={PAGE_WRAPPER_ID}>
                    {isDesktopVersion && (
                        <div className="crm-section__contentHeader">
                            <SidebarHeader
                                propertyId={propertyId}
                                actions={actions}
                                onBack={handleBack}
                            />
                        </div>
                    )}
                    {viewComponent}
                </div>
            </div>
        </SidebarLeft>
    );
};
