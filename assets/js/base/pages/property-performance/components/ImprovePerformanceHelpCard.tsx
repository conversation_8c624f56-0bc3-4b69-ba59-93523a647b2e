import gtxConstants from 'gtx-constants';
import { trans } from '@pepita-i18n/babelfish';
import { Icon } from '@gx-design/icon';
import { Avatar } from '@gx-design/avatar';

type Responsible = {
    imageId?: string;
    email?: string;
    name?: string;
    phone?: string;
};

const RESPONSIBLE_AVATAR_PLACEHOLDER_URL =
    '/bundles/base/getrix/common/img/customerservice.png';

export const ImprovePerformanceHelpCard: React.FC = () => {
    const agency = JSON.parse(document.getElementById('agency-info').innerHTML);

    const responsible: Responsible = {
        imageId: agency?.responsabile?.immagineResponsabile,
        email: agency?.responsabile?.email,
        name: agency?.responsabile?.nome,
        phone: agency?.responsabile?.telefono,
    };

    const avatar = responsible?.imageId
        ? `${gtxConstants('VHOST_URL_MEDIA_IMAGE')}${gtxConstants(
              'RESPONSIBLE_LOGO_ENDPOINT'
          )}/${responsible.imageId}/thumb.jpg`
        : RESPONSIBLE_AVATAR_PLACEHOLDER_URL;

    return responsible && Object.keys(responsible)?.length ? (
        <div className="gx-card gx-card--rounded">
            <div className="gx-card__content">
                <div className="gx-title-2 performance-row__title">
                    {trans('label.property_performance.help_to_improve')}
                </div>
                <p className="gx-body-small">
                    {trans('label.property_performance.referent')}
                </p>
                <div className="referent-card">
                    <Avatar
                        avatarImage={avatar}
                        size="big"
                        altText={`${responsible?.name}`}
                    />
                    <div className="referent-card__content">
                        <div className="referent-card__name">
                            {responsible?.name}
                        </div>
                        {responsible?.phone && (
                            <div className="referent-card__contact">
                                <Icon name="phone" />
                                {responsible.phone}
                            </div>
                        )}
                        {responsible?.email && (
                            <div className="referent-card__contact">
                                <Icon name="mail" />
                                {responsible.email}
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    ) : null;
};
