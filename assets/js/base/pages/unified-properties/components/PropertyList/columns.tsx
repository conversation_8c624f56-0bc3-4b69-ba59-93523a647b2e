import { Checkbox } from '@gx-design/checkbox';
import { trans } from '@pepita-i18n/babelfish';
import { createColumnHelper } from '@tanstack/react-table';
import { useMemo } from 'react';
import { MemoActionsCell } from './cells/ActionsCell/ActionsCell';
import { MemoExtravisibilityCell } from './cells/ExtravisibilityCell';
import { MemoIdCell } from './cells/IdCell/IdCell';
import MatchesCell from './cells/MatchesCell';
import { MemoPerformanceCell } from './cells/PerformanceCell';
import { PositionCell } from './cells/PositionCell';
import { MemoStatsCell } from './cells/StatsCell';
import { MemoThreadsCell } from './cells/ThreadsCell';
import { Property } from './types';
import { MemoQualityCell } from './cells/QualityCell/QualityCell';
import { List, ListItem } from '@gx-design/list';
import { PropertyStatus } from '../../types';
import { MemoSoldActionsCell } from './cells/SoldActionsCell';

export const useColumns = (status: PropertyStatus) => {
    const columns = useMemo(() => {
        const columnHelper = createColumnHelper<Property>();

        const idAccessor = columnHelper.accessor('id', {
            minSize: 200,
            size: 380,
            id: 'code',
            header: ({ column, table }) => (
                <>
                    <div
                        className="crm-property-item__tools"
                        onClick={(evt) => {
                            evt.stopPropagation();
                        }}
                    >
                        <Checkbox
                            aria-label={trans('label.select_all')}
                            indeterminate={
                                table.getIsSomeRowsSelected() &&
                                !table.getIsAllRowsSelected()
                            }
                            checked={
                                table.getSelectedRowModel().rows.length > 0
                            }
                            onChange={() => {
                                table.toggleAllRowsSelected();
                            }}
                        />
                    </div>
                    <div className="crm-cell-id">
                        <span className="gx-text-ellipsis">
                            {trans('label.reference')}
                        </span>
                    </div>
                </>
            ),
            cell(props) {
                return (
                    <div className="crm-cell-id">
                        <MemoIdCell key={props.row.original.id} {...props}>
                            <Checkbox
                                key={props.row.original.id}
                                // TODO: add translation
                                aria-label={`Select ${props.cell.row.original.id}`}
                                checked={props.cell.row.getIsSelected()}
                                onChange={() => props.cell.row.toggleSelected()}
                            />
                        </MemoIdCell>
                    </div>
                );
            },
            enablePinning: true,
        });
        const contractAccessor = columnHelper.accessor('contract', {
            enableSorting: false,
            minSize: 100,
            size: 150,
            header() {
                return trans('label.contract');
            },
        });
        const locationAccessor = columnHelper.accessor('location', {
            enableSorting: true,
            id: 'city',
            minSize: 100,
            size: 200,
            meta: {
                label: trans('label.place'),
            },
            header() {
                return trans('label.place');
            },
            cell: ({ cell }) => {
                const value = cell.getValue();

                if (!value?.length) {
                    return '---';
                }

                return (
                    <List className="crm-table__ellipsisWrapper">
                        {value.map((location, index) => (
                            <ListItem
                                key={index}
                                content={location}
                                className="gx-text-ellipsis"
                            />
                        ))}
                    </List>
                );
            },
        });
        const searchPositionAccessor = columnHelper.accessor('searchPosition', {
            minSize: 100,
            size: 160,
            cell: PositionCell,
            header() {
                return trans('label.position');
            },
            meta: {
                label: trans('label.position'),
            },
        });
        const priceAccessor = columnHelper.accessor('price', {
            minSize: 100,
            size: 120,
            header() {
                return trans('label.price');
            },
            cell: ({ cell }) => {
                const value = cell.getValue();

                if (!value || !value.available) {
                    return '---';
                }

                if (value.isByRequest) {
                    return (
                        <List>
                            <ListItem content={value.price} />
                            <ListItem
                                content={
                                    <span>{trans('label.on_request')}</span>
                                }
                            />
                        </List>
                    );
                }

                return <span className="gx-text-ellipsis">{value.price}</span>;
            },
        });
        const surfaceAccessor = columnHelper.accessor('surface', {
            minSize: 100,
            size: 120,
            header() {
                return trans('label.surface');
            },
            cell: ({ cell }) => {
                const value = cell.getValue();

                return <span className="gx-text-ellipsis">{value}</span>;
            },
        });
        const extraVisibilitiesAccessor = columnHelper.accessor(
            'extraVisibilities',
            {
                enableSorting: false,
                minSize: 100,
                size: 200,
                meta: {
                    label: trans('label.visibility'),
                },
                header() {
                    return trans('label.visibility');
                },
                cell: MemoExtravisibilityCell,
            }
        );
        const qualityAccessor = columnHelper.accessor('quality', {
            minSize: 100,
            size: 150,
            id: 'ranking',
            meta: {
                label: trans('label.quality'),
            },
            header() {
                return trans('label.quality');
            },
            cell: MemoQualityCell,
        });
        const performanceAccessor = columnHelper.accessor('performance', {
            minSize: 100,
            size: 200,
            id: 'absoluteIndex',
            meta: {
                label: trans('label.performances'),
            },
            header() {
                return trans('label.performances');
            },
            cell: MemoPerformanceCell,
        });
        const statsAccessor = columnHelper.accessor('stats', {
            enableSorting: false,
            minSize: 100,
            meta: {
                label: trans('label.statistics'),
            },
            header() {
                return trans('label.statistics');
            },
            cell: MemoStatsCell,
        });

        const messagesAccessor = columnHelper.accessor('messages', {
            enableSorting: false,
            minSize: 100,
            size: 100,
            header() {
                return trans('label.messages');
            },
            cell: MemoThreadsCell,
        });

        const matchAccessor = columnHelper.accessor('match', {
            enableSorting: false,
            minSize: 100,
            size: 100,
            meta: {
                label: trans('label.matches'),
            },
            header() {
                return trans('label.matches');
            },
            cell: MatchesCell,
        });
        const lastUpdateDateAccessor = columnHelper.accessor('lastUpdateDate', {
            minSize: 100,
            size: 180,
            id: 'modified',
            meta: {
                label: trans('label.last_update_date'),
            },
            header() {
                return trans('label.last_update_date');
            },
            cell: ({ cell }) => {
                const value = cell.getValue();

                return <span className="gx-text-ellipsis">{value}</span>;
            },
        });
        const actionsCell = columnHelper.display({
            enableResizing: false,
            id: 'actions',
            cell: MemoActionsCell,
        });
        const soldActionsCell = columnHelper.display({
            id: 'actions',
            cell: MemoSoldActionsCell,
        });

        switch (status) {
            case 'sold':
                return [
                    idAccessor,
                    locationAccessor,
                    priceAccessor,
                    surfaceAccessor,
                    lastUpdateDateAccessor,
                    soldActionsCell,
                ];
            default:
                return [
                    idAccessor,
                    extraVisibilitiesAccessor,
                    contractAccessor,
                    locationAccessor,
                    searchPositionAccessor,
                    priceAccessor,
                    surfaceAccessor,
                    qualityAccessor,
                    performanceAccessor,
                    statsAccessor,
                    messagesAccessor,
                    matchAccessor,
                    lastUpdateDateAccessor,
                    actionsCell,
                ];
        }
    }, [status]);

    return columns;
};

export type PropertyColumns = ReturnType<typeof useColumns>;
