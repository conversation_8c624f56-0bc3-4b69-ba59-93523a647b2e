import { Button } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { useNotifyContext } from '@gx-design/snackbar';
import { Tooltip } from '@gx-design/tooltip';
import { trans } from '@pepita-i18n/babelfish';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useMixpanelContext } from 'gtx-react/hooks/useMixpanelContext';
import { activateProperties } from 'lib/REST/requests/api/portal-properties';
import { propertiesOptions } from 'lib/REST/requests/api/properties/query-factory';
import { ucFirst } from 'lib/strings-formatter';
import { PropertyStatus } from '../../types';

export default function PropertyActivateBulkButton(props: {
    status: PropertyStatus;
    ids: Array<number>;
    toggleAllRowsSelected: () => void;
}) {
    const { trackEvent } = useMixpanelContext();
    const { showNotification } = useNotifyContext();
    const queryClient = useQueryClient();

    const activatePropertiesMutation = useMutation({
        mutationFn: activateProperties,
        mutationKey: ['activate-bulk'],
        onSuccess: (_, variables) => {
            const isPlural = variables.query.ids.length > 1;

            showNotification({
                type: 'success',
                message: trans(
                    `ad.activated_success${isPlural ? '_plural' : ''}`
                ),
            });

            queryClient.invalidateQueries({
                queryKey: propertiesOptions,
                exact: false,
            });
        },
        onError: () => {
            queryClient.invalidateQueries({
                queryKey: propertiesOptions,
                exact: false,
            });

            showNotification({
                type: 'error',
                message: trans('label.error_durin_operation'),
            });
        },
    });

    return (
        <Tooltip text={trans('label.restore')}>
            <Button
                onClick={() => {
                    props.toggleAllRowsSelected();
                    activatePropertiesMutation.mutate({
                        query: { ids: props.ids },
                    });

                    props.ids.map((id) => {
                        trackEvent({
                            event: 'properties_unarchive_listing',
                            extra: {
                                ['property_id']: id,
                                ['bulk_action']: true,
                                ['list_view']: ucFirst(props.status),
                            },
                        });
                    });
                }}
                iconOnly
                size="small"
                variant="ghost"
                aria-label={trans('label.restore')}
            >
                <Icon name="check"></Icon>
            </Button>
        </Tooltip>
    );
}
