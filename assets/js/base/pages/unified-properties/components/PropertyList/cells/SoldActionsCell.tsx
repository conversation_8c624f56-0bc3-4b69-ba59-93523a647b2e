import { ActionList, ActionListItem } from '@gx-design/action-list';
import { Button } from '@gx-design/button';
import { Dropdown } from '@gx-design/dropdown';
import { Icon } from '@gx-design/icon';
import { Tooltip } from '@gx-design/tooltip';
import { trans } from '@pepita-i18n/babelfish';
import { CellContext } from '@tanstack/react-table';
import { useMixpanelContext } from 'gtx-react/hooks/useMixpanelContext';
import { Fragment, memo } from 'react';
import { useConfigContext } from '../../ConfigProvider';
import { useOpenPropertyConvertFromDialog } from '../../dialogs/PropertyConvertFromDialog/PropertyConvertFromDialog';
import { useOpenPropertyDeleteDialog } from '../../dialogs/PropertyDeleteDialog';
import { Property } from '../types';
import { useArchivePropertyMutation } from './ActionsCell/useArchivePropertyMutation';
import { ucFirst } from 'lib/strings-formatter';

export function SoldActionsCell(props: CellContext<Property, unknown>) {
    const { open: openConvertFromDialog } = useOpenPropertyConvertFromDialog();
    const { trackEvent } = useMixpanelContext();
    const { open: openDeleteDialog } = useOpenPropertyDeleteDialog();
    const { hasConvertibleAd } = useConfigContext();

    const archivePropertiesMutation = useArchivePropertyMutation();

    return (
        <div className="crm-cell-actions">
            {props.row.original.portalUrl &&
            props.row.original.status === 'portal_name' ? (
                <Tooltip text={trans('label.preview')}>
                    <Button
                        as="a"
                        href={props.row.original.portalUrl}
                        target="_blank"
                        iconOnly
                        size="small"
                    >
                        <Icon name="eye" />
                    </Button>
                </Tooltip>
            ) : (
                <Fragment />
            )}

            <Tooltip text={trans('label.edit')}>
                <Button
                    as="a"
                    href={`/inserimento_venduto_affittato.php?idAnnuncio=${props.row.original.id}&step=5&tipo=1`}
                    target="_blank"
                    iconOnly
                    size="small"
                >
                    <Icon name="pencil" />
                </Button>
            </Tooltip>

            <Dropdown
                buttonIsIconOnly={true}
                buttonContent={
                    <Tooltip
                        text={trans('label.other_actions')}
                        position="topRight"
                    >
                        <Icon name="ellipsis" />
                    </Tooltip>
                }
                position="bottomRight"
                buttonSize="small"
                showCaret={false}
            >
                <ActionList>
                    {/* convert to sold  */}
                    {hasConvertibleAd &&
                    props.row.original.isPropertySoldable ? (
                        <ActionListItem
                            text={trans('label.convert_from_sold')}
                            onClick={() => {
                                openConvertFromDialog({
                                    id: props.row.original.id,
                                    type: 'REQUIRE_SOLD_CONFIRMATION',
                                });
                            }}
                        />
                    ) : (
                        <Fragment />
                    )}
                    {/* convert to rent  */}
                    {hasConvertibleAd &&
                    props.row.original.isPropertyRentable ? (
                        <ActionListItem
                            text={trans('label.convert_from_rented')}
                            onClick={() => {
                                openConvertFromDialog({
                                    id: props.row.original.id,
                                    type: 'REQUIRE_RENT_CONFIRMATION',
                                });
                            }}
                        />
                    ) : (
                        <Fragment />
                    )}

                    {/* Delete */}
                    <ActionListItem
                        text={trans('label.remove')}
                        onClick={() => {
                            openDeleteDialog([props.row.original.id]);
                        }}
                    />

                    <ActionListItem
                        onClick={() => {
                            archivePropertiesMutation.mutate({
                                query: { ids: [props.row.original.id] },
                            });

                            trackEvent({
                                event: 'properties_archive_listing',
                                extra: {
                                    ['property_id']: props.row.original.id,
                                    ['bulk_action']: false,
                                    ['list_view']: ucFirst(
                                        props.row.original.filterStatus
                                    ),
                                },
                            });
                        }}
                        text={trans('label.archive')}
                    />
                </ActionList>
            </Dropdown>
        </div>
    );
}

export const MemoSoldActionsCell = memo(SoldActionsCell);
