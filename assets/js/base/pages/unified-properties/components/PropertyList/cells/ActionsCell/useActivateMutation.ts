import { useNotifyContext } from '@gx-design/snackbar';
import { trans } from '@pepita-i18n/babelfish';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { activateProperties } from 'lib/REST/requests/api/portal-properties';
import { propertiesOptions } from 'lib/REST/requests/api/properties/query-factory';

export function useActivatePropertyMutation() {
    const { showNotification } = useNotifyContext();
    const queryClient = useQueryClient();

    return useMutation({
        mutationKey: ['activate-bulk'],
        mutationFn: activateProperties,
        onSuccess: () => {
            showNotification({
                type: 'success',
                message: trans('ad.activated_success'),
            });

            queryClient.invalidateQueries({ queryKey: propertiesOptions });
        },
        onError: () => {
            showNotification({
                type: 'error',
                message: trans('label.error_durin_operation'),
            });
        },
    });
}
