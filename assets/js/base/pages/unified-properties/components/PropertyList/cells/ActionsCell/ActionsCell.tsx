import {
    ActionList,
    ActionListItem,
    ActionListItemProps,
} from '@gx-design/action-list';
import { But<PERSON> } from '@gx-design/button';
import { Popover } from '@gx-design/popover';
import { Dropdown } from '@gx-design/dropdown';
import { ContentDropdown } from 'gtx-react/components/ContentDropdown/ContentDropdown';
import { ContentDropdownContext } from 'gtx-react/components/ContentDropdown/useContentDropdown';
import { Icon } from '@gx-design/icon';
import { Tooltip } from '@gx-design/tooltip';
import { trans } from '@pepita-i18n/babelfish';
import { useQuery, useSuspenseQuery } from '@tanstack/react-query';
import { CellContext } from '@tanstack/react-table';
import { useMixpanelContext } from 'gtx-react/hooks/useMixpanelContext';
import { propertySpacesQueryOptions } from 'lib/REST/requests/api/portal-properties/query-factory';
import { createElement, Fragment, memo, useState } from 'react';
import { useConfigContext } from '../../../ConfigProvider';
import { useOpenDataVariationDialog } from '../../../dialogs/DataVariationDialog';
import { useOpenPropertyConvertToRentDialog } from '../../../dialogs/PropertyConvertToRentDialog';
import { useOpenPropertyConvertToSoldDialog } from '../../../dialogs/PropertyConvertToSoldDialog';
import { useOpenPropertyDeleteDialog } from '../../../dialogs/PropertyDeleteDialog';
import { useOpenSendAdEmailDialog } from '../../../dialogs/SendAdEmailDialog/SendAdEmailDialog';
import { Property } from '../../types';
import { PerformanceLink } from './PerformanceLink';
import { useArchivePropertyMutation } from './useArchivePropertyMutation';
import { useChangeVisibilityMutation } from './useChangeVisibilityMutation';
import { useDuplicatePropertyMutation } from './useDuplicatePropertyMutation';
import { useActivatePropertyMutation } from './useActivateMutation';
import { useHandlePrintDetailFetch } from './print/useHandlePrintDetailFetch';
import { useOpenPropertyDetailDialog } from '../../../dialogs/PropertyDetailDialog/PropertyDetailDialog';
import { convertCase, ucFirst } from 'lib/strings-formatter';
import { getMeQueryOptions } from 'lib/REST/requests/rest/profile/query-factory';
import { ShareButton } from 'gtx-react/components/ShareButton';
import { UnlockGetrixPopoverContent } from '../../UnlockGetrixPopoverContent';

export function ActionsCell(props: CellContext<Property, unknown>) {
    if (props.row.original.isTuristic) {
        return <TuristicActions {...props} />;
    }

    return createElement(MemoRegularActions, props);
}

function RegularActions(props: CellContext<Property, unknown>) {
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const { open: openConvertToSoldDialog } =
        useOpenPropertyConvertToSoldDialog();
    const { open: openConvertToRentDialog } =
        useOpenPropertyConvertToRentDialog();
    const { trackEvent } = useMixpanelContext();
    const { open: openDeleteDialog } = useOpenPropertyDeleteDialog();
    const { open: openDataVariationDialog } = useOpenDataVariationDialog();
    const { open: openDetailDialog } = useOpenPropertyDetailDialog();
    const { open: openSendAdEmailDialog } = useOpenSendAdEmailDialog();
    const { mutate } = useDuplicatePropertyMutation();
    const { isGetrix, hasConvertibleAd } = useConfigContext();
    const handlePrintDetail = useHandlePrintDetailFetch();

    const archivePropertiesMutation = useArchivePropertyMutation();
    const activatePropertiesMutation = useActivatePropertyMutation();

    const changeVisibilityMutation = useChangeVisibilityMutation();

    const propertySpacesQuery = useQuery({
        ...propertySpacesQueryOptions,
        select: (data) =>
            data.find(
                (space) => space.type.id === props.row.original.contractId
            )?.available,
    });

    // Payload for extra visibilities exists only if the type is default
    const visibilities = props.row.original.extraVisibilities?.payload;

    const { data: newConstructionIsDisabled } = useSuspenseQuery({
        ...getMeQueryOptions,
        select: (data) => !data.modules['Nuove Costruzioni'].enabled,
    });

    // if getrix and
    const list = {
        'first-level': [
            //  Extravisibilities
            !['archived', 'draft'].includes(props.row.original.filterStatus) &&
            visibilities &&
            !(
                props.row.original.type === 'new_construction' &&
                isGetrix &&
                newConstructionIsDisabled
            )
                ? {
                      text: `${trans(
                          `label.${
                              visibilities.searchable ||
                              visibilities.sky ||
                              !propertySpacesQuery.data
                                  ? 'deactivate'
                                  : 'activate'
                          }`
                      )} ${window.gtxConstants.PREMIUM_VISIBILITY_NAME}`,
                      onClick: () => {
                          changeVisibilityMutation.mutate({
                              params: {
                                  id: props.row.original.id,
                              },
                              query: { status: !visibilities.searchable },
                          });
                          trackEvent({
                              event: visibilities.searchable
                                  ? 'properties_deactivate_premium_on_listing'
                                  : 'properties_activate_premium_on_listing',
                              extra: {
                                  ['property_id']: props.row.original.id,
                                  ['list_view']: ucFirst(
                                      props.row.original.filterStatus
                                  ),
                              },
                          });
                      },
                  }
                : undefined,
            // convert to sold
            hasConvertibleAd && props.row.original.isPropertySoldable
                ? {
                      text: trans('label.convert_to_sold'),
                      onClick: () => {
                          openConvertToSoldDialog({
                              id: props.row.original.id,
                              type: 'REQUIRE_CONFIRMATION',
                          });
                      },
                  }
                : undefined,
            // convert to rent
            hasConvertibleAd && props.row.original.isPropertyRentable
                ? {
                      text: trans('label.convert_to_rented'),
                      onClick: () => {
                          openConvertToRentDialog({
                              id: props.row.original.id,
                              type: 'REQUIRE_CONFIRMATION',
                          });
                      },
                  }
                : undefined,
            // Duplicate
            props.row.original.type !== 'auction' &&
            props.row.original.type !== 'new_construction'
                ? {
                      text: trans('label.duplicate'),
                      onClick: () => {
                          mutate({
                              query: {
                                  id: props.row.original.id,
                              },
                          });
                      },
                      isDisabled: !isGetrix,
                  }
                : undefined,
        ],
        'second-level': [
            // Preview
            props.row.original.portalUrl &&
            props.row.original.status === 'portal_name'
                ? {
                      onClick: () => {
                          trackEvent({
                              event: 'properties_open_preview_listing',
                              extra: {
                                  ['property_id']: props.row.original.id,
                              },
                          });
                      },
                      target: '_blank',
                      href: props.row.original.portalUrl,
                      text: trans('label.see_preview'),
                  }
                : undefined,
            // Plans
            props.row.original.type !== 'new_construction'
                ? {
                      text: trans('label.plans'),
                      isDisabled:
                          !props.row.original.planimetryUrl || !isGetrix,
                      href: props.row.original.planimetryUrl ?? undefined,
                      target: '_blank',
                  }
                : undefined,
            // Virtual tour
            props.row.original.type !== 'new_construction'
                ? {
                      text: trans('label.virtual_tour'),
                      isDisabled:
                          !props.row.original.virtualTourUrl || !isGetrix,
                      href: props.row.original.virtualTourUrl ?? undefined,
                      target: '_blank',
                  }
                : undefined,
            // Data variation
            {
                text: trans('label.data_variation'),
                onClick: () => {
                    openDataVariationDialog(props.row.original.id.toString());
                },
                isDisabled: !isGetrix,
            },
        ],
        'third-level': [
            // Create billboard
            props.row.original.type === 'property'
                ? {
                      text: trans(`label.create_billboard`),
                      href: `/immobili/${props.row.original.id}/cartellone`,
                      isDisabled: !isGetrix,
                  }
                : undefined,
            // Download to PDF
            props.row.original.type === 'property'
                ? {
                      text: trans('label.download_pdf'),
                      href: `/annuncioPDFGenerator.php?id=${props.row.original.id}&display=pdf2`,
                      isDisabled: !isGetrix,
                  }
                : undefined,
            // Print
            {
                text: trans('label.print'),
                onClick: () => {
                    trackEvent({
                        event: 'properties_print_listing',
                        extra: {
                            ['property_id']: props.row.original.id,
                        },
                    });
                    const category =
                        props.row.original.type === 'new_construction'
                            ? 'newConstruction'
                            : props.row.original.type;

                    handlePrintDetail(
                        props.row.original.id.toString(),
                        category
                    );
                },
            },
            // Delete
            {
                text: trans('label.remove'),
                onClick: () => {
                    openDeleteDialog([props.row.original.id]);
                },
            },
            // Activate or Archive
            // Activate
            props.row.original.filterStatus === 'archived'
                ? {
                      text: trans('label.activate'),
                      onClick: () => {
                          activatePropertiesMutation.mutate({
                              query: {
                                  ids: [props.row.original.id],
                              },
                          });

                          trackEvent({
                              event: 'properties_unarchive_listing',
                              extra: {
                                  ['property_id']: props.row.original.id,
                                  ['bulk_action']: false,
                                  ['list_view']: ucFirst(
                                      props.row.original.filterStatus
                                  ),
                              },
                          });
                      },
                  }
                : // Archive
                  {
                      text: trans('label.archive'),
                      onClick: () => {
                          archivePropertiesMutation.mutate({
                              query: {
                                  ids: [props.row.original.id],
                              },
                          });

                          trackEvent({
                              event: 'properties_archive_listing',
                              extra: {
                                  ['property_id']: props.row.original.id,
                                  ['bulk_action']: false,
                                  ['list_view']: ucFirst(
                                      props.row.original.filterStatus
                                  ),
                              },
                          });
                      },
                  },
            // Send to
            props.row.original.type === 'property'
                ? {
                      text: trans('label.send_to'),
                      onClick: () => {
                          openSendAdEmailDialog([props.row.original.id]);
                      },
                      isDisabled: !isGetrix,
                  }
                : undefined,
        ],
    } as Record<string, Array<ActionListItemProps | undefined>>;

    // Single pass through the list to separate enabled/disabled actions
    const processedLists = (() => {
        if (!list) {
            return {};
        }

        const result: Record<string, ActionListItemProps[]> = {};
        const disabledActions: ActionListItemProps[] = [];

        // Single loop through all entries and items
        // it is more verbose but also more efficient than using multiple filter() operations on the same data.
        for (const [key, arr] of Object.entries(list)) {
            if (!arr) {
                continue;
            }

            const enabledItems: ActionListItemProps[] = [];

            for (const item of arr) {
                if (!item) {
                    continue;
                } // Skip undefined items

                if (isGetrix || !item.isDisabled) {
                    enabledItems.push(item);
                } else {
                    // Non-Getrix user with disabled item
                    disabledActions.push(item);
                }
            }

            if (enabledItems.length > 0) {
                result[key] = enabledItems;
            }
        }

        // Add disabled actions group for non-Getrix users
        if (!isGetrix && disabledActions.length > 0) {
            result['disabled-level'] = disabledActions;
        }

        return result;
    })();

    return (
        <div className="crm-cell-actions">
            <Tooltip text={trans('label.see_details')}>
                <Button
                    onClick={() => {
                        openDetailDialog({
                            id: props.row.original.id,
                            //TODO: this should be changed
                            // @ts-expect-error when we change the type of the property, we should remove this
                            type: convertCase(props.row.original.type, 'camel'),
                        });
                        trackEvent({
                            event: 'properties_open_details_listing',
                            extra: {
                                ['property_id']: props.row.original.id,
                            },
                        });
                    }}
                    target="_blank"
                    iconOnly
                    size="small"
                >
                    <Icon name="eye" />
                </Button>
            </Tooltip>

            <Tooltip text={trans('label.edit')}>
                <Button
                    as="a"
                    href={
                        props.row.original.type === 'new_construction'
                            ? `/v2/nuove-costruzioni?idAnnuncio=${props.row.original.id}&tipologia=${props.row.original.type}&step=6`
                            : `/inserimento_annuncio.php?idAnnuncio=${props.row.original.id}&tipologia=${props.row.original.type}&step=5`
                    }
                    target="_blank"
                    iconOnly
                    size="small"
                    onClick={() => {
                        trackEvent({
                            event: 'properties_edit_listing',
                            extra: {
                                ['property_id']: props.row.original.id,
                                ['list_view']: ucFirst(
                                    props.row.original.filterStatus
                                ),
                            },
                        });
                    }}
                >
                    <Icon name="pencil" />
                </Button>
            </Tooltip>

            <PerformanceLink.HideWhen type={props.row.original.type}>
                <PerformanceLink
                    propertyId={props.row.original.id}
                    rowIndex={props.row.index}
                    pageIndex={props.table.options.state.pagination?.pageIndex}
                    pageSize={props.table.options.state.pagination?.pageSize}
                />
            </PerformanceLink.HideWhen>

            <ShareButton
                link={props.row.original.portalUrl || ''}
                onShareItemClick={(sharingChannel) => {
                    trackEvent({
                        event: 'properties_listing_shared_step_2',
                        extra: {
                            ['sharing_channel']: sharingChannel,
                            ['property_id']: props.row.original.id,
                        },
                    });
                }}
                onDropdownButtonClick={() => {
                    trackEvent({
                        event: 'properties_listing_shared_step_1',
                        extra: {
                            ['property_id']: props.row.original.id,
                        },
                    });
                }}
                buttonClassName="gx-button--small"
            />
            <ContentDropdownContext.Provider
                value={{
                    isOpen: isDropdownOpen,
                    setIsOpen: setIsDropdownOpen,
                }}
            >
                <ContentDropdown
                    renderAction={({ ref }) => (
                        <Button
                            ref={ref}
                            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                            size="small"
                            iconOnly
                            className="crm-cell-actions__otherActions"
                        >
                            <Tooltip
                                text={trans('label.other_actions')}
                                position="topRight"
                            >
                                <Icon name="ellipsis" />
                            </Tooltip>
                        </Button>
                    )}
                    position="bottom-right"
                >
                    {Object.entries(processedLists)
                        .filter(([_, value]) => value.length > 0)
                        .map(([key, value]) => {
                            // For non-Getrix users, wrap the entire disabled-level ActionList with Popover
                            if (!isGetrix && key === 'disabled-level') {
                                return (
                                    <DisabledActionsWithPopover
                                        key={key}
                                        actions={value}
                                    />
                                );
                            }

                            // Regular ActionList for Getrix users
                            return (
                                <ActionList data-testid="action-list" key={key}>
                                    {value.map((item, index) => (
                                        <ActionListItem
                                            data-testid="action-list-item"
                                            key={index}
                                            {...item}
                                        />
                                    ))}
                                </ActionList>
                            );
                        })}
                </ContentDropdown>
            </ContentDropdownContext.Provider>
        </div>
    );
}

const MemoRegularActions = memo(RegularActions);

const DisabledActionsWithPopover = memo(
    ({ actions }: { actions: ActionListItemProps[] }) => {
        return (
            <Popover
                // @ts-ignore
                title={null}
                onEdge={false}
                large
                position="left"
                // @ts-ignore
                content={<UnlockGetrixPopoverContent />}
            >
                {/*todo: Helper Overlay Div to trigger the hover. The disabled items inside prevent hover events */}
                <div className="action-list-disabled-with-overlay">
                    <ActionList data-testid="action-list">
                        {actions.map((item, index) => (
                            <ActionListItem
                                key={index}
                                data-testid="action-list-item"
                                {...item}
                                endElement={<Icon name="lock" />}
                                isDisabled={true}
                            />
                        ))}
                    </ActionList>
                </div>
            </Popover>
        );
    }
);

function TuristicActions(props: CellContext<Property, unknown>) {
    const { trackEvent } = useMixpanelContext();
    const { open: openDeleteDialog } = useOpenPropertyDeleteDialog();
    const { open: openDetailDialog } = useOpenPropertyDetailDialog();

    return (
        <div className="crm-cell-actions">
            <Tooltip text={trans('label.see_details')}>
                <Button
                    onClick={() => {
                        openDetailDialog({
                            id: props.row.original.id,
                            //TODO: this should be changed
                            // @ts-expect-error when we change the type of the property, we should remove this
                            type: convertCase(props.row.original.type, 'camel'),
                        });
                    }}
                    target="_blank"
                    iconOnly
                    size="small"
                >
                    <Icon name="eye" />
                </Button>
            </Tooltip>
            <Dropdown
                buttonIsIconOnly={true}
                buttonContent={
                    <Tooltip
                        text={trans('label.other_actions')}
                        position="topRight"
                    >
                        <Icon name="ellipsis" />
                    </Tooltip>
                }
                position="bottomRight"
                buttonSize="small"
                showCaret={false}
            >
                <ActionList>
                    {/* Preview */}
                    {props.row.original.portalUrl &&
                    props.row.original.status === 'portal_name' ? (
                        <ActionListItem
                            onClick={() => {
                                trackEvent({
                                    event: 'properties_open_preview_listing',
                                    extra: {
                                        ['property_id']: props.row.original.id,
                                    },
                                });
                            }}
                            target="_blank"
                            href={props.row.original.portalUrl}
                            text={trans('label.see_preview')}
                        />
                    ) : (
                        <Fragment />
                    )}
                    {/* Delete */}
                    <ActionListItem
                        text={trans('label.remove')}
                        onClick={() => {
                            openDeleteDialog([props.row.original.id]);
                        }}
                    />
                </ActionList>
            </Dropdown>
        </div>
    );
}

export const MemoActionsCell = memo(ActionsCell);
