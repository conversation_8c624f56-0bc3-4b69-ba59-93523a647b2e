import { describe, expect, it, vi } from 'vitest';
import { ActivationError, getErrors } from './ActivationError';
import { render, screen } from '#tests/react/testing-library-enhanced';
import { QueryClientProvider } from '@tanstack/react-query';
import { createQueryClient } from 'lib/queryClient';
import errorJSON from '#tests/mock/api/portal-properties/PATCH_activate--error.json';
import * as hooks from './useActivationErrorState';
import { useState } from 'react';
import { server } from '#tests/vitest/setup';
import { getValidationErrors } from 'lib/REST/requests/api/properties/properties';
import { http, HttpResponse } from 'msw';

const useMockedActivationErrorState = () => {
    const [state] = useState({ status: 'error' as any, error: errorJSON.data });

    return state;
};

describe('getErrors', () => {
    it('should return errors', () => {
        expect(
            getErrors(
                {
                    defaults: {},
                    errors: {
                        cap: {
                            REQUIRED: 'RICHIESTO',
                        },
                    },
                },
                {
                    adId: '82555641',
                    code: '',
                    extra: {
                        'properties[0].cap.randomstuff': ['REQUIRED'],
                    },
                }
            )
        ).toEqual({ code: '', errors: ['RICHIESTO'], id: '82555641' });
    });
});

describe('ActivationError', () => {
    it('should render successfully', async () => {
        // The dictionary you get from the API (limited to the mock to keep it simple)
        server.use(
            http.get(getValidationErrors.endpoint, () =>
                HttpResponse.json({
                    status: 'success',
                    data: {
                        defaults: {},
                        errors: {
                            cap: {
                                REQUIRED: 'RICHIESTO',
                                TOTAL_CONSISTENCES_SURFACE_TOO_LOW: 'LOW',
                                EXPECTED_GREATER_VALUE: 'GREATER_VALUE',
                            },
                        },
                    },
                })
            )
        );

        // The error you get
        vi.spyOn(hooks, 'useActivationErrorState').mockReturnValue({
            status: 'error',
            error: {
                extra: [
                    {
                        adId: 103953249,
                        extra: {
                            'properties[0].cap.randomstuff': [
                                'TOTAL_CONSISTENCES_SURFACE_TOO_LOW',
                            ],
                            'properties[0].cap': ['EXPECTED_GREATER_VALUE'],
                        },
                    },
                ],
            },
        });

        render(<ActivationError />, {
            wrapper(props) {
                return (
                    <QueryClientProvider client={createQueryClient()}>
                        {props.children}
                    </QueryClientProvider>
                );
            },
        });

        expect(
            await screen.findByText('ads_activation_failed')
        ).toBeInTheDocument();

        await screen.findByText('103953249');
        screen.getByText('LOW');
        screen.getByText('GREATER_VALUE');
    });

    it('should be dismissable', async () => {
        vi.spyOn(hooks, 'useActivationErrorState').mockImplementation(
            useMockedActivationErrorState
        );

        const { user } = render(<ActivationError />, {
            wrapper(props) {
                return (
                    <QueryClientProvider client={createQueryClient()}>
                        {props.children}
                    </QueryClientProvider>
                );
            },
        });

        const dismissableBtn = await screen.findByRole('button');
        expect(dismissableBtn).toBeInTheDocument();
        await user.click(dismissableBtn);
        expect(dismissableBtn).not.toBeInTheDocument();
    });

    it('should not be visible', async () => {
        vi.spyOn(hooks, 'useActivationErrorState').mockReturnValue({
            status: 'idle',
        });

        render(<ActivationError />, {
            wrapper(props) {
                return (
                    <QueryClientProvider client={createQueryClient()}>
                        {props.children}
                    </QueryClientProvider>
                );
            },
        });

        expect(
            screen.queryByText('ads_activation_failed')
        ).not.toBeInTheDocument();
    });
});
