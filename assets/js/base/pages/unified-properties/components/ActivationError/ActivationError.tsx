import { Alert } from '@gx-design/alert';
import { trans } from '@pepita-i18n/babelfish';
import { useQuery } from '@tanstack/react-query';
import { getValidationErrorsOptions } from 'lib/REST/requests/api/properties/query-factory';
import { useEffect, useState } from 'react';
import { useActivationErrorState } from './useActivationErrorState';

export function getErrors(
    dictionary: {
        errors: Record<string, Record<string, string>>;
        defaults: Record<string, string>;
    },
    item: { extra: any; adId: string; code: string }
) {
    const errorData: {
        id: string;
        code: string;
        errors: string[];
    } = {
        id: item['adId'],
        code: item['code'],
        errors: [],
    };

    const extraKeys = Object.keys(item.extra);

    for (const extraKeyItem of extraKeys) {
        const errorType = item.extra?.[extraKeyItem]?.[0];
        const splittedKeyItems = extraKeyItem.split('.');

        for (const key of splittedKeyItems) {
            if (dictionary.errors[key] && dictionary.errors[key][errorType]) {
                errorData.errors.push(trans(dictionary.errors[key][errorType]));
                continue;
            }
        }
    }

    return {
        ...errorData,
        // errors should be unique
        errors: Array.from(new Set(errorData.errors)),
    };
}

export function ActivationError() {
    const [alertVisibile, setAlertVisible] = useState(false);
    const activationState = useActivationErrorState();

    useEffect(() => {
        setAlertVisible(activationState?.status === 'error');
    }, [activationState]);

    const validationErrorOptions = useQuery({
        enabled: activationState?.status === 'error',
        ...getValidationErrorsOptions,
        select(data): Array<{ id: string; code: string; errors: string[] }> {
            if (activationState?.status === 'error') {
                return activationState?.error?.extra.map((item) =>
                    getErrors(data, item)
                );
            }

            return [];
        },
    });

    if (!alertVisibile) {
        return null;
    }

    if (validationErrorOptions.data?.length) {
        return (
            <div className="crm-alert-wrapper">
                <Alert
                    style="error"
                    dismissable
                    onDismiss={() => {
                        setAlertVisible(false);
                    }}
                >
                    <div className="error-title">
                        <strong>{trans('ads_activation_failed')}</strong>
                    </div>
                    <span>{trans('ads_activation_failed_codes')}</span>
                    {validationErrorOptions.data?.map((error) => {
                        return (
                            <div key={error.id} className="activation-error-item">
                                <strong>
                                    {error.code
                                        ? `${error.code} (${error.id})`
                                        : error.id}
                                </strong>
                                {error.errors.length > 0 && (
                                    <ul>
                                        {error.errors.map(
                                            (errorItem, index) => (
                                                <li key={`error_item_${index}`}>
                                                    <span>{errorItem}</span>
                                                </li>
                                            )
                                        )}
                                    </ul>
                                )}
                            </div>
                        );
                    })}
                </Alert>
            </div>
        );
    }

    return null;
}
