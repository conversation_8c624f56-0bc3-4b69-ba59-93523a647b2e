import { useMutationState } from '@tanstack/react-query';
import { ExpectedRestError } from 'lib/REST';
import { useMemo } from 'react';

export const useActivationErrorState = () => {
    const activationStates = useMutationState({
        filters: {
            mutationKey: ['activate-bulk'],
        },
    });
    const lastActivationState = activationStates[activationStates.length - 1];

    return useMemo(() => {
        switch (lastActivationState?.status) {
            case 'error':
                return {
                    status: lastActivationState?.status,
                    error:
                        lastActivationState?.error instanceof ExpectedRestError
                            ? lastActivationState?.error.originalError.data
                            : undefined,
                };
            default:
                return {
                    status: lastActivationState?.status,
                };
        }
    }, [lastActivationState]);
};
