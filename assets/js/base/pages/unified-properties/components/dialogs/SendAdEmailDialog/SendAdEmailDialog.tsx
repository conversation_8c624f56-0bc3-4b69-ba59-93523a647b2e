import {
    sendMultipleEmails,
    getContactsLookup,
    queryFactory as contactsQueryFactory,
} from 'lib/REST/requests/v2/services';
import { UnexpectedRestError } from 'lib/REST';

import React, {
    createContext,
    PropsWithChildren,
    useCallback,
    useContext,
    useMemo,
    useState,
} from 'react';
import { trans } from '@pepita-i18n/babelfish';
import { useMutation, useQuery } from '@tanstack/react-query';
import * as Yup from 'yup';

import { Modal } from '@gx-design/modal';
import { Form, Formik, useFormikContext } from 'formik';
import { GxFkTextarea, GxFkInput } from 'gtx-react/components/gx-formik';
import AutocompleteFormik from 'gtx-react/containers/AutocompleteFormik';
import { Tag } from '@gx-design/tag';
import { Loader } from '@gx-design/loader';
import { useNotifyContext } from '@gx-design/snackbar';
import { Button } from '@gx-design/button';
import { validEmail, keyPressedEnter } from 'gtx-react/utils/utility';

import { DESC_MAX_LENGTH } from 'gtx-react/components/EditProfileForm/constants';

type SendEmailFormValues = {
    destinatari: string[];
    oggetto: string;
    messaggio: string;
};

type SendEmailFormProps = {
    isSubmitting?: boolean;
};

const SendEmailSchema = Yup.object().shape({
    destinatari: Yup.array()
        .min(1)
        .required(trans('label.email.missing_recipient')),
    oggetto: Yup.string().required(trans('label.required_value')),
    messaggio: Yup.string().required(trans('label.required_value')),
});

export type SendEmailSchemaType = Yup.InferType<typeof SendEmailSchema>;

type SendAdEmailDialogValues = Array<number> | null;

const SendAdEmailDialogReaderContext = createContext<
    SendAdEmailDialogValues | undefined
>(undefined);

export function useSendAdEmailDialogReader() {
    const context = useContext(SendAdEmailDialogReaderContext);
    if (context === undefined) {
        throw new Error(
            'useSendAdEmailDialogReader must be used within a SendAdEmailDialogProvider'
        );
    }
    return context;
}

const SendAdEmailDialogWriterContext = createContext<
    ((args: SendAdEmailDialogValues) => void) | undefined
>(undefined);

export function useSendAdEmailDialogWriter() {
    const context = useContext(SendAdEmailDialogWriterContext);
    if (context === undefined) {
        throw new Error(
            'useSendAdEmailDialogWriter must be used within a SendAdEmailDialogProvider'
        );
    }
    return context;
}

export function SendAdEmailModal(props: {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    isSubmitting?: boolean;
}) {
    const { submitForm, isValid, values } =
        useFormikContext<SendEmailSchemaType>();

    return (
        <Modal
            footer={
                <>
                    <Button variant="ghost" onClick={props.onClose}>
                        {trans('label.cancel')}
                    </Button>
                    <Button
                        variant="accent"
                        onClick={() => {
                            // Only submit if form is valid and destinatari is not empty
                            if (isValid && values.destinatari.length > 0) {
                                props.onConfirm();
                                submitForm();
                            }
                        }}
                        disabled={!isValid || values.destinatari.length === 0}
                    >
                        {trans(`btn.label.send`)}
                    </Button>
                </>
            }
            size="large"
            closeAction
            isOpen={props.isOpen}
            title={trans('label.send_ad_pdf_attachment_email')}
            onClose={props.onClose}
            onConfirm={props.onConfirm}
        >
            <SendEmailForm isSubmitting={props.isSubmitting}></SendEmailForm>
        </Modal>
    );
}

export function SendAdEmailDialogProvider(props: PropsWithChildren) {
    const [ids, setIds] = useState<SendAdEmailDialogValues>(null);

    /**
     * Callback to set the ids in the context
     * @param newIds
     */
    const setIdsCallback = useCallback((newIds: SendAdEmailDialogValues) => {
        setIds(newIds);
    }, []);

    return (
        <SendAdEmailDialogWriterContext.Provider value={setIdsCallback}>
            <SendAdEmailDialogReaderContext.Provider value={ids}>
                {props.children}
            </SendAdEmailDialogReaderContext.Provider>
        </SendAdEmailDialogWriterContext.Provider>
    );
}

export function useOpenSendAdEmailDialog() {
    const setIds = useSendAdEmailDialogWriter();

    return useMemo(
        () => ({
            open: (ids: SendAdEmailDialogValues) => {
                setIds(ids);
            },
            close: () => {
                setIds(null);
            },
        }),
        [setIds]
    );
}

export function SendAdEmailDialog() {
    const ids = useSendAdEmailDialogReader();
    const { close } = useOpenSendAdEmailDialog();
    const { showNotification } = useNotifyContext();

    const sendEmailMutation = useMutation({
        mutationFn: async (data: SendEmailFormValues) => {
            const { destinatari, oggetto, messaggio } = data;
            const requestData = {
                destinatari: destinatari.join(', '),
                oggetto,
                messaggio,
                body: messaggio, // old api call sends this property too
                pk: ids?.[0]?.toString() ?? '',
            };

            const response = await sendMultipleEmails({ query: requestData });

            if (response && response.result) {
                return response;
            } else {
                throw new Error(response?.message);
            }
        },
        onSuccess: () => {
            showNotification({
                type: 'success',
                message: trans('label.email.send_success'),
            });
            close();
        },
        onError: async (error) => {
            if (error instanceof UnexpectedRestError) {
                if (error.response.status === 400) {
                    const data = await error.response.json();
                    return showNotification({
                        type: 'error',
                        message: data.message,
                    });
                }
            }
            showNotification({
                type: 'error',
                message: trans('label.email.send_error'),
            });
        },
    });

    if (!ids) {
        return null;
    }

    const onSubmit = (data: SendEmailFormValues) => {
        sendEmailMutation.mutate(data);
    };

    return (
        <Formik
            initialValues={{
                destinatari: [],
                oggetto: trans('label.real_estate_proposal'),
                messaggio: `${trans('label.dear_customer')},\n${trans(
                    'label.sending_ad_preview_as_attachment'
                )}: ${ids}\n\n${trans('label.greetings')}`,
                customerEmail: {
                    name: '',
                    value: '',
                },
            }}
            onSubmit={onSubmit}
            validationSchema={SendEmailSchema}
        >
            <SendAdEmailModal
                isOpen={Boolean(ids)}
                onClose={close}
                onConfirm={() => {
                    if (!ids) {
                        throw new Error(
                            'SendAdEmailDialog: ids is null, this should not happen'
                        );
                    }
                }}
                isSubmitting={sendEmailMutation.isPending}
            />
        </Formik>
    );
}

const SendEmailForm = ({ isSubmitting }: SendEmailFormProps) => {
    const { values, setFieldValue } = useFormikContext<SendEmailSchemaType>();

    const handleInputKeyDown = (
        event: React.KeyboardEvent<HTMLInputElement>,
        inputValue?: string
    ) => {
        // On press Enter, check if value is a valid email and add it to destinatari
        if (keyPressedEnter(event)) {
            const emailValue = inputValue?.trim();
            if (emailValue && validEmail(emailValue)) {
                addTag('customerEmail', { value: emailValue });
            }
        }
    };

    const handleInputBlur = (emailValue: string) => {
        // On blur, check if value is a valid email and add it to destinatari
        if (emailValue && validEmail(emailValue)) {
            addTag('customerEmail', { value: emailValue });
        }
    };

    // Use React Query for loading all contacts with mutation pattern
    const {
        data: allContactsData,
        isFetching: isLoadingContacts,
        refetch: refetchAllContacts,
    } = useQuery({
        ...contactsQueryFactory.createAllContactsQueryOptions(),
        enabled: false, // Only fetch when explicitly triggered
    });

    // Use mutation pattern for better loading state and error handling
    const addAllContactsMutation = useMutation({
        mutationFn: async () => {
            const result = await refetchAllContacts();
            if (!result.data) {
                throw new Error('No contacts data received');
            }
            return result.data;
        },
        onSuccess: (contacts) => {
            const emails = contacts.map((contact) => contact.value);
            setFieldValue('destinatari', emails);
        },
        onError: (error) => {
            console.error('Failed to load all contacts:', error);
        },
    });

    // Wrapper function to match AutocompleteFormik expected signature
    const getContactsApiWrapper = (search: string) => {
        if (!search) {
            return Promise.resolve([]);
        }
        // TODO: use createContactsLookupQueryOptions with autoformik. For now it directly calls getContactsLookup
        return getContactsLookup({ query: { q: search } });
    };

    const addTag = (field?: string, data?: Record<string, any>) => {
        if (!field || !data) {
            return;
        }

        setFieldValue('customerEmail', {
            name: '',
            value: '',
        });

        if (data?.value) {
            setFieldValue('destinatari', [...values.destinatari, data.value]);
        }
    };

    const removeTag = (tag: string) => {
        setFieldValue(
            'destinatari',
            values.destinatari.filter((a) => a !== tag)
        );
    };

    const removeAllTags = () => {
        setFieldValue('destinatari', []);
    };

    return (
        <Form
            noValidate
            placeholder={undefined}
            onPointerEnterCapture={undefined}
            onPointerLeaveCapture={undefined}
        >
            {(isSubmitting ||
                isLoadingContacts ||
                addAllContactsMutation.isPending) && <Loader />}

            <div className="gx-box-row">
                <div>
                    <div className="gx-label">
                        {trans('label.recipients_list')}
                    </div>
                    {values.destinatari.length ? (
                        <div className="tags-container">
                            <>
                                {values.destinatari.map((tag, index) => (
                                    <Tag
                                        key={`tag_${index}`}
                                        dismissable
                                        onCloseClick={() => removeTag(tag)}
                                        text={tag}
                                    />
                                ))}
                            </>
                        </div>
                    ) : null}

                    <AutocompleteFormik
                        defaultValue={values['customerEmail']}
                        name={'customerEmail'}
                        key={'customerEmail'}
                        placeholder={trans('label.enter_name_or_email')}
                        getAutocompleteApi={getContactsApiWrapper}
                        itemKey="value"
                        selectedLabelKey="value"
                        showRequiredSymbol={true}
                        formatFn={(item) =>
                            `${item.label} ${
                                item.value ? `(${item.value})` : ''
                            }`
                        }
                        classWrap="gx-box-row"
                        onChange={addTag}
                        onInputKeyDown={handleInputKeyDown}
                        onInputBlur={handleInputBlur}
                    />
                </div>
                <div>
                    <Button
                        variant="accent"
                        onClick={() => addAllContactsMutation.mutate()}
                    >
                        <span>{trans('label.add_all_customers')}</span>
                    </Button>

                    {values.destinatari.length ? (
                        <Button onClick={removeAllTags}>
                            <span>{trans('label.remove_all_customers')}</span>
                        </Button>
                    ) : null}
                </div>
            </div>

            <div className="gx-box-row">
                <GxFkInput
                    required
                    name="oggetto"
                    label={trans('label.subject')}
                    type="text"
                />
            </div>
            <div className="gx-box-row">
                <GxFkTextarea
                    label={trans('label.text_body')}
                    isLabelVisible={true}
                    name="messaggio"
                    maxLength={DESC_MAX_LENGTH}
                    rows={8}
                />
            </div>
        </Form>
    );
};
