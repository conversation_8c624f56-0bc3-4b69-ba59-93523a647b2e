import React from 'react';
import {
    Image as GxImage,
    ListItem,
    ListField,
} from '../../../../commons/gtx-react/components';
import { IvSchActions } from './IvSchActions';
import { IScheduledVisitItem } from '../types/list';
import { getPriceFromProperty } from '../utils/getPriceFromProperty';
import { getSurfaceFromProperty } from '../utils/getSurfaceFromProperty';
import { getAddressFromProperty } from '../utils/getAddressFromProperty';
import { Button } from '@gx-design/button';
import { Popover } from '@gx-design/popover';
import { PopoverContent } from './IvSchPopoverContent';
import { format, isValid } from 'date-fns';

interface IRealEstateColProps {
    property: IScheduledVisitItem['property'];
}

const RealEstateCol: React.FC<IRealEstateColProps> = ({ property }) => {
    return (
        <div className="gx-immovisitaRow__property">
            <div className="list__field__thumb">
                <div className="property-block__photo">
                    <GxImage
                        loading="lazy"
                        src={property?.mainThumbUrl}
                        fallbackSrc="/bundles/base/getrix/common/img/img-placeholder.png"
                    />
                </div>
            </div>
            <div className="generic-content-block truncate">
                <div>RIF: {property.id}</div>
                <div>{getAddressFromProperty(property)}</div>
                <div>
                    {getPriceFromProperty(property)} |{' '}
                    {getSurfaceFromProperty(property)}
                </div>
            </div>
        </div>
    );
};

const IvSchRowVisitDate = (props: { scheduledVisitDate?: string | null }) => {
    if (!props.scheduledVisitDate) {
        return null;
    }

    const scheduledVisitDate = new Date(props.scheduledVisitDate);

    return (
        isValid(scheduledVisitDate) && (
            <>
                {format(scheduledVisitDate, 'dd/MM/yyyy')}
                <br />
                {format(scheduledVisitDate, 'HH:mm')}
            </>
        )
    );
};

interface IIvSchRowProps {
    scheduledVisit: IScheduledVisitItem;
}
export const IvSchRow: React.FC<IIvSchRowProps> = ({ scheduledVisit }) => {
    return (
        <ListItem>
            <ListField className="list__field--immovisita" key="date">
                <div
                    className="generic-content-block"
                    data-testid="scheduled-visit-formatted-date"
                >
                    <IvSchRowVisitDate
                        scheduledVisitDate={scheduledVisit.scheduledTime}
                    />
                </div>
            </ListField>
            <ListField key="immobile">
                {scheduledVisit.property && (
                    <RealEstateCol property={scheduledVisit.property} />
                )}
            </ListField>
            <ListField key="agent">
                <div className="generic-content-block">
                    {scheduledVisit.agent.firstname}{' '}
                    {scheduledVisit.agent.lastname}
                </div>
            </ListField>
            <ListField key="guests">
                {/** @ts-ignore */}
                {scheduledVisit.guests?.length > 0 ? (
                    <Popover
                        title=""
                        onEdge={false}
                        large={false}
                        content={
                            <PopoverContent scheduledVisit={scheduledVisit} />
                        }
                    >
                        <Button iconOnly={true}>
                            {scheduledVisit.guests.length}
                        </Button>
                    </Popover>
                ) : (
                    <Button iconOnly={true}>
                        {scheduledVisit.guests.length}
                    </Button>
                )}
            </ListField>
            <ListField
                key="actions"
                className="list__field--messages list__field--alignRight list__field--small"
            >
                <IvSchActions isDesktop item={scheduledVisit} />
            </ListField>
        </ListItem>
    );
};
