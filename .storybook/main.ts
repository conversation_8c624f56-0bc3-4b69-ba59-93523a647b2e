import type { StorybookConfig } from '@storybook/react-webpack5';
import { join, dirname } from 'path';
import path from 'path';
import { I18nPo2Json } from '../packages/webpack-plugins/i18nPo2Json/dist/index.js';

// console.log('Webpack config:', custom.resolve?.plugins);

const TsconfigPathsPlugin = require('tsconfig-paths-webpack-plugin');

// Translation directories for i18n
const translationsDirs = [
    path.join(__dirname, '../vendor/indomio-translations/cms-contents/translations/messages'),
    path.join(__dirname, '../vendor/indomio-translations/cms/translations/messages'),
];

/**
 * This function is used to resolve the absolute path of a package.
 * It is needed in projects that use Yarn PnP or are set up within a monorepo.
 */
function getAbsolutePath(value: string): any {
    return dirname(require.resolve(join(value, 'package.json')));
}
const config: StorybookConfig = {
    core: {
        builder: 'webpack5',
        disableTelemetry: true, // 👈 Disables telemetry
    },
    stories: [
        '../assets/documentation/**/*.mdx"',
        '../assets/documentation/**/*.stories.mdx',
        '../assets/documentation/**/*.stories.@(js|jsx|mjs|ts|tsx)',
    ],
    addons: [
        getAbsolutePath('@storybook/addon-webpack5-compiler-swc'),
        getAbsolutePath('@storybook/addon-essentials'),
        getAbsolutePath('@storybook/addon-onboarding'),
        getAbsolutePath('@storybook/addon-interactions'),
        getAbsolutePath('@storybook/addon-styling-webpack'),
        getAbsolutePath('@storybook/addon-mdx-gfm'),
    ],
    framework: {
        name: getAbsolutePath('@storybook/react-webpack5'),
        options: {
            fsCache: true,
            lazyCompilation: true,
        },
    },
    typescript: {
        reactDocgen: 'react-docgen-typescript',
    },
    docs: {},
    webpackFinal: async (config) => {
        config.resolve = config.resolve || {};
        config.resolve.alias = {
            ...(config.resolve.alias || {}),
            'gtx-react': path.resolve(__dirname, '../assets/js/commons/gtx-react'),
            lib: path.resolve(__dirname, '../assets/js/commons/lib'),
            '@getrix/common/js/gtx-logged-user': path.resolve(__dirname, '../.storybook/__mocks__/gtx-logged-user.ts'),
            'gtx-constants': path.resolve(__dirname, '../.storybook/__mocks__/gtx-constants.ts'),
        };

        config.plugins = [
            ...(Array.isArray(config.plugins) ? config.plugins : []),
            new I18nPo2Json(translationsDirs, false) as any,
        ].filter(Boolean);

        return config;
    },
};

export default config;
