import { ComponentProps } from 'react';
import * as components from './components';
import { Action } from '../types';

export type ComponentKey = keyof typeof components;

export type ComponentPropsMap = {
  [key in ComponentKey]: ComponentProps<(typeof components)[key]>;
};

type CustomComponentProps<T extends ComponentKey> = {
  componentName: T;
} & ComponentPropsMap[T];

export function CustomComponent<T extends ComponentKey>({
  componentName,
  ...props
}: CustomComponentProps<T>) {
  const Component = components[componentName] as (
    props: Omit<CustomComponentProps<T>, 'componentName'> | unknown,
  ) => JSX.Element;
  if (!Component) {
    return null;
  }
  return <Component {...props} />;
}

export function CustomComponents(props: { actions: Action[] }) {
  return (
    <>
      {props.actions.map(({ type, ...rest }, index) => {
        const props = rest.props as ComponentPropsMap[ComponentKey];

        if (typeof props !== 'object' || props === null) {
          // Skip this iteration if componentProps is not an object.
          return <CustomComponent key={index} componentName={type} />;
        }

        return <CustomComponent key={index} componentName={type} {...props} />;
      })}
    </>
  );
}
