import { BaseMenuConfig, MenuConfig } from '../../types';
import { ContextMapKey, adapterMap } from './contexts';

type AdapterProps = {
  menu: BaseMenuConfig;
  level?: number | undefined;
  children: (menu: BaseMenuConfig) => JSX.Element | null;
};

type ComponentAdapterProps = (props: AdapterProps) => JSX.Element | null;

type FeatureMenuAdapterConsumerProps = {
  index?: number;
  menu: MenuConfig;
  components: ComponentAdapterProps[];
  children: (menu: MenuConfig) => JSX.Element | null;
};

/**
 * This component is used to adapt the menu with the contexts
 * @internal This component is used internally by the `FeaturesMenuAdapter` component
 */
function FeatureMenuAdapterConsumer({
  components,
  index = 0,
  menu,
  children,
}: FeatureMenuAdapterConsumerProps) {
  const next = index + 1;
  const Component = components[index];

  if (!Component) {
    return children(menu);
  }

  const isLast = next === components.length;

  return (
    <Component menu={menu}>
      {(_menu) =>
        isLast ? (
          children(_menu)
        ) : (
          <FeatureMenuAdapterConsumer index={next} menu={_menu} components={components}>
            {(__menu) => children(__menu)}
          </FeatureMenuAdapterConsumer>
        )
      }
    </Component>
  );
}

type FeaturesMenuAdapterProps = {
  /**
   * The menu to adapt
   */
  menu: MenuConfig;
  /**
   * The level of the menu (according to the menu tree)
   * @description Actually we have only 2 (for the secondary) and 3 (for the main)
   * Be careful, levels may vary in the future.
   */
  level?: number;
  /**
   * The contexts to use to adapt the menu
   * @description Contexts are defined in the `contexts` folder and are used for specific features
   * Take as example the `<MessageCounterContext>` that is used to show the number of unread messages, you can find it in `contexts/MessageCounterContext.tsx`
   * It is important to note that the order of the contexts is important, because the menu is adapted in the order in which the contexts are passed and the result of the first context is passed to the second and so on.
   */
  contexts?: ContextMapKey[];
  /**
   * The children of the component
   * @description The children are the component that will be rendered after the menu is adapted
   * @example The following example shows how to use the `FeaturesMenuAdapter` component
   * ```tsx
   * <FeaturesMenuAdapter menu={menu} contexts={['message-counter']}>
   *    {(menu) => <Menu menu={menu} />}
   * </FeaturesMenuAdapter>
   * ```
   * In this example the `Menu` component will be rendered after the menu is adapted with the `messageCounter` context
   */
  children: (menu: MenuConfig) => JSX.Element | null;
};

/**
 * This component is used to adapt the menu with the contexts
 */
export const FeaturesMenuAdapter = (props: FeaturesMenuAdapterProps) => {
  if (!props.contexts) {
    return props.children(props.menu);
  }

  const adapters = props.contexts.reduce<ComponentAdapterProps[]>((current, context) => {
    const adapter = adapterMap.get(context);

    if (adapter) {
      current.push(adapter);
    }

    return current;
  }, []);

  return (
    <FeatureMenuAdapterConsumer menu={props.menu} components={adapters}>
      {props.children}
    </FeatureMenuAdapterConsumer>
  );
};
