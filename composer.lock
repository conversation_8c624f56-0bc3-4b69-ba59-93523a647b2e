{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "76f12621f2cfef50ee83a6dd078f3e20", "packages": [{"name": "apache/thrift", "version": "0.9.2", "source": {"type": "git", "url": "https://github.com/apache/thrift.git", "reference": "591e20f9636c37527a70dc03598218c3468a0eff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/apache/thrift/zipball/591e20f9636c37527a70dc03598218c3468a0eff", "reference": "591e20f9636c37527a70dc03598218c3468a0eff", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"Thrift": "lib/php/lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Apache Thrift Developers", "email": "<EMAIL>", "homepage": "http://thrift.apache.org"}], "description": "Apache Thrift RPC system", "homepage": "http://thrift.apache.org/", "support": {"email": "<EMAIL>", "issues": "https://issues.apache.org/jira/browse/THRIFT", "source": "https://github.com/apache/thrift/tree/0.9.2"}, "time": "2014-11-05T02:12:20+00:00"}, {"name": "brick/math", "version": "0.9.3", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "ca57d18f028f84f777b2168cd1911b0dee2343ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/ca57d18f028f84f777b2168cd1911b0dee2343ae", "reference": "ca57d18f028f84f777b2168cd1911b0dee2343ae", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^7.5.15 || ^8.5 || ^9.0", "vimeo/psalm": "4.9.2"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "brick", "math"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.9.3"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/brick/math", "type": "tidelift"}], "time": "2021-08-15T20:50:18+00:00"}, {"name": "doctrine/annotations", "version": "1.14.4", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "253dca476f70808a5aeed3a47cc2cc88c5cab915"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/253dca476f70808a5aeed3a47cc2cc88c5cab915", "reference": "253dca476f70808a5aeed3a47cc2cc88c5cab915", "shasum": ""}, "require": {"doctrine/lexer": "^1 || ^2", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "~1.4.10 || ^1.10.28", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6.4 || ^7", "vimeo/psalm": "^4.30 || ^5.14"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.14.4"}, "time": "2024-09-05T10:15:52+00:00"}, {"name": "doctrine/cache", "version": "1.13.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "56cd022adb5514472cb144c087393c1821911d09"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/56cd022adb5514472cb144c087393c1821911d09", "reference": "56cd022adb5514472cb144c087393c1821911d09", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "predis/predis": "~1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.13.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:06:54+00:00"}, {"name": "doctrine/collections", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "2b44dd4cbca8b5744327de78bafef5945c7e7b5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/2b44dd4cbca8b5744327de78bafef5945c7e7b5e", "reference": "2b44dd4cbca8b5744327de78bafef5945c7e7b5e", "shasum": ""}, "require": {"doctrine/deprecations": "^0.5.3 || ^1", "php": "^7.1.3 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0 || ^10.0", "phpstan/phpstan": "^1.4.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.1.5", "vimeo/psalm": "^4.22"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "lib/Doctrine/Common/Collections"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.8.0"}, "time": "2022-09-01T20:12:10+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"phpunit/phpunit": "<=7.5 || >=13"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12 || ^13", "phpstan/phpstan": "1.4.10 || 2.1.11", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6 || ^10.5 || ^11.5 || ^12", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.5"}, "time": "2025-04-07T20:06:18+00:00"}, {"name": "doctrine/event-manager", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/95aa4cb529f1e96576f3fda9f5705ada4056a520", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520", "shasum": ""}, "require": {"doctrine/deprecations": "^0.5.3 || ^1", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2022-10-12T20:51:15+00:00"}, {"name": "doctrine/lexer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/c268e882d4dbdd85e36e4ad69e02dc284f89d229", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.11"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.3"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2022-02-28T11:07:21+00:00"}, {"name": "doctrine/persistence", "version": "1.3.8", "source": {"type": "git", "url": "https://github.com/doctrine/persistence.git", "reference": "7a6eac9fb6f61bba91328f15aa7547f4806ca288"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/persistence/zipball/7a6eac9fb6f61bba91328f15aa7547f4806ca288", "reference": "7a6eac9fb6f61bba91328f15aa7547f4806ca288", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.2", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.10@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "vimeo/psalm": "^3.11"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common", "Doctrine\\Persistence\\": "lib/Doctrine/Persistence"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "homepage": "https://doctrine-project.org/projects/persistence.html", "keywords": ["mapper", "object", "odm", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/1.3.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fpersistence", "type": "tidelift"}], "time": "2020-06-20T12:56:16+00:00"}, {"name": "doctrine/reflection", "version": "1.2.4", "source": {"type": "git", "url": "https://github.com/doctrine/reflection.git", "reference": "6bcea3e81ab8b3d0abe5fde5300bbc8a968960c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/reflection/zipball/6bcea3e81ab8b3d0abe5fde5300bbc8a968960c7", "reference": "6bcea3e81ab8b3d0abe5fde5300bbc8a968960c7", "shasum": ""}, "require": {"doctrine/annotations": "^1.0 || ^2.0", "ext-tokenizer": "*", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^9", "doctrine/common": "^3.3", "phpstan/phpstan": "^1.4.10", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Reflection project is a simple library used by the various Doctrine projects which adds some additional functionality on top of the reflection functionality that comes with PHP. It allows you to get the reflection information about classes, methods and properties statically.", "homepage": "https://www.doctrine-project.org/projects/reflection.html", "keywords": ["reflection", "static"], "support": {"issues": "https://github.com/doctrine/reflection/issues", "source": "https://github.com/doctrine/reflection/tree/1.2.4"}, "abandoned": "roave/better-reflection", "time": "2023-07-27T18:11:59+00:00"}, {"name": "egulias/email-validator", "version": "3.2.6", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7", "reference": "e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7", "shasum": ""}, "require": {"doctrine/lexer": "^1.2|^2", "php": ">=7.2", "symfony/polyfill-intl-idn": "^1.15"}, "require-dev": {"phpunit/phpunit": "^8.5.8|^9.3.3", "vimeo/psalm": "^4"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.2.6"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2023-06-01T07:04:22+00:00"}, {"name": "ekbl/stats", "version": "2.0.0", "source": {"type": "git", "url": "https://guest:i4mG1t!!@gitlab.pepita.io/ekbl/stats.git", "reference": "3dce33e2d9358889976792042dfc6597a8a73bb0"}, "dist": {"type": "zip", "url": "https://satis.pepita.io/dist/ekbl/stats/ekbl-stats-2.0.0-bac75d.zip", "reference": "3dce33e2d9358889976792042dfc6597a8a73bb0", "shasum": "409e1bff9bcc4d3fe2064e2edb10ec3556919110"}, "require": {"php": ">=5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Ekbl\\Stats\\": "src/"}}, "license": ["proprietary"], "authors": [{"name": "Immobiliare Labs", "email": "<EMAIL>"}], "description": "Software relativo alle metriche applicative (stats)", "time": "2018-05-17T08:59:59+00:00"}, {"name": "ekbl/stats-bundle", "version": "2.3.0", "source": {"type": "git", "url": "https://guest:i4mG1t!!@gitlab.pepita.io/ekbl/stats-bundle.git", "reference": "aed95dd75a261ee2d6fa39550bc3144b9c8921a8"}, "dist": {"type": "zip", "url": "https://satis.pepita.io/dist/ekbl/stats-bundle/ekbl-stats-bundle-2.3.0-7d838c.zip", "reference": "aed95dd75a261ee2d6fa39550bc3144b9c8921a8", "shasum": "764e3a19d53b1b8a74454e099d961a3213f15a43"}, "require": {"doctrine/annotations": "^1.4", "ekbl/stats": "^2.0", "symfony/framework-bundle": "^2.8 || ^3.0 || ^4.0"}, "require-dev": {"phpunit/phpunit": "^5.7|^6.5"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Ekbl\\StatsBundle\\": ""}}, "license": ["proprietary"], "authors": [{"name": "Immobiliare Labs", "email": "<EMAIL>"}], "description": "Symfony EkblStatsBundle", "time": "2023-10-27T14:42:48+00:00"}, {"name": "firebase/php-jwt", "version": "1.0.0", "target-dir": "Firebase/PHP-JWT", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "58c1e61d0dff797f2989c54415229f4016d57a6d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/58c1e61d0dff797f2989c54415229f4016d57a6d", "reference": "58c1e61d0dff797f2989c54415229f4016d57a6d", "shasum": ""}, "require": {"php": ">=5.2.0"}, "type": "library", "autoload": {"classmap": ["Authentication/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v1.0.0"}, "time": "2014-08-28T17:14:17+00:00"}, {"name": "getrix-be/growthbook-bundle", "version": "v1.3.1", "source": {"type": "git", "url": "https://guest:i4mG1t!!@gitlab.pepita.io/getrix-be/growthbook-bundle.git", "reference": "ecb6f8166def3ade39d9ee88ee811441210a77a2"}, "dist": {"type": "zip", "url": "https://satis.pepita.io/dist/getrix-be/growthbook-bundle/getrix-be-growthbook-bundle-v1.3.1-748e75.zip", "reference": "ecb6f8166def3ade39d9ee88ee811441210a77a2", "shasum": "65685390f2985425ea0c83368c8793ec62e38059"}, "require": {"ext-json": "*", "growthbook/growthbook": "^1.7", "php": ">=7.4", "symfony/cache": "^3.4", "symfony/framework-bundle": "^3.4", "symfony/yaml": "^3.4"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.4", "phpunit/phpunit": "^9.6"}, "type": "bundle", "autoload": {"psr-4": {"GetrixBe\\GrowthbookBundle\\": "src/"}}, "license": ["proprietary"], "authors": [{"name": "Immobiliare Labs", "email": "<EMAIL>"}, {"name": "Getrix Team", "email": "<EMAIL>"}], "description": "B2B Growthbook SDK bundle", "time": "2025-07-04T13:29:44+00:00"}, {"name": "getrix/object-mapper", "version": "v1.0.2", "source": {"type": "git", "url": "https://guest:i4mG1t!!@gitlab.pepita.io/getrix/object-mapper.git", "reference": "1de939f56c8e754ec1fe2948bf683a9cbb109da2"}, "dist": {"type": "zip", "url": "https://satis.pepita.io/dist/getrix/object-mapper/getrix-object-mapper-v1.0.2-adb4e7.zip", "reference": "1de939f56c8e754ec1fe2948bf683a9cbb109da2", "shasum": "d08d6cc47746275bacef6cf2e14541e43039c65c"}, "require": {"php": ">=7.0"}, "require-dev": {"symfony/debug": "^3.4", "symfony/var-dumper": "^3.4"}, "type": "library", "autoload": {"psr-4": {"ObjectMapper\\": "src/"}}, "autoload-dev": {"psr-4": {"ObjectMapper\\Test\\": "tests/", "ObjectMapper\\": "tests/"}}, "authors": [{"name": "ImmobiliareLabs", "email": "<EMAIL>"}], "time": "2022-08-03T09:07:16+00:00"}, {"name": "getrix/pnformatter", "version": "v0.0.8", "source": {"type": "git", "url": "https://guest:i4mG1t!!@gitlab.pepita.io/getrix/pnformatter.git", "reference": "1647ca451c2271de05569c5fff2f9b48dd0ad305"}, "dist": {"type": "zip", "url": "https://satis.pepita.io/dist/getrix/pnformatter/getrix-pnformatter-v0.0.8-511710.zip", "reference": "1647ca451c2271de05569c5fff2f9b48dd0ad305", "shasum": "df7816f8bc34a9996c2a0422e265fe5715ccc6e6"}, "require-dev": {"phpunit/php-code-coverage": "^4.0.4", "phpunit/phpunit": "5.7.*"}, "type": "library", "autoload": {"classmap": ["src/"]}, "license": ["proprietary"], "authors": [{"name": "ImmobiliareLabs", "email": "<EMAIL>"}], "description": "php phone number formatter", "homepage": "http://gitlab.rete.farm/getrix/pnformatter", "time": "2018-03-09T16:47:15+00:00"}, {"name": "growthbook/growthbook", "version": "v1.7.1", "source": {"type": "git", "url": "https://github.com/growthbook/growthbook-php.git", "reference": "ee81a71231b3aee4da1cd4c51afb8dd06894baa7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/growthbook/growthbook-php/zipball/ee81a71231b3aee4da1cd4c51afb8dd06894baa7", "reference": "ee81a71231b3aee4da1cd4c51afb8dd06894baa7", "shasum": ""}, "require": {"ext-json": "*", "php-http/discovery": "^1.15", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0|^2.0", "psr/log": "^1.0|^2.0|^3.0", "psr/simple-cache": "^1.0|^2.0|^3.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0.0", "phpstan/phpstan": "^0.12.86", "phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"Growthbook\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHP SDK for GrowthBook, the feature flagging and A/B testing platform", "support": {"issues": "https://github.com/growthbook/growthbook-php/issues", "source": "https://github.com/growthbook/growthbook-php/tree/v1.7.1"}, "time": "2025-03-27T23:08:37+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.8.2", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "f4152d9eb85c445fe1f992001d1748e8bec070d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/f4152d9eb85c445fe1f992001d1748e8bec070d2", "reference": "f4152d9eb85c445fe1f992001d1748e8bec070d2", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^1.9.1 || ^2.6.3", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.8.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2024-07-18T11:12:18+00:00"}, {"name": "guzzlehttp/promises", "version": "1.5.3", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "67ab6e18aaa14d753cc148911d273f6e6cb6721e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/67ab6e18aaa14d753cc148911d273f6e6cb6721e", "reference": "67ab6e18aaa14d753cc148911d273f6e6cb6721e", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2023-05-21T12:31:43+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c2270caaabe631b3b44c85f99e5a04bbb8060d16", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2025-03-27T12:30:47+00:00"}, {"name": "indomio-translations/cms", "version": "v1.84.0", "source": {"type": "git", "url": "https://guest:i4mG1t!!@gitlab.pepita.io/indomio/translations/cms.git", "reference": "0ad9bdd340119ae1d64bbd905d47924ab72e57f6"}, "dist": {"type": "zip", "url": "https://satis.pepita.io/dist/indomio-translations/cms/indomio-translations-cms-v1.84.0-49e181.zip", "reference": "0ad9bdd340119ae1d64bbd905d47924ab72e57f6", "shasum": "909392e31919bfc4cabd1bcb6d8a7223f64ca772"}, "require": {"php": ">=5.2.4"}, "type": "library", "authors": [{"name": "immobiliare", "email": "<EMAIL>"}], "description": "Contiene i vari file con le traduzioni del cms di Indomio", "time": "2025-08-04T08:51:34+00:00"}, {"name": "indomio-translations/cms-contents", "version": "v1.13.9", "source": {"type": "git", "url": "https://guest:i4mG1t!!@gitlab.pepita.io/indomio/translations/cms-contents.git", "reference": "3731fb26aaa6f22a0e927c2cd32f3f41afab041d"}, "dist": {"type": "zip", "url": "https://satis.pepita.io/dist/indomio-translations/cms-contents/indomio-translations-cms-contents-v1.13.9-8c1c11.zip", "reference": "3731fb26aaa6f22a0e927c2cd32f3f41afab041d", "shasum": "33c7c05964ed4772669218c8dbdb08c9946fe4c6"}, "require": {"php": ">=5.2.4"}, "type": "library", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Database translations set used in gestionale product", "time": "2025-07-29T07:40:02+00:00"}, {"name": "kylekatarnls/update-helper", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/kylekatarnls/update-helper.git", "reference": "429be50660ed8a196e0798e5939760f168ec8ce9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kylekatarnls/update-helper/zipball/429be50660ed8a196e0798e5939760f168ec8ce9", "reference": "429be50660ed8a196e0798e5939760f168ec8ce9", "shasum": ""}, "require": {"composer-plugin-api": "^1.1.0 || ^2.0.0", "php": ">=5.3.0"}, "require-dev": {"codeclimate/php-test-reporter": "dev-master", "composer/composer": "2.0.x-dev || ^2.0.0-dev", "phpunit/phpunit": ">=4.8.35 <6.0"}, "type": "composer-plugin", "extra": {"class": "UpdateHelper\\ComposerPlugin"}, "autoload": {"psr-0": {"UpdateHelper\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Update helper", "support": {"issues": "https://github.com/kylekatarnls/update-helper/issues", "source": "https://github.com/kylekatarnls/update-helper/tree/1.2.1"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2020-04-07T20:44:10+00:00"}, {"name": "maxakawizard/po-parser", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/MAXakaWIZARD/PoParser.git", "reference": "e002b6219a0105de0c5e09948bca93e3ed5fb8b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MAXakaWIZARD/PoParser/zipball/e002b6219a0105de0c5e09948bca93e3ed5fb8b6", "reference": "e002b6219a0105de0c5e09948bca93e3ed5fb8b6", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"php-coveralls/php-coveralls": "~2.5", "phpunit/phpunit": ">7 <10", "squizlabs/php_codesniffer": "~3.0"}, "type": "library", "autoload": {"psr-4": {"PoParser\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Gettext *.po parser for PHP", "homepage": "http://github.com/MAXakaWIZARD/PoParser", "keywords": ["gettext", "i18n", "l10n", "po"], "support": {"issues": "https://github.com/MAXakaWIZARD/PoParser/issues", "source": "https://github.com/MAXakaWIZARD/PoParser/tree/1.4.0"}, "time": "2022-07-12T16:41:06+00:00"}, {"name": "monolog/monolog", "version": "1.27.1", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/904713c5929655dc9b97288b69cfeedad610c9a1", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpstan/phpstan": "^0.12.59", "phpunit/phpunit": "~4.5", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.27.1"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2022-06-09T08:53:42+00:00"}, {"name": "nesbot/carbon", "version": "1.39.1", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon.git", "reference": "4be0c005164249208ce1b5ca633cd57bdd42ff33"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/4be0c005164249208ce1b5ca633cd57bdd42ff33", "reference": "4be0c005164249208ce1b5ca633cd57bdd42ff33", "shasum": ""}, "require": {"kylekatarnls/update-helper": "^1.1", "php": ">=5.3.9", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}, "require-dev": {"composer/composer": "^1.2", "friendsofphp/php-cs-fixer": "~2", "phpunit/phpunit": "^4.8.35 || ^5.7"}, "bin": ["bin/upgrade-carbon"], "type": "library", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "update-helper": "Carbon\\Upgrade"}, "autoload": {"psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}], "description": "A simple API extension for DateTime.", "homepage": "http://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2019-10-14T05:51:36+00:00"}, {"name": "pepita/wsse-header-generator-bundle", "version": "1.0.1", "source": {"type": "git", "url": "https://guest:i4mG1t!!@gitlab.pepita.io/pepita/pepita-backend/wsse-header-generator-bundle-php.git", "reference": "fc553e907c3fb7a288790fcf0802d79572afab36"}, "dist": {"type": "zip", "url": "https://satis.pepita.io/dist/pepita/wsse-header-generator-bundle/pepita-wsse-header-generator-bundle-1.0.1-0b486e.zip", "reference": "fc553e907c3fb7a288790fcf0802d79572afab36", "shasum": "a98ac022d6a8dd8e8cd7b94bf01bf21abf1b9f8e"}, "require": {"pepita/wsse-header-generator-php": "^1.0", "php": "^7.0", "symfony/dependency-injection": "^3.4|^4.0", "symfony/framework-bundle": "^3.4|^4.0", "symfony/http-kernel": "^3.4|^4.0", "symfony/yaml": "^3.4|^4.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.15", "phpunit/phpunit": "^6.5|^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Pepita\\WsseHeaderGeneratorBundle\\": "src/"}}, "autoload-dev": {"psr-4": {"Test\\Pepita\\WsseHeaderGeneratorBundle\\": "tests/"}}, "scripts": {"test": ["vendor/bin/phpunit --no-coverage -c phpunit.xml"], "php-cs-fixer": ["vendor/bin/php-cs-fixer fix --verbose"], "php-cs-fixer-dry-run": ["vendor/bin/php-cs-fixer fix --verbose --diff --dry-run"]}, "license": ["proprietary"], "authors": [{"name": "ImmobiliareLabs", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple bundle for symfony framework for generate WSSE Header by command.", "keywords": ["bundle", "generator", "header", "symfony", "wsse", "wsse header", "wsse header generator"], "time": "2020-08-24T13:18:36+00:00"}, {"name": "pepita/wsse-header-generator-php", "version": "1.0.0", "source": {"type": "git", "url": "https://guest:i4mG1t!!@gitlab.pepita.io/pepita/pepita-backend/wsse-header-generator-php.git", "reference": "74d245e7490c3fe3aae24ceef44515bdbc36ec77"}, "dist": {"type": "zip", "url": "https://satis.pepita.io/dist/pepita/wsse-header-generator-php/pepita-wsse-header-generator-php-1.0.0-a57efa.zip", "reference": "74d245e7490c3fe3aae24ceef44515bdbc36ec77", "shasum": "2ad6c30d058023ac3e02a8509a6053a41ffd908a"}, "require": {"php": "^7.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.15", "phpunit/phpunit": "^6.5"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Pepita\\WsseHeaderGenerator\\": "src/"}}, "autoload-dev": {"psr-4": {"Test\\Pepita\\WsseHeaderGenerator\\": "tests/"}}, "scripts": {"test": ["vendor/bin/phpunit --no-coverage -c phpunit.xml"], "php-cs-fixer": ["vendor/bin/php-cs-fixer fix --verbose"], "php-cs-fixer-dry-run": ["vendor/bin/php-cs-fixer fix --verbose --diff --dry-run"]}, "license": ["proprietary"], "authors": [{"name": "ImmobiliareLabs", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple php library for generate WSSE Header", "keywords": ["generator", "header", "wsse", "wsse header", "wsse header generator"], "time": "2020-01-21T09:56:04+00:00"}, {"name": "php-http/discovery", "version": "1.20.0", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "82fe4c73ef3363caed49ff8dd1539ba06044910d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/82fe4c73ef3363caed49ff8dd1539ba06044910d", "reference": "82fe4c73ef3363caed49ff8dd1539ba06044910d", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0", "zendframework/zend-diactoros": "*"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "*", "psr/http-factory-implementation": "*", "psr/http-message-implementation": "*"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1 || ^7.3", "sebastian/comparator": "^3.0.5 || ^4.0.8", "symfony/phpunit-bridge": "^6.4.4 || ^7.0.1"}, "type": "composer-plugin", "extra": {"class": "Http\\Discovery\\Composer\\Plugin", "plugin-optional": true}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}, "exclude-from-classmap": ["src/Composer/Plugin.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds and installs PSR-7, PSR-17, PSR-18 and HTTPlug implementations", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr17", "psr7"], "support": {"issues": "https://github.com/php-http/discovery/issues", "source": "https://github.com/php-http/discovery/tree/1.20.0"}, "time": "2024-10-02T11:20:13+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "4.3.4", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "da3fd972d6bafd628114f7e7e036f45944b62e9c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/da3fd972d6bafd628114f7e7e036f45944b62e9c", "reference": "da3fd972d6bafd628114f7e7e036f45944b62e9c", "shasum": ""}, "require": {"php": "^7.0", "phpdocumentor/reflection-common": "^1.0.0 || ^2.0.0", "phpdocumentor/type-resolver": "~0.4 || ^1.0.0", "webmozart/assert": "^1.0"}, "require-dev": {"doctrine/instantiator": "^1.0.5", "mockery/mockery": "^1.0", "phpdocumentor/type-resolver": "0.4.*", "phpunit/phpunit": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/release/4.x"}, "time": "2019-12-28T18:55:12+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.10.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/679e3ce485b99e84c775d28e2e96fade9a7fb50a", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.3 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.18|^2.0"}, "require-dev": {"ext-tokenizer": "*", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^9.5", "rector/rector": "^0.13.9", "vimeo/psalm": "^4.25"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.10.0"}, "time": "2024-11-09T15:12:26+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "9b30d6fd026b2c132b3985ce6b23bec09ab3aa68"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/9b30d6fd026b2c132b3985ce6b23bec09ab3aa68", "reference": "9b30d6fd026b2c132b3985ce6b23bec09ab3aa68", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^5.3.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^9.6", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/2.1.0"}, "time": "2025-02-19T13:28:12+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "time": "2021-11-05T16:50:12+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/4ebe3a8bf773a19edfe0a84b6585ba3d401b724d", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/master"}, "time": "2016-10-10T12:19:37+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/collection", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/ramsey/collection.git", "reference": "ad7475d1c9e70b190ecffc58f2d989416af339b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/collection/zipball/ad7475d1c9e70b190ecffc58f2d989416af339b4", "reference": "ad7475d1c9e70b190ecffc58f2d989416af339b4", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "symfony/polyfill-php81": "^1.23"}, "require-dev": {"captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.28.3", "fakerphp/faker": "^1.21", "hamcrest/hamcrest-php": "^2.0", "jangregor/phpstan-prophecy": "^1.0", "mockery/mockery": "^1.5", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpcsstandards/phpcsutils": "^1.0.0-rc1", "phpspec/prophecy-phpunit": "^2.0", "phpstan/extension-installer": "^1.2", "phpstan/phpstan": "^1.9", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5", "psalm/plugin-mockery": "^1.1", "psalm/plugin-phpunit": "^0.18.4", "ramsey/coding-standard": "^2.0.3", "ramsey/conventional-commits": "^1.3", "vimeo/psalm": "^5.4"}, "type": "library", "extra": {"captainhook": {"force-install": true}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP library for representing and manipulating collections.", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/1.3.0"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/collection", "type": "tidelift"}], "time": "2022-12-27T19:12:24+00:00"}, {"name": "ramsey/uuid", "version": "4.2.3", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "fc9bb7fb5388691fd7373cd44dcb4d63bbcf24df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/fc9bb7fb5388691fd7373cd44dcb4d63bbcf24df", "reference": "fc9bb7fb5388691fd7373cd44dcb4d63bbcf24df", "shasum": ""}, "require": {"brick/math": "^0.8 || ^0.9", "ext-json": "*", "php": "^7.2 || ^8.0", "ramsey/collection": "^1.0", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php80": "^1.14"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"captainhook/captainhook": "^5.10", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.8", "ergebnis/composer-normalize": "^2.15", "mockery/mockery": "^1.3", "moontoast/math": "^1.1", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.2", "php-mock/php-mock-mockery": "^1.3", "php-parallel-lint/php-parallel-lint": "^1.1", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-mockery": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^8.5 || ^9", "slevomat/coding-standard": "^7.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.9"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-ctype": "Enables faster processing of character classification using ctype functions.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"captainhook": {"force-install": true}, "branch-alias": {"dev-main": "4.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.2.3"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/uuid", "type": "tidelift"}], "time": "2021-09-25T23:10:38+00:00"}, {"name": "sensio/framework-extra-bundle", "version": "v5.4.1", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioFrameworkExtraBundle.git", "reference": "585f4b3a1c54f24d1a8431c729fc8f5acca20c8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioFrameworkExtraBundle/zipball/585f4b3a1c54f24d1a8431c729fc8f5acca20c8a", "reference": "585f4b3a1c54f24d1a8431c729fc8f5acca20c8a", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/persistence": "^1.0", "php": ">=7.1.3", "symfony/config": "^3.4|^4.3", "symfony/dependency-injection": "^3.4|^4.3", "symfony/framework-bundle": "^3.4|^4.3", "symfony/http-kernel": "^3.4|^4.3"}, "require-dev": {"doctrine/doctrine-bundle": "^1.6", "doctrine/orm": "^2.5", "nyholm/psr7": "^1.1", "symfony/browser-kit": "^3.4|^4.3", "symfony/dom-crawler": "^3.4|^4.3", "symfony/expression-language": "^3.4|^4.3", "symfony/finder": "^3.4|^4.3", "symfony/monolog-bridge": "^3.0|^4.0", "symfony/monolog-bundle": "^3.2", "symfony/phpunit-bridge": "^3.4.19|^4.1.8", "symfony/psr-http-message-bridge": "^1.1", "symfony/security-bundle": "^3.4|^4.3", "symfony/twig-bundle": "^3.4|^4.3", "symfony/yaml": "^3.4|^4.3", "twig/twig": "~1.12|~2.0"}, "suggest": {"symfony/expression-language": "", "symfony/psr-http-message-bridge": "To use the PSR-7 converters", "symfony/security-bundle": ""}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "5.4.x-dev"}}, "autoload": {"psr-4": {"Sensio\\Bundle\\FrameworkExtraBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This bundle provides a way to configure your controllers with annotations", "keywords": ["annotations", "controllers"], "support": {"issues": "https://github.com/sensiolabs/SensioFrameworkExtraBundle/issues", "source": "https://github.com/sensiolabs/SensioFrameworkExtraBundle/tree/v5.4.1"}, "abandoned": "Symfony", "time": "2019-07-08T08:31:25+00:00"}, {"name": "sentry/sentry", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-php.git", "reference": "159eeaa02bb2ef8a8ec669f3c88e4bff7e6a7ffe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-php/zipball/159eeaa02bb2ef8a8ec669f3c88e4bff7e6a7ffe", "reference": "159eeaa02bb2ef8a8ec669f3c88e4bff7e6a7ffe", "shasum": ""}, "require": {"ext-curl": "*", "php": "^5.3|^7.0"}, "conflict": {"raven/raven": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^1.8.0", "monolog/monolog": "^1.0", "phpunit/phpunit": "^4.8.35 || ^5.7"}, "suggest": {"ext-hash": "*", "ext-json": "*", "ext-mbstring": "*", "monolog/monolog": "Automatically capture Monolog events as breadcrumbs"}, "bin": ["bin/sentry"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.11.x-dev"}}, "autoload": {"psr-0": {"Raven_": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PHP client for Sentry (http://getsentry.com)", "homepage": "http://getsentry.com", "keywords": ["log", "logging"], "support": {"issues": "https://github.com/getsentry/sentry-php/issues", "source": "https://github.com/getsentry/sentry-php/tree/1.11.0"}, "time": "2020-02-12T18:38:11+00:00"}, {"name": "sentry/sentry-symfony", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-symfony.git", "reference": "482c98d2699fe9cccd995bc1239894ecc5704633"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-symfony/zipball/482c98d2699fe9cccd995bc1239894ecc5704633", "reference": "482c98d2699fe9cccd995bc1239894ecc5704633", "shasum": ""}, "require": {"php": "^5.6|^7.0", "sentry/sentry": "^1.8", "symfony/config": "^2.7|^3.0", "symfony/console": "^2.7|^3.0", "symfony/dependency-injection": "^2.7|^3.0", "symfony/event-dispatcher": "^2.7|^3.0", "symfony/http-kernel": "^2.7|^3.0", "symfony/security-core": "^2.7|^3.0", "symfony/yaml": "^2.7|^3.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.5", "phpunit/phpunit": "^5.7|^6.5", "scrutinizer/ocular": "^1.5"}, "type": "symfony-bundle", "extra": {"branch-alias": {"master": "2.0.x-dev", "releases/1.x": "1.0.x-dev", "releases/0.8.x": "0.8.x-dev"}}, "autoload": {"psr-4": {"Sentry\\SentryBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Symfony integration for Sentry (http://getsentry.com)", "homepage": "http://getsentry.com", "keywords": ["errors", "logging", "sentry", "symfony"], "support": {"issues": "https://github.com/getsentry/sentry-symfony/issues", "source": "https://github.com/getsentry/sentry-symfony/tree/1.0.3"}, "time": "2018-06-01T08:48:53+00:00"}, {"name": "soa/stubs", "version": "20250604161148", "source": {"type": "git", "url": "https://guest:i4mG1t!!@gitlab.pepita.io/soa/stubs.git", "reference": "0352c90424eafa2da4be00e3cd2fe6ef1cd0f404"}, "dist": {"type": "zip", "url": "https://satis.pepita.io/dist/soa/stubs/soa-stubs-20250604161148-2df957.zip", "reference": "0352c90424eafa2da4be00e3cd2fe6ef1cd0f404", "shasum": "1cae2edaf334985fe89c11a2ba1d6d71b81f8575"}, "require": {"apache/thrift": "0.9.2", "php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"classmap": ["Services/", "Scribe/", "Facebook/"]}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Apache Thrift Stubs Library for server and clients", "homepage": "http://thrift.apache.org/", "support": {"email": "<EMAIL>", "issues": "https://issues.apache.org/jira/browse/THRIFT"}, "time": "2025-06-04T16:11:48+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.3.0", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/8a5d5072dca8f48460fce2f4131fcc495eec654c", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c", "shasum": ""}, "require": {"egulias/email-validator": "^2.0|^3.1", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.4"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "support": {"issues": "https://github.com/swiftmailer/swiftmailer/issues", "source": "https://github.com/swiftmailer/swiftmailer/tree/v6.3.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/swiftmailer/swiftmailer", "type": "tidelift"}], "abandoned": "symfony/mailer", "time": "2021-10-18T15:26:12+00:00"}, {"name": "symfony/asset", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/asset.git", "reference": "0970d65388724df88c982111ec37c08457506ce3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/asset/zipball/0970d65388724df88c982111ec37c08457506ce3", "reference": "0970d65388724df88c982111ec37c08457506ce3", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "require-dev": {"symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/http-kernel": "~2.8|~3.0|~4.0"}, "suggest": {"symfony/http-foundation": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Asset\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Asset Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/asset/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/cache", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "a7a14c4832760bd1fbd31be2859ffedc9b6ff813"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/a7a14c4832760bd1fbd31be2859ffedc9b6ff813", "reference": "a7a14c4832760bd1fbd31be2859ffedc9b6ff813", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/cache": "~1.0", "psr/log": "~1.0", "psr/simple-cache": "^1.0", "symfony/polyfill-apcu": "~1.1"}, "conflict": {"symfony/var-dumper": "<3.3"}, "provide": {"psr/cache-implementation": "1.0", "psr/simple-cache-implementation": "1.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/cache": "^1.6", "doctrine/dbal": "^2.4|^3.0", "predis/predis": "^1.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Cache component with PSR-6, PSR-16, and tags", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/class-loader", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/class-loader.git", "reference": "a22265a9f3511c0212bf79f54910ca5a77c0e92c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/class-loader/zipball/a22265a9f3511c0212bf79f54910ca5a77c0e92c", "reference": "a22265a9f3511c0212bf79f54910ca5a77c0e92c", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "require-dev": {"symfony/finder": "~2.8|~3.0|~4.0", "symfony/polyfill-apcu": "~1.1"}, "suggest": {"symfony/polyfill-apcu": "For using ApcClassLoader on HHVM"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ClassLoader\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony ClassLoader Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/class-loader/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "abandoned": true, "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/config", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "bc6b3fd3930d4b53a60b42fe2ed6fc466b75f03f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/bc6b3fd3930d4b53a60b42fe2ed6fc466b75f03f", "reference": "bc6b3fd3930d4b53a60b42fe2ed6fc466b75f03f", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/filesystem": "~2.8|~3.0|~4.0", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/dependency-injection": "<3.3", "symfony/finder": "<3.3"}, "require-dev": {"symfony/dependency-injection": "~3.3|~4.0", "symfony/event-dispatcher": "~3.3|~4.0", "symfony/finder": "~3.3|~4.0", "symfony/yaml": "~3.0|~4.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Config Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/console", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "a10b1da6fc93080c180bba7219b5ff5b7518fe81"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/a10b1da6fc93080c180bba7219b5ff5b7518fe81", "reference": "a10b1da6fc93080c180bba7219b5ff5b7518fe81", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/debug": "~2.8|~3.0|~4.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~3.3|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/lock": "~3.4|~4.0", "symfony/process": "~3.3|~4.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/console/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/css-selector", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "da3d9da2ce0026771f5fe64cb332158f1bd2bc33"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/da3d9da2ce0026771f5fe64cb332158f1bd2bc33", "reference": "da3d9da2ce0026771f5fe64cb332158f1bd2bc33", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony CssSelector Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/debug", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "ab42889de57fdfcfcc0759ab102e2fd4ea72dcae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/ab42889de57fdfcfcc0759ab102e2fd4ea72dcae", "reference": "ab42889de57fdfcfcc0759ab102e2fd4ea72dcae", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/log": "~1.0"}, "conflict": {"symfony/http-kernel": ">=2.3,<2.3.24|~2.4.0|>=2.5,<2.5.9|>=2.6,<2.6.2"}, "require-dev": {"symfony/http-kernel": "~2.8|~3.0|~4.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Debug Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/debug/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "abandoned": "symfony/error-handler", "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/dependency-injection", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "51d2a2708c6ceadad84393f8581df1dcf9e5e84b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/51d2a2708c6ceadad84393f8581df1dcf9e5e84b", "reference": "51d2a2708c6ceadad84393f8581df1dcf9e5e84b", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/container": "^1.0"}, "conflict": {"symfony/config": "<3.3.7", "symfony/finder": "<3.3", "symfony/proxy-manager-bridge": "<3.4", "symfony/yaml": "<3.4"}, "provide": {"psr/container-implementation": "1.0"}, "require-dev": {"symfony/config": "~3.3|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"symfony/config": "", "symfony/expression-language": "For using expressions in service container configuration", "symfony/finder": "For using double-star glob patterns or when GLOB_BRACE portability is required", "symfony/proxy-manager-bridge": "Generate service proxies to lazy load them", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DependencyInjection Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dependency-injection/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.4", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "605389f2a7e5625f273b53960dc46aeaf9c62918"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/605389f2a7e5625f273b53960dc46aeaf9c62918", "reference": "605389f2a7e5625f273b53960dc46aeaf9c62918", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/dom-crawler", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "ef97bcfbae5b384b4ca6c8d57b617722f15241a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/ef97bcfbae5b384b4ca6c8d57b617722f15241a6", "reference": "ef97bcfbae5b384b4ca6c8d57b617722f15241a6", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"symfony/css-selector": "~2.8|~3.0|~4.0"}, "suggest": {"symfony/css-selector": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DomCrawler Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dom-crawler/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/dotenv", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/dotenv.git", "reference": "1022723ac4f56b001d99691d96c6025dbf1404f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dotenv/zipball/1022723ac4f56b001d99691d96c6025dbf1404f1", "reference": "1022723ac4f56b001d99691d96c6025dbf1404f1", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "require-dev": {"symfony/process": "^3.4.2|^4.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Dotenv\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Registers environment variables from a .env file", "homepage": "https://symfony.com", "keywords": ["dotenv", "env", "environment"], "support": {"source": "https://github.com/symfony/dotenv/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/event-dispatcher", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "31fde73757b6bad247c54597beef974919ec6860"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/31fde73757b6bad247c54597beef974919ec6860", "reference": "31fde73757b6bad247c54597beef974919ec6860", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "conflict": {"symfony/dependency-injection": "<3.3"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/debug": "~3.4|~4.4", "symfony/dependency-injection": "~3.3|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/stopwatch": "~2.8|~3.0|~4.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony EventDispatcher Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/expression-language", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/expression-language.git", "reference": "de38e66398fca1fcb9c48e80279910e6889cb28f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/expression-language/zipball/de38e66398fca1fcb9c48e80279910e6889cb28f", "reference": "de38e66398fca1fcb9c48e80279910e6889cb28f", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/cache": "~3.1|~4.0", "symfony/polyfill-php70": "~1.6"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ExpressionLanguage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony ExpressionLanguage Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/expression-language/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/filesystem", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "e58d7841cddfed6e846829040dca2cca0ebbbbb3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/e58d7841cddfed6e846829040dca2cca0ebbbbb3", "reference": "e58d7841cddfed6e846829040dca2cca0ebbbbb3", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Filesystem Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/finder", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "b6b6ad3db3edb1b4b1c1896b1975fb684994de6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/b6b6ad3db3edb1b4b1c1896b1975fb684994de6e", "reference": "b6b6ad3db3edb1b4b1c1896b1975fb684994de6e", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-11-16T17:02:08+00:00"}, {"name": "symfony/flex", "version": "v1.22.0", "source": {"type": "git", "url": "https://github.com/symfony/flex.git", "reference": "5cc985971b1a700cb74bedd9e01cfa93eb4747f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/flex/zipball/5cc985971b1a700cb74bedd9e01cfa93eb4747f7", "reference": "5cc985971b1a700cb74bedd9e01cfa93eb4747f7", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": ">=7.1"}, "conflict": {"composer/semver": "<1.7.2"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "symfony/dotenv": "^4.4|^5.0|^6.0", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/phpunit-bridge": "^4.4.12|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0"}, "type": "composer-plugin", "extra": {"class": "Symfony\\Flex\\Flex"}, "autoload": {"psr-4": {"Symfony\\Flex\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Composer plugin for Symfony", "support": {"issues": "https://github.com/symfony/flex/issues", "source": "https://github.com/symfony/flex/tree/v1.22.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-03T07:50:24+00:00"}, {"name": "symfony/form", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/form.git", "reference": "62e841f089ec485e5ee425308b56b6ce2b5d11fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/form/zipball/62e841f089ec485e5ee425308b56b6ce2b5d11fa", "reference": "62e841f089ec485e5ee425308b56b6ce2b5d11fa", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/intl": "^2.8.18|^3.2.5|~4.0", "symfony/options-resolver": "~3.4|~4.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/property-access": "~2.8|~3.0|~4.0"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/dependency-injection": "<3.3", "symfony/doctrine-bridge": "<2.7", "symfony/framework-bundle": "<3.4", "symfony/http-kernel": "<3.3.5", "symfony/twig-bridge": "<3.4.5|<4.0.5,>=4.0"}, "require-dev": {"doctrine/collections": "~1.0", "symfony/config": "~2.7|~3.0|~4.0", "symfony/console": "~3.4|~4.0", "symfony/dependency-injection": "~3.3|~4.0", "symfony/expression-language": "~3.4|~4.0", "symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/http-kernel": "^3.3.5|~4.0", "symfony/security-csrf": "^2.8.31|^3.3.13|~4.0", "symfony/translation": "~2.8|~3.0|~4.0", "symfony/validator": "^3.4.44|^4.0.3", "symfony/var-dumper": "~3.3.11|~3.4|~4.0"}, "suggest": {"symfony/framework-bundle": "For templating with PHP.", "symfony/security-csrf": "For protecting forms against CSRF attacks.", "symfony/twig-bridge": "For templating with <PERSON><PERSON>.", "symfony/validator": "For form validation."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Form\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Form Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/form/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-28T05:23:51+00:00"}, {"name": "symfony/framework-bundle", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/framework-bundle.git", "reference": "6c95e747b75ddd2af61152ce93bf87299d15710e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/framework-bundle/zipball/6c95e747b75ddd2af61152ce93bf87299d15710e", "reference": "6c95e747b75ddd2af61152ce93bf87299d15710e", "shasum": ""}, "require": {"ext-xml": "*", "php": "^5.5.9|>=7.0.8", "symfony/cache": "~3.4.31|^4.3.4", "symfony/class-loader": "~3.2", "symfony/config": "^3.4.31|^4.3.4", "symfony/debug": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "^3.4.24|^4.2.5", "symfony/event-dispatcher": "~3.4|~4.0", "symfony/filesystem": "~2.8|~3.0|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/http-foundation": "^3.4.38|^4.3", "symfony/http-kernel": "^3.4.44|^4.3.4", "symfony/polyfill-mbstring": "~1.0", "symfony/routing": "^3.4.5|^4.0.5"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.0", "phpdocumentor/type-resolver": "<0.2.1", "phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/asset": "<3.3", "symfony/console": "<3.4", "symfony/form": "<3.4", "symfony/property-info": "<3.3", "symfony/serializer": "<3.3", "symfony/stopwatch": "<3.4", "symfony/translation": "<3.4", "symfony/validator": "<3.4", "symfony/workflow": "<3.3"}, "require-dev": {"doctrine/annotations": "~1.7", "doctrine/cache": "~1.0", "fig/link-util": "^1.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0", "symfony/asset": "~3.3|~4.0", "symfony/browser-kit": "~2.8|~3.0|~4.0", "symfony/console": "~3.4.31|^4.3.4", "symfony/css-selector": "~2.8|~3.0|~4.0", "symfony/dom-crawler": "~2.8|~3.0|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/form": "^3.4.31|^4.3.4", "symfony/lock": "~3.4|~4.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/process": "~2.8|~3.0|~4.0", "symfony/property-info": "~3.3|~4.0", "symfony/security-core": "~3.2|~4.0", "symfony/security-csrf": "^2.8.31|^3.3.13|~4.0", "symfony/serializer": "~3.3|~4.0", "symfony/stopwatch": "~3.4|~4.0", "symfony/templating": "~2.8|~3.0|~4.0", "symfony/translation": "~3.4|~4.0", "symfony/validator": "~3.4|~4.0", "symfony/var-dumper": "~3.3|~4.0", "symfony/web-link": "~3.3|~4.0", "symfony/workflow": "~3.3|~4.0", "symfony/yaml": "~3.2|~4.0", "twig/twig": "~1.34|~2.4"}, "suggest": {"ext-apcu": "For best performance of the system caches", "symfony/console": "For using the console commands", "symfony/form": "For using forms", "symfony/property-info": "For using the property_info service", "symfony/serializer": "For using the serializer service", "symfony/validator": "For using validation", "symfony/web-link": "For using web links, features such as preloading, prefetching or prerendering", "symfony/yaml": "For using the debug:config and lint:yaml commands"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\FrameworkBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony FrameworkBundle", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/framework-bundle/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/http-foundation", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "b9885fcce6fe494201da4f70a9309770e9d13dc8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/b9885fcce6fe494201da4f70a9309770e9d13dc8", "reference": "b9885fcce6fe494201da4f70a9309770e9d13dc8", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php70": "~1.6"}, "require-dev": {"symfony/expression-language": "~2.8|~3.0|~4.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpFoundation Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/http-kernel", "version": "v3.4.49", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "5aa72405f5bd5583c36ed6e756acb17d3f98ac40"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/5aa72405f5bd5583c36ed6e756acb17d3f98ac40", "reference": "5aa72405f5bd5583c36ed6e756acb17d3f98ac40", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/log": "~1.0", "symfony/debug": "^3.3.3|~4.0", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/http-foundation": "~3.4.12|~4.0.12|^4.1.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php56": "~1.8"}, "conflict": {"symfony/config": "<2.8", "symfony/dependency-injection": "<3.4.10|<4.0.10,>=4", "symfony/var-dumper": "<3.3", "twig/twig": "<1.34|<2.4,>=2"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/cache": "~1.0", "symfony/browser-kit": "~2.8|~3.0|~4.0", "symfony/class-loader": "~2.8|~3.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/console": "~2.8|~3.0|~4.0", "symfony/css-selector": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "^3.4.10|^4.0.10", "symfony/dom-crawler": "~2.8|~3.0|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/process": "~2.8|~3.0|~4.0", "symfony/routing": "~3.4|~4.0", "symfony/stopwatch": "~2.8|~3.0|~4.0", "symfony/templating": "~2.8|~3.0|~4.0", "symfony/translation": "~2.8|~3.0|~4.0", "symfony/var-dumper": "~3.3|~4.0"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": "", "symfony/finder": "", "symfony/var-dumper": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpKernel Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v3.4.49"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-05-19T12:06:59+00:00"}, {"name": "symfony/inflector", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/inflector.git", "reference": "b557c5d061b72cadf454dd87cd1308d0710c8021"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/inflector/zipball/b557c5d061b72cadf454dd87cd1308d0710c8021", "reference": "b557c5d061b72cadf454dd87cd1308d0710c8021", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Inflector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Inflector Component", "homepage": "https://symfony.com", "keywords": ["inflection", "pluralize", "singularize", "string", "symfony", "words"], "support": {"source": "https://github.com/symfony/inflector/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "abandoned": "EnglishInflector from the String component", "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/intl", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/intl.git", "reference": "c0e22a40039977f11dc4de03a853ab9450c2b4cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/intl/zipball/c0e22a40039977f11dc4de03a853ab9450c2b4cd", "reference": "c0e22a40039977f11dc4de03a853ab9450c2b4cd", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-intl-icu": "~1.0"}, "require-dev": {"symfony/filesystem": "~2.8|~3.0|~4.0"}, "suggest": {"ext-intl": "to use the component with locales other than \"en\""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Intl\\": ""}, "classmap": ["Resources/stubs"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A PHP replacement layer for the C intl extension that includes additional data from the ICU library.", "homepage": "https://symfony.com", "keywords": ["i18n", "icu", "internationalization", "intl", "l10n", "localization"], "support": {"source": "https://github.com/symfony/intl/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/lts", "version": "v3", "source": {"type": "git", "url": "https://github.com/symfony/lts.git", "reference": "3a4e88df038e3197e6b66d091d2495fd7d255c0b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/lts/zipball/3a4e88df038e3197e6b66d091d2495fd7d255c0b", "reference": "3a4e88df038e3197e6b66d091d2495fd7d255c0b", "shasum": ""}, "conflict": {"symfony/asset": ">=4", "symfony/browser-kit": ">=4", "symfony/cache": ">=4", "symfony/class-loader": ">=4", "symfony/config": ">=4", "symfony/console": ">=4", "symfony/css-selector": ">=4", "symfony/debug": ">=4", "symfony/debug-bundle": ">=4", "symfony/dependency-injection": ">=4", "symfony/doctrine-bridge": ">=4", "symfony/dom-crawler": ">=4", "symfony/dotenv": ">=4", "symfony/event-dispatcher": ">=4", "symfony/expression-language": ">=4", "symfony/filesystem": ">=4", "symfony/finder": ">=4", "symfony/form": ">=4", "symfony/framework-bundle": ">=4", "symfony/http-foundation": ">=4", "symfony/http-kernel": ">=4", "symfony/inflector": ">=4", "symfony/intl": ">=4", "symfony/ldap": ">=4", "symfony/lock": ">=4", "symfony/monolog-bridge": ">=4", "symfony/options-resolver": ">=4", "symfony/process": ">=4", "symfony/property-access": ">=4", "symfony/property-info": ">=4", "symfony/proxy-manager-bridge": ">=4", "symfony/routing": ">=4", "symfony/security": ">=4", "symfony/security-bundle": ">=4", "symfony/security-core": ">=4", "symfony/security-csrf": ">=4", "symfony/security-guard": ">=4", "symfony/security-http": ">=4", "symfony/serializer": ">=4", "symfony/stopwatch": ">=4", "symfony/symfony": ">=4", "symfony/templating": ">=4", "symfony/translation": ">=4", "symfony/twig-bridge": ">=4", "symfony/twig-bundle": ">=4", "symfony/validator": ">=4", "symfony/var-dumper": ">=4", "symfony/web-link": ">=4", "symfony/web-profiler-bundle": ">=4", "symfony/web-server-bundle": ">=4", "symfony/workflow": ">=4", "symfony/yaml": ">=4"}, "type": "metapackage", "extra": {"branch-alias": {"dev-master": "3-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Enforces Long Term Supported versions of Symfony components", "homepage": "https://symfony.com", "support": {"issues": "https://github.com/symfony/lts/issues", "source": "https://github.com/symfony/lts/tree/master"}, "abandoned": "symfony/flex", "time": "2017-10-19T02:02:36+00:00"}, {"name": "symfony/monolog-bridge", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bridge.git", "reference": "93915f0d981bc166dfa475698124435327f6ee63"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bridge/zipball/93915f0d981bc166dfa475698124435327f6ee63", "reference": "93915f0d981bc166dfa475698124435327f6ee63", "shasum": ""}, "require": {"monolog/monolog": "~1.19", "php": "^5.5.9|>=7.0.8", "symfony/http-kernel": "~2.8|~3.0|~4.0"}, "conflict": {"symfony/console": "<2.8", "symfony/http-foundation": "<3.3"}, "require-dev": {"symfony/console": "~2.8|~3.0|~4.0", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/security-core": "~2.8|~3.0|~4.0", "symfony/var-dumper": "~3.3|~4.0"}, "suggest": {"symfony/console": "For the possibility to show log messages in console commands depending on verbosity settings. You need version ^2.8 of the console for it.", "symfony/event-dispatcher": "Needed when using log messages in console commands.", "symfony/http-kernel": "For using the debugging handlers together with the response life cycle of the HTTP kernel.", "symfony/var-dumper": "For using the debugging handlers like the console handler or the log server handler."}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Monolog\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Monolog Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/monolog-bridge/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/monolog-bundle", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bundle.git", "reference": "e495f5c7e4e672ffef4357d4a4d85f010802f940"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bundle/zipball/e495f5c7e4e672ffef4357d4a4d85f010802f940", "reference": "e495f5c7e4e672ffef4357d4a4d85f010802f940", "shasum": ""}, "require": {"monolog/monolog": "~1.22 || ~2.0", "php": ">=5.6", "symfony/config": "~3.4 || ~4.0 || ^5.0", "symfony/dependency-injection": "~3.4.10 || ^4.0.10 || ^5.0", "symfony/http-kernel": "~3.4 || ~4.0 || ^5.0", "symfony/monolog-bridge": "~3.4 || ~4.0 || ^5.0"}, "require-dev": {"symfony/console": "~3.4 || ~4.0 || ^5.0", "symfony/phpunit-bridge": "^4.4 || ^5.0", "symfony/yaml": "~3.4 || ~4.0 || ^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MonologBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony MonologBundle", "homepage": "http://symfony.com", "keywords": ["log", "logging"], "support": {"issues": "https://github.com/symfony/monolog-bundle/issues", "source": "https://github.com/symfony/monolog-bundle/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-06T15:12:11+00:00"}, {"name": "symfony/options-resolver", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "c7efc97a47b2ebaabc19d5b6c6b50f5c37c92744"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/c7efc97a47b2ebaabc19d5b6c6b50f5c37c92744", "reference": "c7efc97a47b2ebaabc19d5b6c6b50f5c37c92744", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony OptionsResolver Component", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/polyfill-apcu", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-apcu.git", "reference": "9cba05c714911d416f478a74d1758865f7373c3f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-apcu/zipball/9cba05c714911d416f478a74d1758865f7373c3f", "reference": "9cba05c714911d416f478a74d1758865f7373c3f", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Apcu\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting apcu_* functions to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["apcu", "compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-apcu/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-10T14:38:51+00:00"}, {"name": "symfony/polyfill-intl-icu", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-icu.git", "reference": "763d2a91fea5681509ca01acbc1c5e450d127811"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-icu/zipball/763d2a91fea5681509ca01acbc1c5e450d127811", "reference": "763d2a91fea5681509ca01acbc1c5e450d127811", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance and support of other locales than \"en\""}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Icu\\": ""}, "classmap": ["Resources/stubs"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's ICU-related data and classes", "homepage": "https://symfony.com", "keywords": ["compatibility", "icu", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-icu/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-21T18:38:29+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/9614ac4d8061dc257ecc64cba1b140873dce8ad3", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-10T14:38:51+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-23T08:48:59+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce", "reference": "fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "metapackage", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-02T08:10:11+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/property-access", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "f1dc91d0c987f3ba95be1d7874527d11477b25ff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/f1dc91d0c987f3ba95be1d7874527d11477b25ff", "reference": "f1dc91d0c987f3ba95be1d7874527d11477b25ff", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/inflector": "~3.1|~4.0", "symfony/polyfill-php70": "~1.0"}, "require-dev": {"symfony/cache": "~3.1|~4.0"}, "suggest": {"psr/cache-implementation": "To cache access methods."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony PropertyAccess Component", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property path", "reflection"], "support": {"source": "https://github.com/symfony/property-access/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/property-info", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "a5f1e77c881342a5b1e05fdc12642650853bd112"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/a5f1e77c881342a5b1e05fdc12642650853bd112", "reference": "a5f1e77c881342a5b1e05fdc12642650853bd112", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/inflector": "~3.1|~4.0"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.0||>=3.2.0,<3.2.2", "phpdocumentor/type-resolver": "<0.3.0", "symfony/dependency-injection": "<3.3"}, "require-dev": {"doctrine/annotations": "~1.7", "phpdocumentor/reflection-docblock": "^3.0|^4.0", "symfony/cache": "~3.1|~4.0", "symfony/dependency-injection": "~3.3|~4.0", "symfony/serializer": "~2.8|~3.0|~4.0"}, "suggest": {"phpdocumentor/reflection-docblock": "To use the PHPDoc", "psr/cache-implementation": "To cache results", "symfony/doctrine-bridge": "To use Doctrine metadata", "symfony/serializer": "To use Serializer metadata"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Property Info Component", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "support": {"source": "https://github.com/symfony/property-info/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/routing", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "3e522ac69cadffd8131cc2b22157fa7662331a6c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/3e522ac69cadffd8131cc2b22157fa7662331a6c", "reference": "3e522ac69cadffd8131cc2b22157fa7662331a6c", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "conflict": {"symfony/config": "<3.3.1", "symfony/dependency-injection": "<3.3", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.0", "psr/log": "~1.0", "symfony/config": "^3.3.1|~4.0", "symfony/dependency-injection": "~3.3|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Routing Component", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/security", "version": "v3.4.49", "source": {"type": "git", "url": "https://github.com/symfony/security.git", "reference": "ac1af40e3e0a183f80f0ff8c3d22b18bfb74dbb3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security/zipball/ac1af40e3e0a183f80f0ff8c3d22b18bfb74dbb3", "reference": "ac1af40e3e0a183f80f0ff8c3d22b18bfb74dbb3", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/http-foundation": "~3.4.40|^4.4.7", "symfony/http-kernel": "~3.3|~4.0", "symfony/polyfill-php56": "~1.0", "symfony/polyfill-php70": "~1.0", "symfony/property-access": "~2.8|~3.0|~4.0"}, "replace": {"symfony/security-core": "self.version", "symfony/security-csrf": "self.version", "symfony/security-guard": "self.version", "symfony/security-http": "self.version"}, "require-dev": {"psr/container": "^1.0", "psr/log": "~1.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/ldap": "~3.1|~4.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-icu": "~1.0", "symfony/routing": "~2.8|~3.0|~4.0", "symfony/validator": "^3.2.5|~4.0"}, "suggest": {"psr/container-implementation": "To instantiate the Security class", "symfony/expression-language": "For using the expression voter", "symfony/form": "", "symfony/ldap": "For using the LDAP user and authentication providers", "symfony/routing": "For using the HttpUtils class to create sub-requests, redirect the user, and match URLs", "symfony/validator": "For using the user password constraint"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\": ""}, "exclude-from-classmap": ["/Core/Tests/", "/Csrf/Tests/", "/Guard/Tests/", "/Http/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security/tree/v3.4.49"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "abandoned": true, "time": "2021-05-19T12:03:21+00:00"}, {"name": "symfony/security-bundle", "version": "v3.4.48", "source": {"type": "git", "url": "https://github.com/symfony/security-bundle.git", "reference": "84355eff4dd84293aa872402355479ac8cf888ee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-bundle/zipball/84355eff4dd84293aa872402355479ac8cf888ee", "reference": "84355eff4dd84293aa872402355479ac8cf888ee", "shasum": ""}, "require": {"ext-xml": "*", "php": "^5.5.9|>=7.0.8", "symfony/config": "~3.4|~4.0", "symfony/dependency-injection": "^3.4.3|^4.0.3", "symfony/http-kernel": "~3.4|~4.0", "symfony/polyfill-php70": "~1.0", "symfony/security": "~3.4.38|~4.3.10|^4.4.5"}, "conflict": {"symfony/console": "<3.4", "symfony/event-dispatcher": "<3.4", "symfony/framework-bundle": "<3.4", "symfony/var-dumper": "<3.3"}, "require-dev": {"doctrine/doctrine-bundle": "~1.5", "symfony/asset": "~2.8|~3.0|~4.0", "symfony/browser-kit": "~2.8|~3.0|~4.0", "symfony/console": "~3.4|~4.0", "symfony/css-selector": "~2.8|~3.0|~4.0", "symfony/dom-crawler": "~2.8|~3.0|~4.0", "symfony/event-dispatcher": "~3.4|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/form": "^3.4|~4.0", "symfony/framework-bundle": "~3.4|~4.0", "symfony/http-foundation": "~3.3|~4.0", "symfony/process": "~3.3|~4.0", "symfony/security-acl": "~2.8|~3.0", "symfony/translation": "~3.4|~4.0", "symfony/twig-bridge": "~3.4|~4.0", "symfony/twig-bundle": "~3.4|~4.0", "symfony/validator": "^3.4|~4.0", "symfony/var-dumper": "~3.3|~4.0", "symfony/yaml": "~3.4|~4.0", "twig/twig": "~1.41|~2.10"}, "suggest": {"symfony/security-acl": "For using the ACL functionality of this bundle"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\SecurityBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony SecurityBundle", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-bundle/tree/v3.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-29T09:39:14+00:00"}, {"name": "symfony/serializer", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "6d69ccc1dcfb64c1e9c9444588643e98718d1849"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/6d69ccc1dcfb64c1e9c9444588643e98718d1849", "reference": "6d69ccc1dcfb64c1e9c9444588643e98718d1849", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"phpdocumentor/type-resolver": "<0.2.1", "symfony/dependency-injection": "<3.2", "symfony/property-access": ">=3.0,<3.0.4|>=2.8,<2.8.4", "symfony/property-info": "<3.1", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.0", "doctrine/cache": "~1.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0", "symfony/cache": "~3.1|~4.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.2|~4.0", "symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/property-access": "~2.8|~3.0|~4.0", "symfony/property-info": "^3.4.13|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"doctrine/annotations": "For using the annotation mapping. You will also need doctrine/cache.", "doctrine/cache": "For using the default cached annotation reader and metadata cache.", "psr/cache-implementation": "For using the metadata cache.", "symfony/config": "For using the XML mapping loader.", "symfony/http-foundation": "For using a MIME type guesser within the DataUriNormalizer.", "symfony/property-access": "For using the ObjectNormalizer.", "symfony/property-info": "To deserialize relations.", "symfony/yaml": "For using the default YAML mapping loader."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Serializer Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/serializer/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/swiftmailer-bundle", "version": "v3.3.1", "source": {"type": "git", "url": "https://github.com/symfony/swiftmailer-bundle.git", "reference": "defa9bdfc0191ed70b389cb93c550c6c82cf1745"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/swiftmailer-bundle/zipball/defa9bdfc0191ed70b389cb93c550c6c82cf1745", "reference": "defa9bdfc0191ed70b389cb93c550c6c82cf1745", "shasum": ""}, "require": {"php": ">=7.0.0", "swiftmailer/swiftmailer": "^6.1.3", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/http-kernel": "^3.4|^4.0|^5.0"}, "conflict": {"twig/twig": "<1.41|<2.10"}, "require-dev": {"symfony/console": "^3.4|^4.0|^5.0", "symfony/framework-bundle": "^3.4|^4.0|^5.0", "symfony/phpunit-bridge": "^3.4.32|^4.3.5|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"psr/log": "Allows logging"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\SwiftmailerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony SwiftmailerBundle", "homepage": "http://symfony.com", "support": {"issues": "https://github.com/symfony/swiftmailer-bundle/issues", "source": "https://github.com/symfony/swiftmailer-bundle/tree/master"}, "abandoned": "symfony/mailer", "time": "2019-11-07T21:01:35+00:00"}, {"name": "symfony/templating", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/templating.git", "reference": "84ca10f95aaff084ae2bcfc5c21ae551af173d5a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/templating/zipball/84ca10f95aaff084ae2bcfc5c21ae551af173d5a", "reference": "84ca10f95aaff084ae2bcfc5c21ae551af173d5a", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8"}, "require-dev": {"psr/log": "~1.0"}, "suggest": {"psr/log-implementation": "For using debug logging in loaders"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Templating\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Templating Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/templating/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/translation", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "be83ee6c065cb32becdb306ba61160d598b1ce88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/be83ee6c065cb32becdb306ba61160d598b1ce88", "reference": "be83ee6c065cb32becdb306ba61160d598b1ce88", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/config": "<2.8", "symfony/dependency-injection": "<3.4", "symfony/yaml": "<3.4"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/http-kernel": "~3.4|~4.0", "symfony/intl": "^2.8.18|^3.2.5|~4.0", "symfony/var-dumper": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Translation Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/twig-bridge", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/twig-bridge.git", "reference": "090d19d6f1ea5b9e1d79f372785aa5e5c9cd4042"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bridge/zipball/090d19d6f1ea5b9e1d79f372785aa5e5c9cd4042", "reference": "090d19d6f1ea5b9e1d79f372785aa5e5c9cd4042", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "twig/twig": "^1.41|^2.10"}, "conflict": {"symfony/console": "<3.4", "symfony/form": "<3.4.31|>=4.0,<4.3.4"}, "require-dev": {"fig/link-util": "^1.0", "symfony/asset": "~2.8|~3.0|~4.0", "symfony/console": "~3.4|~4.0", "symfony/dependency-injection": "~2.8|~3.0|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/form": "^3.4.31|^4.3.4", "symfony/http-foundation": "^3.3.11|~4.0", "symfony/http-kernel": "~3.2|~4.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/routing": "~2.8|~3.0|~4.0", "symfony/security": "^2.8.31|^3.3.13|~4.0", "symfony/security-acl": "~2.8|~3.0", "symfony/stopwatch": "~2.8|~3.0|~4.0", "symfony/templating": "~2.8|~3.0|~4.0", "symfony/translation": "~2.8|~3.0|~4.0", "symfony/var-dumper": "~2.8.10|~3.1.4|~3.2|~4.0", "symfony/web-link": "~3.3|~4.0", "symfony/workflow": "~3.3|~4.0", "symfony/yaml": "~2.8|~3.0|~4.0"}, "suggest": {"symfony/asset": "For using the AssetExtension", "symfony/expression-language": "For using the ExpressionExtension", "symfony/finder": "", "symfony/form": "For using the FormExtension", "symfony/http-kernel": "For using the HttpKernelExtension", "symfony/routing": "For using the RoutingExtension", "symfony/security": "For using the SecurityExtension", "symfony/stopwatch": "For using the StopwatchExtension", "symfony/templating": "For using the TwigEngine", "symfony/translation": "For using the TranslationExtension", "symfony/var-dumper": "For using the DumpExtension", "symfony/web-link": "For using the WebLinkExtension", "symfony/yaml": "For using the YamlExtension"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Twig\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Twig Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bridge/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/twig-bundle", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/twig-bundle.git", "reference": "977b3096e2df96bc8a8d2329e83466cfc30c373d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bundle/zipball/977b3096e2df96bc8a8d2329e83466cfc30c373d", "reference": "977b3096e2df96bc8a8d2329e83466cfc30c373d", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/config": "~3.2|~4.0", "symfony/debug": "~2.8|~3.0|~4.0", "symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/http-kernel": "^3.3|~4.0", "symfony/polyfill-ctype": "~1.8", "symfony/twig-bridge": "^3.4.3|^4.0.3", "twig/twig": "~1.41|~2.10"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/event-dispatcher": "<3.3.1"}, "require-dev": {"doctrine/annotations": "~1.7", "doctrine/cache": "~1.0", "symfony/asset": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.4.24|^4.2.5", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/form": "~2.8|~3.0|~4.0", "symfony/framework-bundle": "^3.3.11|~4.0", "symfony/routing": "~2.8|~3.0|~4.0", "symfony/stopwatch": "~2.8|~3.0|~4.0", "symfony/templating": "~2.8|~3.0|~4.0", "symfony/web-link": "~3.3|~4.0", "symfony/yaml": "~2.8|~3.0|~4.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\TwigBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony TwigBundle", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bundle/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/validator", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "d25ceea5c99022aecf37adf157c76c31fc5dcbed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/d25ceea5c99022aecf37adf157c76c31fc5dcbed", "reference": "d25ceea5c99022aecf37adf157c76c31fc5dcbed", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/translation": "~2.8|~3.0|~4.0"}, "conflict": {"doctrine/lexer": "<1.0.2", "phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/dependency-injection": "<3.3", "symfony/http-kernel": "<3.3.5", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.7", "doctrine/cache": "~1.0", "egulias/email-validator": "^2.1.10", "symfony/cache": "~3.1|~4.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.3|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/http-kernel": "^3.3.5|~4.0", "symfony/intl": "^2.8.18|^3.2.5|~4.0", "symfony/property-access": "~2.8|~3.0|~4.0", "symfony/var-dumper": "~3.3|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"doctrine/annotations": "For using the annotation mapping. You will also need doctrine/cache.", "doctrine/cache": "For using the default cached annotation reader and metadata cache.", "egulias/email-validator": "Strict (RFC compliant) email validation", "psr/cache-implementation": "For using the metadata cache.", "symfony/config": "", "symfony/expression-language": "For using the Expression validator", "symfony/http-foundation": "", "symfony/intl": "", "symfony/property-access": "For accessing properties within comparison constraints", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Validator Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/validator/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-28T05:23:51+00:00"}, {"name": "symfony/yaml", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "88289caa3c166321883f67fe5130188ebbb47094"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/88289caa3c166321883f67fe5130188ebbb47094", "reference": "88289caa3c166321883f67fe5130188ebbb47094", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/console": "<3.4"}, "require-dev": {"symfony/console": "~3.4|~4.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "twig/extensions", "version": "v1.5.4", "source": {"type": "git", "url": "https://github.com/twigphp/Twig-extensions.git", "reference": "57873c8b0c1be51caa47df2cdb824490beb16202"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig-extensions/zipball/57873c8b0c1be51caa47df2cdb824490beb16202", "reference": "57873c8b0c1be51caa47df2cdb824490beb16202", "shasum": ""}, "require": {"twig/twig": "^1.27|^2.0"}, "require-dev": {"symfony/phpunit-bridge": "^3.4", "symfony/translation": "^2.7|^3.4"}, "suggest": {"symfony/translation": "Allow the time_diff output to be translated"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-0": {"Twig_Extensions_": "lib/"}, "psr-4": {"Twig\\Extensions\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common additional features for Twig that do not directly belong in core", "keywords": ["i18n", "text"], "support": {"issues": "https://github.com/twigphp/Twig-extensions/issues", "source": "https://github.com/twigphp/Twig-extensions/tree/master"}, "abandoned": true, "time": "2018-12-05T18:34:18+00:00"}, {"name": "twig/twig", "version": "v2.16.1", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "19185947ec75d433a3ac650af32fc05649b95ee1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/19185947ec75d433a3ac650af32fc05649b95ee1", "reference": "19185947ec75d433a3ac650af32fc05649b95ee1", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php72": "^1.8"}, "require-dev": {"psr/container": "^1.0", "symfony/phpunit-bridge": "^5.4.9|^6.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.16-dev"}}, "autoload": {"psr-0": {"Twig_": "lib/"}, "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v2.16.1"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2024-09-09T17:53:56+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}, {"name": "whichbrowser/parser", "version": "v2.1.8", "source": {"type": "git", "url": "https://github.com/WhichBrowser/Parser-PHP.git", "reference": "581d614d686bfbec3529ad60562a5213ac5d8d72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WhichBrowser/Parser-PHP/zipball/581d614d686bfbec3529ad60562a5213ac5d8d72", "reference": "581d614d686bfbec3529ad60562a5213ac5d8d72", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/cache": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"cache/array-adapter": "^1.1", "icomefromthenet/reverse-regex": "*******", "php-coveralls/php-coveralls": "^2.0", "phpunit/php-code-coverage": "^5.0 || ^7.0", "phpunit/phpunit": "^6.0 || ^8.0", "squizlabs/php_codesniffer": "^3.5", "symfony/yaml": "~3.4 || ~4.0"}, "suggest": {"cache/array-adapter": "Allows testing of the caching functionality"}, "type": "library", "autoload": {"psr-4": {"WhichBrowser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Useragent sniffing library for PHP", "homepage": "http://whichbrowser.net", "keywords": ["browser", "sniffing", "ua", "useragent"], "support": {"issues": "https://github.com/WhichBrowser/Parser-PHP/issues", "source": "https://github.com/WhichBrowser/Parser-PHP/tree/v2.1.8"}, "time": "2024-04-17T12:47:41+00:00"}], "packages-dev": [{"name": "amphp/amp", "version": "v2.6.4", "source": {"type": "git", "url": "https://github.com/amphp/amp.git", "reference": "ded3d9be08f526089eb7ee8d9f16a9768f9dec2d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/amp/zipball/ded3d9be08f526089eb7ee8d9f16a9768f9dec2d", "reference": "ded3d9be08f526089eb7ee8d9f16a9768f9dec2d", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"amphp/php-cs-fixer-config": "dev-master", "amphp/phpunit-util": "^1", "ext-json": "*", "jetbrains/phpstorm-stubs": "^2019.3", "phpunit/phpunit": "^7 | ^8 | ^9", "react/promise": "^2", "vimeo/psalm": "^3.12"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"files": ["lib/functions.php", "lib/Internal/functions.php"], "psr-4": {"Amp\\": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A non-blocking concurrency framework for PHP applications.", "homepage": "https://amphp.org/amp", "keywords": ["async", "asynchronous", "awaitable", "concurrency", "event", "event-loop", "future", "non-blocking", "promise"], "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v2.6.4"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-03-21T18:52:26+00:00"}, {"name": "amphp/byte-stream", "version": "v1.8.2", "source": {"type": "git", "url": "https://github.com/amphp/byte-stream.git", "reference": "4f0e968ba3798a423730f567b1b50d3441c16ddc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/byte-stream/zipball/4f0e968ba3798a423730f567b1b50d3441c16ddc", "reference": "4f0e968ba3798a423730f567b1b50d3441c16ddc", "shasum": ""}, "require": {"amphp/amp": "^2", "php": ">=7.1"}, "require-dev": {"amphp/php-cs-fixer-config": "dev-master", "amphp/phpunit-util": "^1.4", "friendsofphp/php-cs-fixer": "^2.3", "jetbrains/phpstorm-stubs": "^2019.3", "phpunit/phpunit": "^6 || ^7 || ^8", "psalm/phar": "^3.11.4"}, "type": "library", "autoload": {"files": ["lib/functions.php"], "psr-4": {"Amp\\ByteStream\\": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A stream abstraction to make working with non-blocking I/O simple.", "homepage": "https://amphp.org/byte-stream", "keywords": ["amp", "amphp", "async", "io", "non-blocking", "stream"], "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v1.8.2"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-04-13T18:00:56+00:00"}, {"name": "composer/package-versions-deprecated", "version": "*********", "source": {"type": "git", "url": "https://github.com/composer/package-versions-deprecated.git", "reference": "b4f54f74ef3453349c24a845d22392cd31e65f1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/package-versions-deprecated/zipball/b4f54f74ef3453349c24a845d22392cd31e65f1d", "reference": "b4f54f74ef3453349c24a845d22392cd31e65f1d", "shasum": ""}, "require": {"composer-plugin-api": "^1.1.0 || ^2.0", "php": "^7 || ^8"}, "replace": {"ocramius/package-versions": "1.11.99"}, "require-dev": {"composer/composer": "^1.9.3 || ^2.0@dev", "ext-zip": "^1.13", "phpunit/phpunit": "^6.5 || ^7"}, "type": "composer-plugin", "extra": {"class": "PackageVersions\\Installer", "branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"PackageVersions\\": "src/PackageVersions"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "Composer plugin that provides efficient querying for installed package versions (no runtime IO)", "support": {"issues": "https://github.com/composer/package-versions-deprecated/issues", "source": "https://github.com/composer/package-versions-deprecated/tree/*********"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-01-17T14:14:24+00:00"}, {"name": "composer/pcre", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "67a32d7d6f9f560b726ab25a061b38ff3a80c560"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/67a32d7d6f9f560b726ab25a061b38ff3a80c560", "reference": "67a32d7d6f9f560b726ab25a061b38ff3a80c560", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.3", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/1.0.1"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-01-21T20:24:37+00:00"}, {"name": "composer/semver", "version": "3.4.3", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-09-19T14:15:21+00:00"}, {"name": "composer/xdebug-handler", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "9e36aeed4616366d2b690bdce11f71e9178c579a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/9e36aeed4616366d2b690bdce11f71e9178c579a", "reference": "9e36aeed4616366d2b690bdce11f71e9178c579a", "shasum": ""}, "require": {"composer/pcre": "^1", "php": "^5.3.2 || ^7.0 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^4.2 || ^5.0 || ^6.0"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/2.0.5"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-02-24T20:20:32+00:00"}, {"name": "dnoegel/php-xdg-base-dir", "version": "v0.1.1", "source": {"type": "git", "url": "https://github.com/dnoegel/php-xdg-base-dir.git", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dnoegel/php-xdg-base-dir/zipball/8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~7.0|~6.0|~5.0|~4.8.35"}, "type": "library", "autoload": {"psr-4": {"XdgBaseDir\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "implementation of xdg base directory specification for php", "support": {"issues": "https://github.com/dnoegel/php-xdg-base-dir/issues", "source": "https://github.com/dnoegel/php-xdg-base-dir/tree/v0.1.1"}, "time": "2019-12-04T15:06:13+00:00"}, {"name": "doctrine/instantiator", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/0a0fa9780f5d4e507415a065172d26a98d02047b", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.30 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.5.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-12-30T00:15:36+00:00"}, {"name": "felixfbecker/advanced-json-rpc", "version": "v3.2.1", "source": {"type": "git", "url": "https://github.com/felixfbecker/php-advanced-json-rpc.git", "reference": "b5f37dbff9a8ad360ca341f3240dc1c168b45447"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/felixfbecker/php-advanced-json-rpc/zipball/b5f37dbff9a8ad360ca341f3240dc1c168b45447", "reference": "b5f37dbff9a8ad360ca341f3240dc1c168b45447", "shasum": ""}, "require": {"netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "php": "^7.1 || ^8.0", "phpdocumentor/reflection-docblock": "^4.3.4 || ^5.0.0"}, "require-dev": {"phpunit/phpunit": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"AdvancedJsonRpc\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A more advanced JSONRPC implementation", "support": {"issues": "https://github.com/felixfbecker/php-advanced-json-rpc/issues", "source": "https://github.com/felixfbecker/php-advanced-json-rpc/tree/v3.2.1"}, "time": "2021-06-11T22:34:44+00:00"}, {"name": "felixfbecker/language-server-protocol", "version": "v1.5.3", "source": {"type": "git", "url": "https://github.com/felixfbecker/php-language-server-protocol.git", "reference": "a9e113dbc7d849e35b8776da39edaf4313b7b6c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/felixfbecker/php-language-server-protocol/zipball/a9e113dbc7d849e35b8776da39edaf4313b7b6c9", "reference": "a9e113dbc7d849e35b8776da39edaf4313b7b6c9", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpstan/phpstan": "*", "squizlabs/php_codesniffer": "^3.1", "vimeo/psalm": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"LanguageServerProtocol\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP classes for the Language Server Protocol", "keywords": ["language", "microsoft", "php", "server"], "support": {"issues": "https://github.com/felixfbecker/php-language-server-protocol/issues", "source": "https://github.com/felixfbecker/php-language-server-protocol/tree/v1.5.3"}, "time": "2024-04-30T00:40:11+00:00"}, {"name": "friendsofphp/php-cs-fixer", "version": "v2.19.3", "source": {"type": "git", "url": "https://github.com/FriendsOfPHP/PHP-CS-Fixer.git", "reference": "75ac86f33fab4714ea5a39a396784d83ae3b5ed8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfPHP/PHP-CS-Fixer/zipball/75ac86f33fab4714ea5a39a396784d83ae3b5ed8", "reference": "75ac86f33fab4714ea5a39a396784d83ae3b5ed8", "shasum": ""}, "require": {"composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.2 || ^2.0", "doctrine/annotations": "^1.2", "ext-json": "*", "ext-tokenizer": "*", "php": "^5.6 || ^7.0 || ^8.0", "php-cs-fixer/diff": "^1.3", "symfony/console": "^3.4.43 || ^4.1.6 || ^5.0", "symfony/event-dispatcher": "^3.0 || ^4.0 || ^5.0", "symfony/filesystem": "^3.0 || ^4.0 || ^5.0", "symfony/finder": "^3.0 || ^4.0 || ^5.0", "symfony/options-resolver": "^3.0 || ^4.0 || ^5.0", "symfony/polyfill-php70": "^1.0", "symfony/polyfill-php72": "^1.4", "symfony/process": "^3.0 || ^4.0 || ^5.0", "symfony/stopwatch": "^3.0 || ^4.0 || ^5.0"}, "require-dev": {"justinrainbow/json-schema": "^5.0", "keradus/cli-executor": "^1.4", "mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.4.2", "php-cs-fixer/accessible-object": "^1.0", "php-cs-fixer/phpunit-constraint-isidenticalstring": "^1.2", "php-cs-fixer/phpunit-constraint-xmlmatchesxsd": "^1.2.1", "phpspec/prophecy-phpunit": "^1.1 || ^2.0", "phpunit/phpunit": "^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.13 || ^9.5", "phpunitgoodpractices/polyfill": "^1.5", "phpunitgoodpractices/traits": "^1.9.1", "sanmai/phpunit-legacy-adapter": "^6.4 || ^8.2.1", "symfony/phpunit-bridge": "^5.2.1", "symfony/yaml": "^3.0 || ^4.0 || ^5.0"}, "suggest": {"ext-dom": "For handling output formats in XML", "ext-mbstring": "For handling non-UTF8 characters.", "php-cs-fixer/phpunit-constraint-isidenticalstring": "For IsIdenticalString constraint.", "php-cs-fixer/phpunit-constraint-xmlmatchesxsd": "For XmlMatchesXsd constraint.", "symfony/polyfill-mbstring": "When enabling `ext-mbstring` is not possible."}, "bin": ["php-cs-fixer"], "type": "application", "extra": {"branch-alias": {"dev-master": "2.19-dev"}}, "autoload": {"psr-4": {"PhpCsFixer\\": "src/"}, "classmap": ["tests/Test/AbstractFixerTestCase.php", "tests/Test/AbstractIntegrationCaseFactory.php", "tests/Test/AbstractIntegrationTestCase.php", "tests/Test/Assert/AssertTokensTrait.php", "tests/Test/IntegrationCase.php", "tests/Test/IntegrationCaseFactory.php", "tests/Test/IntegrationCaseFactoryInterface.php", "tests/Test/InternalIntegrationCaseFactory.php", "tests/Test/IsIdenticalConstraint.php", "tests/Test/TokensWithObservedTransformers.php", "tests/TestCase.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A tool to automatically fix PHP code style", "support": {"issues": "https://github.com/FriendsOfPHP/PHP-CS-Fixer/issues", "source": "https://github.com/FriendsOfPHP/PHP-CS-Fixer/tree/v2.19.3"}, "funding": [{"url": "https://github.com/keradus", "type": "github"}], "time": "2021-11-15T17:17:55+00:00"}, {"name": "myclabs/deep-copy", "version": "1.13.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "1720ddd719e16cf0db4eb1c6eca108031636d46c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/1720ddd719e16cf0db4eb1c6eca108031636d46c", "reference": "1720ddd719e16cf0db4eb1c6eca108031636d46c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.13.1"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2025-04-29T12:36:36+00:00"}, {"name": "netresearch/jsonmapper", "version": "v4.5.0", "source": {"type": "git", "url": "https://github.com/cweiske/jsonmapper.git", "reference": "8e76efb98ee8b6afc54687045e1b8dba55ac76e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cweiske/jsonmapper/zipball/8e76efb98ee8b6afc54687045e1b8dba55ac76e5", "reference": "8e76efb98ee8b6afc54687045e1b8dba55ac76e5", "shasum": ""}, "require": {"ext-json": "*", "ext-pcre": "*", "ext-reflection": "*", "ext-spl": "*", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "~7.5 || ~8.0 || ~9.0 || ~10.0", "squizlabs/php_codesniffer": "~3.5"}, "type": "library", "autoload": {"psr-0": {"JsonMapper": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["OSL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://github.com/cweiske/jsonmapper/", "role": "Developer"}], "description": "Map nested JSON structures onto PHP classes", "support": {"email": "<EMAIL>", "issues": "https://github.com/cweiske/jsonmapper/issues", "source": "https://github.com/cweiske/jsonmapper/tree/v4.5.0"}, "time": "2024-09-08T10:13:13+00:00"}, {"name": "nikic/php-parser", "version": "v4.19.4", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/715f4d25e225bc47b293a8b997fe6ce99bf987d2", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.19.4"}, "time": "2024-09-29T15:01:53+00:00"}, {"name": "openlss/lib-array2xml", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/nullivex/lib-array2xml.git", "reference": "a91f18a8dfc69ffabe5f9b068bc39bb202c81d90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nullivex/lib-array2xml/zipball/a91f18a8dfc69ffabe5f9b068bc39bb202c81d90", "reference": "a91f18a8dfc69ffabe5f9b068bc39bb202c81d90", "shasum": ""}, "require": {"php": ">=5.3.2"}, "type": "library", "autoload": {"psr-0": {"LSS": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.nullivex.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.nullivex.com"}], "description": "Array2XML conversion library credit to lalit.org", "homepage": "https://www.nullivex.com", "keywords": ["array", "array conversion", "xml", "xml conversion"], "support": {"issues": "https://github.com/nullivex/lib-array2xml/issues", "source": "https://github.com/nullivex/lib-array2xml/tree/master"}, "time": "2019-03-29T20:06:56+00:00"}, {"name": "phar-io/manifest", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "54750ef60c58e43759730615a392c31c80e23176"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/54750ef60c58e43759730615a392c31c80e23176", "reference": "54750ef60c58e43759730615a392c31c80e23176", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.4"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:33:53+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "php-cs-fixer/diff", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/PHP-CS-Fixer/diff.git", "reference": "dbd31aeb251639ac0b9e7e29405c1441907f5759"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-CS-Fixer/diff/zipball/dbd31aeb251639ac0b9e7e29405c1441907f5759", "reference": "dbd31aeb251639ac0b9e7e29405c1441907f5759", "shasum": ""}, "require": {"php": "^5.6 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^5.7.23 || ^6.4.3 || ^7.0", "symfony/process": "^3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "SpacePossum"}], "description": "sebastian/diff v2 backport support for PHP5.6", "homepage": "https://github.com/PHP-CS-Fixer", "keywords": ["diff"], "support": {"issues": "https://github.com/PHP-CS-Fixer/diff/issues", "source": "https://github.com/PHP-CS-Fixer/diff/tree/v1.3.1"}, "abandoned": true, "time": "2020-10-14T08:39:05+00:00"}, {"name": "phpunit/php-code-coverage", "version": "9.2.32", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "85402a822d1ecf1db1096959413d35e1c37cf1a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/85402a822d1ecf1db1096959413d35e1c37cf1a5", "reference": "85402a822d1ecf1db1096959413d35e1c37cf1a5", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.19.1 || ^5.1.0", "php": ">=7.3", "phpunit/php-file-iterator": "^3.0.6", "phpunit/php-text-template": "^2.0.4", "sebastian/code-unit-reverse-lookup": "^2.0.3", "sebastian/complexity": "^2.0.3", "sebastian/environment": "^5.1.5", "sebastian/lines-of-code": "^1.0.4", "sebastian/version": "^3.0.2", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^9.6"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "type": "library", "extra": {"branch-alias": {"dev-main": "9.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.32"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-08-22T04:23:01+00:00"}, {"name": "phpunit/php-file-iterator", "version": "3.0.6", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/3.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2021-12-02T12:48:52+00:00"}, {"name": "phpunit/php-invoker", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/3.1.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T05:58:55+00:00"}, {"name": "phpunit/php-text-template", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T05:33:50+00:00"}, {"name": "phpunit/php-timer", "version": "5.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebastian<PERSON>mann/php-timer/tree/5.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:16:10+00:00"}, {"name": "phpunit/phpunit", "version": "9.6.23", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "43d2cb18d0675c38bd44982a5d1d88f6d53d8d95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/43d2cb18d0675c38bd44982a5d1d88f6d53d8d95", "reference": "43d2cb18d0675c38bd44982a5d1d88f6d53d8d95", "shasum": ""}, "require": {"doctrine/instantiator": "^1.5.0 || ^2", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.13.1", "phar-io/manifest": "^2.0.4", "phar-io/version": "^3.2.1", "php": ">=7.3", "phpunit/php-code-coverage": "^9.2.32", "phpunit/php-file-iterator": "^3.0.6", "phpunit/php-invoker": "^3.1.1", "phpunit/php-text-template": "^2.0.4", "phpunit/php-timer": "^5.0.3", "sebastian/cli-parser": "^1.0.2", "sebastian/code-unit": "^1.0.8", "sebastian/comparator": "^4.0.8", "sebastian/diff": "^4.0.6", "sebastian/environment": "^5.1.5", "sebastian/exporter": "^4.0.6", "sebastian/global-state": "^5.0.7", "sebastian/object-enumerator": "^4.0.4", "sebastian/resource-operations": "^3.0.4", "sebastian/type": "^3.2.1", "sebastian/version": "^3.0.2"}, "suggest": {"ext-soap": "To be able to generate mocks based on WSDL files", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "9.6-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "security": "https://github.com/sebastian<PERSON>mann/phpunit/security/policy", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/9.6.23"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "time": "2025-05-02T06:40:34+00:00"}, {"name": "sebastian/cli-parser", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "2b56bea83a09de3ac06bb18b92f068e60cc6f50b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/2b56bea83a09de3ac06bb18b92f068e60cc6f50b", "reference": "2b56bea83a09de3ac06bb18b92f068e60cc6f50b", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/1.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:27:43+00:00"}, {"name": "sebastian/code-unit", "version": "1.0.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/1fc9f64c0927627ef78ba436c9b17d967e68e120", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/1.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:08:54+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/2.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T05:30:19+00:00"}, {"name": "sebastian/comparator", "version": "4.0.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "fa0f136dd2334583309d32b62544682ee972b51a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/comparator/zipball/fa0f136dd2334583309d32b62544682ee972b51a", "reference": "fa0f136dd2334583309d32b62544682ee972b51a", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/diff": "^4.0", "sebastian/exporter": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/4.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-09-14T12:41:17+00:00"}, {"name": "sebastian/complexity", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "25f207c40d62b8b7aa32f5ab026c53561964053a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/25f207c40d62b8b7aa32f5ab026c53561964053a", "reference": "25f207c40d62b8b7aa32f5ab026c53561964053a", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/2.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-22T06:19:30+00:00"}, {"name": "sebastian/diff", "version": "4.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "ba01945089c3a293b01ba9badc29ad55b106b0bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/ba01945089c3a293b01ba9badc29ad55b106b0bc", "reference": "ba01945089c3a293b01ba9badc29ad55b106b0bc", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3", "symfony/process": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/4.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:30:58+00:00"}, {"name": "sebastian/environment", "version": "5.1.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "830c43a844f1f8d5b7a1f6d6076b784454d8b7ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/830c43a844f1f8d5b7a1f6d6076b784454d8b7ed", "reference": "830c43a844f1f8d5b7a1f6d6076b784454d8b7ed", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/5.1.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:03:51+00:00"}, {"name": "sebastian/exporter", "version": "4.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "78c00df8f170e02473b682df15bfcdacc3d32d72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/78c00df8f170e02473b682df15bfcdacc3d32d72", "reference": "78c00df8f170e02473b682df15bfcdacc3d32d72", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/4.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:33:00+00:00"}, {"name": "sebastian/global-state", "version": "5.0.7", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9", "reference": "bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/5.0.7"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:35:11+00:00"}, {"name": "sebastian/lines-of-code", "version": "1.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "e1e4a170560925c26d424b6a03aed157e7dcc5c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/lines-of-code/zipball/e1e4a170560925c26d424b6a03aed157e7dcc5c5", "reference": "e1e4a170560925c26d424b6a03aed157e7dcc5c5", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/1.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-22T06:20:34+00:00"}, {"name": "sebastian/object-enumerator", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/5c9eeac41b290a3712d88851518825ad78f45c71", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/4.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:12:34+00:00"}, {"name": "sebastian/object-reflector", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:14:26+00:00"}, {"name": "sebastian/recursion-context", "version": "4.0.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1", "reference": "e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/4.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:07:39+00:00"}, {"name": "sebastian/resource-operations", "version": "3.0.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "05d5692a7993ecccd56a03e40cd7e5b09b1d404e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/resource-operations/zipball/05d5692a7993ecccd56a03e40cd7e5b09b1d404e", "reference": "05d5692a7993ecccd56a03e40cd7e5b09b1d404e", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "support": {"source": "https://github.com/sebastian<PERSON>mann/resource-operations/tree/3.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-14T16:00:52+00:00"}, {"name": "sebastian/type", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/type/zipball/75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7", "reference": "75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/3.2.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:13:03+00:00"}, {"name": "sebastian/version", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c6c1022351a901512170118436c764e473f6de8c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c6c1022351a901512170118436c764e473f6de8c", "reference": "c6c1022351a901512170118436c764e473f6de8c", "shasum": ""}, "require": {"php": ">=7.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/3.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T06:39:44+00:00"}, {"name": "symfony/browser-kit", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/browser-kit.git", "reference": "9590bd3d3f9fa2f28d34b713ed4765a8cc8ad15c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/browser-kit/zipball/9590bd3d3f9fa2f28d34b713ed4765a8cc8ad15c", "reference": "9590bd3d3f9fa2f28d34b713ed4765a8cc8ad15c", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/dom-crawler": "~2.8|~3.0|~4.0"}, "require-dev": {"symfony/css-selector": "~2.8|~3.0|~4.0", "symfony/process": "~2.8|~3.0|~4.0"}, "suggest": {"symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\BrowserKit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony BrowserKit Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/browser-kit/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/phpunit-bridge", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/phpunit-bridge.git", "reference": "2eabda563921f21cbce1d1e3247b3c36568905e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/phpunit-bridge/zipball/2eabda563921f21cbce1d1e3247b3c36568905e6", "reference": "2eabda563921f21cbce1d1e3247b3c36568905e6", "shasum": ""}, "require": {"php": ">=7.2.5"}, "conflict": {"phpunit/phpunit": "<7.5|9.1.2"}, "require-dev": {"symfony/deprecation-contracts": "^2.5|^3.0", "symfony/error-handler": "^5.4|^6.4|^7.0", "symfony/polyfill-php81": "^1.27"}, "bin": ["bin/simple-phpunit"], "type": "symfony-bridge", "extra": {"thanks": {"url": "https://github.com/sebastian<PERSON>mann/phpunit", "name": "phpunit/phpunit"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Bridge\\PhpUnit\\": ""}, "exclude-from-classmap": ["/Tests/", "/bin/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides utilities for PHPUnit, especially user deprecation notices management", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/phpunit-bridge/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-23T07:26:30+00:00"}, {"name": "symfony/process", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "b8648cf1d5af12a44a51d07ef9bf980921f15fca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/b8648cf1d5af12a44a51d07ef9bf980921f15fca", "reference": "b8648cf1d5af12a44a51d07ef9bf980921f15fca", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/stopwatch", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "298b81faad4ce60e94466226b2abbb8c9bca7462"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/298b81faad4ce60e94466226b2abbb8c9bca7462", "reference": "298b81faad4ce60e94466226b2abbb8c9bca7462", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Stopwatch Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/stopwatch/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/var-dumper", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "0719f6cf4633a38b2c1585140998579ce23b4b7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/0719f6cf4633a38b2c1585140998579ce23b4b7d", "reference": "0719f6cf4633a38b2c1585140998579ce23b4b7d", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0"}, "require-dev": {"ext-iconv": "*", "twig/twig": "~1.34|~2.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "ext-symfony_debug": ""}, "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony mechanism for exploring and dumping PHP variables", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/web-profiler-bundle", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/web-profiler-bundle.git", "reference": "ccb83b3a508f4a683e44f571f127beebdc315ff9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/web-profiler-bundle/zipball/ccb83b3a508f4a683e44f571f127beebdc315ff9", "reference": "ccb83b3a508f4a683e44f571f127beebdc315ff9", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/config": "~3.4|~4.0", "symfony/http-kernel": "~3.4.25|^4.2.6", "symfony/polyfill-php70": "~1.0", "symfony/routing": "~3.4.7|~4.0", "symfony/twig-bundle": "~3.4|~4.0", "symfony/var-dumper": "~3.3|~4.0", "twig/twig": "~1.34|~2.4"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/event-dispatcher": "<3.3.1", "symfony/framework-bundle": ">4.3.99", "symfony/var-dumper": "<3.3"}, "require-dev": {"symfony/browser-kit": "~3.4|~4.0", "symfony/console": "~3.4|~4.0", "symfony/css-selector": "~3.4|~4.0", "symfony/framework-bundle": "~3.4|~4.0", "symfony/stopwatch": "~3.4|~4.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\WebProfilerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony WebProfilerBundle", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/web-profiler-bundle/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.3"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:36:25+00:00"}, {"name": "vimeo/psalm", "version": "4.30.0", "source": {"type": "git", "url": "https://github.com/vimeo/psalm.git", "reference": "d0bc6e25d89f649e4f36a534f330f8bb4643dd69"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vimeo/psalm/zipball/d0bc6e25d89f649e4f36a534f330f8bb4643dd69", "reference": "d0bc6e25d89f649e4f36a534f330f8bb4643dd69", "shasum": ""}, "require": {"amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1 || ^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-tokenizer": "*", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.5", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.13", "openlss/lib-array2xml": "^1.0", "php": "^7.1|^8", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0 || ^6.0", "symfony/polyfill-php80": "^1.25", "webmozart/path-util": "^2.3"}, "provide": {"psalm/psalm": "self.version"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0||^6.0", "ext-curl": "*", "php-parallel-lint/php-parallel-lint": "^1.2", "phpdocumentor/reflection-docblock": "^5", "phpmyadmin/sql-parser": "5.1.0||dev-master", "phpspec/prophecy": ">=1.9.0", "phpstan/phpdoc-parser": "1.2.* || 1.6.4", "phpunit/phpunit": "^9.0", "psalm/plugin-phpunit": "^0.16", "slevomat/coding-standard": "^7.0", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3 || ^5.0 || ^6.0", "weirdan/prophecy-shim": "^1.0 || ^2.0"}, "suggest": {"ext-curl": "In order to send data to shepherd", "ext-igbinary": "^2.0.5 is required, used to serialize caching data"}, "bin": ["psalm", "psalm-language-server", "psalm-plugin", "psalm-refactor", "psalter"], "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev", "dev-2.x": "2.x-dev", "dev-3.x": "3.x-dev", "dev-master": "4.x-dev"}}, "autoload": {"files": ["src/functions.php", "src/spl_object_id.php"], "psr-4": {"Psalm\\": "src/Psalm/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A static analysis tool for finding errors in PHP applications", "keywords": ["code", "inspection", "php"], "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.30.0"}, "time": "2022-11-06T20:37:08+00:00"}, {"name": "webmozart/path-util", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/webmozart/path-util.git", "reference": "d939f7edc24c9a1bb9c0dee5cb05d8e859490725"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozart/path-util/zipball/d939f7edc24c9a1bb9c0dee5cb05d8e859490725", "reference": "d939f7edc24c9a1bb9c0dee5cb05d8e859490725", "shasum": ""}, "require": {"php": ">=5.3.3", "webmozart/assert": "~1.0"}, "require-dev": {"phpunit/phpunit": "^4.6", "sebastian/version": "^1.0.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"psr-4": {"Webmozart\\PathUtil\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "A robust cross-platform utility for normalizing, comparing and modifying file paths.", "support": {"issues": "https://github.com/webmozart/path-util/issues", "source": "https://github.com/webmozart/path-util/tree/2.3.0"}, "abandoned": "symfony/filesystem", "time": "2015-12-17T08:42:14+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": "^7.4", "ext-ctype": "*", "ext-iconv": "*", "ext-json": "*", "ext-openssl": "*"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}