<?php

declare(strict_types=1);

namespace Functional\Controller\Property\API;

use Tests\ValueObject\ApiRequestParametersBag;
use Tests\WebTestCase;

class ListPropertiesControllerTest extends WebTestCase
{
    /**
     * @dataProvider dataProviderIndex
     */
    public function testIndex(
        array $filters,
        string $responseBodyFileName,
        array $mockData
    ): void {
        $this->setDataProviderMockData($mockData);
        $this->logInAs('testuser');

        $this->apiRequest(
            ApiRequestParametersBag::from(
                '/api/properties/list/active',
                'GET',
                200,
                [],
                null,
                [],
                $responseBodyFileName
            )
        );
    }

    public function dataProviderIndex(): array
    {
        return [
            'Active Ads' => [
                [],
                'immobiliare_active_ads.json',
                ['Tests\DataProvider\Property\Property::listListingAds' => 'immobiliare_active_ads.json'],
            ],
            'Archived Ads' => [
                [],
                'immobiliare_archived_ads.json',
                ['Tests\DataProvider\Property\Property::listListingAds' => 'immobiliare_archived_ads.json'],
            ],
            'Draft Ads' => [
                [],
                'immobiliare_draft_ads.json',
                ['Tests\DataProvider\Property\Property::listListingAds' => 'immobiliare_draft_ads.json'],
            ],
        ];
    }
}
