<?php

declare(strict_types=1);

namespace Integration\Controller\Property\API;

use App\Controller\Property\API\ListPropertiesController;
use App\DataMapper\EntityToResponse\Property\AdListResponseDataMapper;
use App\DTO\Property\ListPropertiesFiltersRequest;
use App\DTO\Property\ListTypeRequest;
use App\Handler\Property\AdHandler;
use App\Model\Property\Ad;
use App\Model\Request\Shared\PaginationRequest;
use Symfony\Component\HttpFoundation\Response;
use Tests\KernelTestCase;

class ListPropertiesControllerTest extends KernelTestCase
{
    private ListPropertiesController $controller;
    private AdHandler $adHandler;
    private AdListResponseDataMapper $adListResponseDataMapper;

    protected function setUp(): void
    {
        parent::setUp();
        $kernel = static::bootKernel();

        // Mock dependencies
        $this->adHandler = $this->createMock(AdHandler::class);
        $this->adListResponseDataMapper = $this->createMock(AdListResponseDataMapper::class);

        // Instantiate the controller with mocked dependencies
        $this->controller = new ListPropertiesController(
            $this->adHandler,
            $this->adListResponseDataMapper
        );
    }

    public function testIndexReturnsSuccessfulResponseWithProperties(): void
    {
        $typeRequest = new ListTypeRequest();
        $typeRequest->setType('PROPERTY'); // Example type
        $paginationRequest = new PaginationRequest();
        $paginationRequest->setLimit(10);
        $paginationRequest->setOffset(0);
        $filtersRequest = new ListPropertiesFiltersRequest();

        $mockAd = $this->createMock(Ad::class);
        $mockMappedAd = ['id' => 1, 'title' => 'Test Property'];

        $this->adHandler
            ->expects($this->once())
            ->method('listActiveAds')
            ->with(
                $this->equalTo($typeRequest->getType()),
                $this->equalTo($paginationRequest),
                $this->equalTo($filtersRequest)
            )
            ->willReturn([
                'properties' => [$mockAd],
                'totalCount' => 1,
                'pagination' => [
                    'results' => 1,
                    'page' => 1,
                ],
            ]);

        $this->adListResponseDataMapper
            ->expects($this->once())
            ->method('map')
            ->with($this->equalTo($mockAd))
            ->willReturn($mockMappedAd);

        $response = $this->controller->index($typeRequest, $paginationRequest, $filtersRequest);

        $this->assertInstanceOf(Response::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $content = \json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $content);
        $this->assertArrayHasKey('list', $content['data']);
        $this->assertArrayHasKey('pagination', $content['data']);
        $this->assertCount(1, $content['data']['list']);
        $this->assertEquals($mockMappedAd, $content['data']['list'][0]);
        $this->assertEquals(1, $content['data']['pagination']['total']);
        $this->assertEquals(1, $content['data']['pagination']['results']);
        $this->assertEquals(1, $content['data']['pagination']['page']);
    }

    public function testIndexReturnsSuccessfulResponseWithoutProperties(): void
    {
        $typeRequest = new ListTypeRequest();
        $typeRequest->setType('PROPERTY');
        $paginationRequest = new PaginationRequest();
        $paginationRequest->setLimit(10);
        $paginationRequest->setOffset(0);
        $filtersRequest = new ListPropertiesFiltersRequest();

        $this->adHandler
            ->expects($this->once())
            ->method('listActiveAds')
            ->with(
                $this->equalTo($typeRequest->getType()),
                $this->equalTo($paginationRequest),
                $this->equalTo($filtersRequest)
            )
            ->willReturn([
                'properties' => [],
                'totalCount' => 0,
                'pagination' => [
                    'results' => 0,
                    'page' => 1,
                ],
            ]);

        $this->adListResponseDataMapper
            ->expects($this->never()) // Mapper should not be called if no properties
            ->method('map');

        $response = $this->controller->index($typeRequest, $paginationRequest, $filtersRequest);

        $this->assertInstanceOf(Response::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $content = \json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $content);
        $this->assertArrayHasKey('list', $content['data']);
        $this->assertArrayHasKey('pagination', $content['data']);
        $this->assertEmpty($content['data']['list']);
        $this->assertEquals(0, $content['data']['pagination']['total']);
        $this->assertEquals(0, $content['data']['pagination']['results']);
        $this->assertEquals(1, $content['data']['pagination']['page']);
    }
}
