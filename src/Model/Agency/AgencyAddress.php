<?php

declare(strict_types=1);

namespace App\Model\Agency;

use App\Model\Geography\Address;
use App\Model\Geography\City;

class AgencyAddress
{
    /**
     * @var Address|null
     */
    private $address;

    /**
     * @var City|null
     */
    private $city;

    /**
     * @return Address|null
     */
    public function getAddress()
    {
        return $this->address;
    }

    public function setAddress(
        Address $address = null
    ) {
        $this->address = $address;
    }

    /**
     * @return City|null
     */
    public function getCity()
    {
        return $this->city;
    }

    public function setCity(
        City $city = null
    ) {
        $this->city = $city;
    }
}
