<?php

declare(strict_types=1);

namespace App\Model\Agency;

use App\Model\Shared\Pagination;

class MultiAgencyListMembers
{
    /** @var int|null */
    public $resultsCount;

    /** @var Agency[]|null */
    public $agencies;

    /** @var Pagination|null */
    public $pagination;

    /** @return int|null */
    public function getResultsCount()
    {
        return $this->resultsCount;
    }

    public function setResultsCount(
        int $resultsCount = null
    ) {
        $this->resultsCount = $resultsCount;
    }

    /** @return Agency[]|null */
    public function getAgencies()
    {
        return $this->agencies;
    }

    public function setAgencies(
        array $agencies = null
    ) {
        $this->agencies = $agencies;
    }

    /** @return Pagination|null */
    public function getPagination()
    {
        return $this->pagination;
    }

    public function setPagination(
        Pagination $pagination = null
    ) {
        $this->pagination = $pagination;
    }
}
