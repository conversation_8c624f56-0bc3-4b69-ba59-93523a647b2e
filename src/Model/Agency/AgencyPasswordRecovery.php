<?php

namespace App\Model\Agency;

use App\Entity\Agency;

class AgencyPasswordRecovery
{
    /** @var Agency|null */
    private $agency;

    /** @var Token|null */
    private $token;

    public function getAgency()
    {
        return $this->agency;
    }

    public function setAgency(Agency $agency = null)
    {
        $this->agency = $agency;
    }

    public function getToken()
    {
        return $this->token;
    }

    public function setToken(Token $token = null)
    {
        $this->token = $token;
    }
}
