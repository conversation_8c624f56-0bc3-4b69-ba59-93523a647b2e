<?php

namespace App\Model\Agency;

class SsoProduct
{
    /**
     * @var int|null
     */
    private $id;

    /**
     * @var SsoProductRole
     */
    private $role;

    /**
     * @var string|null
     */
    private $uuid;

    /**
     * @var string|null
     */
    private $name;

    /**
     * @var SsoProductInfo[]|null
     */
    private $info;

    /**
     * @var string[]|null
     */
    private $subnets;

    /**
     * @var string[]|null
     */
    private $clientSubnets;

    /**
     * @var string[]|null
     */
    private $domains;

    /**
     * @return int|null
     */
    public function getId()
    {
        return $this->id;
    }

    public function setId(int $id = null)
    {
        $this->id = $id;
    }

    public function getRole(): SsoProductRole
    {
        return $this->role;
    }

    public function setRole(SsoProductRole $role): void
    {
        $this->role = $role;
    }

    /**
     * @return string|null
     */
    public function getUuid()
    {
        return $this->uuid;
    }

    public function setUuid(string $uuid = null)
    {
        $this->uuid = $uuid;
    }

    /**
     * @return string|null
     */
    public function getName()
    {
        return $this->name;
    }

    public function setName(string $name = null)
    {
        $this->name = $name;
    }

    /**
     * @return SsoProductInfo[]|null
     */
    public function getInfo()
    {
        return $this->info;
    }

    public function setInfo(array $info = null)
    {
        $this->info = $info;
    }

    /**
     * @return string[]|null
     */
    public function getSubnets()
    {
        return $this->subnets;
    }

    public function setSubnets(array $subnets = null)
    {
        $this->subnets = $subnets;
    }

    /**
     * @return string[]|null
     */
    public function getClientSubnets()
    {
        return $this->clientSubnets;
    }

    public function setClientSubnets(array $clientSubnets = null)
    {
        $this->clientSubnets = $clientSubnets;
    }

    /**
     * @return string[]|null
     */
    public function getDomains()
    {
        return $this->domains;
    }

    public function setDomains(array $domains = null)
    {
        $this->domains = $domains;
    }
}
