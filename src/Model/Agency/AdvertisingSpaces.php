<?php

namespace App\Model\Agency;

class AdvertisingSpaces
{
    private ?string $name = null;
    private ?string $service = null;
    private ?int $used = null;
    private ?int $total = null;
    private ?\DateTime $expiration = null;
    private ?bool $active = null;

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name = null)
    {
        $this->name = $name;
    }

    public function getService(): ?string
    {
        return $this->service;
    }

    public function setService(string $service = null)
    {
        $this->service = $service;
    }

    public function getUsed(): ?int
    {
        return $this->used;
    }

    public function setUsed(int $used = null)
    {
        $this->used = $used;
    }

    public function getTotal(): ?int
    {
        return $this->total;
    }

    public function setTotal(int $total = null)
    {
        $this->total = $total;
    }

    public function getExpiration(): ?\DateTime
    {
        return $this->expiration;
    }

    public function setExpiration(\DateTime $expiration = null)
    {
        $this->expiration = $expiration;
    }

    public function getActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active = null)
    {
        $this->active = $active;
    }
}
