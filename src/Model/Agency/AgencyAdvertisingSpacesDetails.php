<?php

namespace App\Model\Agency;

class AgencyAdvertisingSpacesDetails
{
    /** @var AdvertisingSpaces|null */
    private $sellAds;

    /** @var AdvertisingSpaces|null */
    private $rentAds;

    /** @var AdvertisingSpaces|null */
    private $projects;

    /** @var int|null */
    private $rankingAvg;

    public function getSellAds()
    {
        return $this->sellAds;
    }

    public function setSellAds(AdvertisingSpaces $sellAds = null)
    {
        $this->sellAds = $sellAds;
    }

    public function getRentAds()
    {
        return $this->rentAds;
    }

    public function setRentAds(AdvertisingSpaces $rentAds = null)
    {
        $this->rentAds = $rentAds;
    }

    public function getProjects()
    {
        return $this->projects;
    }

    public function setProjects(AdvertisingSpaces $projects = null)
    {
        $this->projects = $projects;
    }

    public function getRankingAvg()
    {
        return $this->rankingAvg;
    }

    public function setRankingAvg(int $rankingAvg = null)
    {
        $this->rankingAvg = $rankingAvg;
    }
}
