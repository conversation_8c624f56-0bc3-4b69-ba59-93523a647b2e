<?php

declare(strict_types=1);

namespace App\Model\Agency;

class AgencyLanguage
{
    /**
     * @var string
     */
    private $id;

    /**
     * @var string|null
     */
    private $name;

    /**
     * @var string|null
     */
    private $code;

    public function getId(): string
    {
        return $this->id;
    }

    public function setId(string $id)
    {
        $this->id = $id;
    }

    /**
     * @return string|null
     */
    public function getName()
    {
        return $this->name;
    }

    public function setName(string $name = null)
    {
        $this->name = $name;
    }

    /**
     * @return string|null
     */
    public function getCode()
    {
        return $this->code;
    }

    public function setCode(string $code = null)
    {
        $this->code = $code;
    }
}
