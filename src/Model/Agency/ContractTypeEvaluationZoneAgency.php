<?php

declare(strict_types=1);

namespace App\Model\Agency;

class ContractTypeEvaluationZoneAgency
{
    /** @var int int */
    private $id;

    /** @var string|null */
    private $name;

    /** @var bool|null */
    private $active;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id)
    {
        $this->id = $id;
    }

    public function getName()
    {
        return $this->name;
    }

    public function setName(string $name = null)
    {
        $this->name = $name;
    }

    public function getActive()
    {
        return $this->active;
    }

    public function setActive(bool $active = null)
    {
        $this->active = $active;
    }
}
