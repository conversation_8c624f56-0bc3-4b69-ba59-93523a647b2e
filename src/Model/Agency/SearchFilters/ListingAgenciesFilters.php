<?php

namespace App\Model\Agency\SearchFilters;

class ListingAgenciesFilters
{
    /** @var string|null */
    private $group;

    /** @var string|null */
    private $externalId;

    /** @var string[]|null */
    private $agencyIds;

    /**
     * @return string|null
     */
    public function getGroup()
    {
        return $this->group;
    }

    public function setGroup(string $group = null)
    {
        $this->group = $group;
    }

    /**
     * @return string|null
     */
    public function getExternalId()
    {
        return $this->externalId;
    }

    public function setExternalId(string $externalId = null)
    {
        $this->externalId = $externalId;
    }

    /** @return string[]|null */
    public function getAgencyIds(): ?array
    {
        return $this->agencyIds;
    }

    /** @param string[]|null $agencyIds */
    public function setAgencyIds(?array $agencyIds): void
    {
        $this->agencyIds = $agencyIds;
    }
}
