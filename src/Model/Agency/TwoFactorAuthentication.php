<?php

namespace App\Model\Agency;

class TwoFactorAuthentication
{
    public const TWO_FACTOR = '1';
    public const THREE_FACTOR = '2';

    /** @var bool|null */
    private $deviceValidationNeeded;

    /** @var string|null */
    private $version;

    /** @var TwoFactorAuthenticationType|null */
    private $authType;

    public function getDeviceValidationNeeded()
    {
        return $this->deviceValidationNeeded;
    }

    public function setDeviceValidationNeeded(bool $deviceValidationNeeded = null)
    {
        $this->deviceValidationNeeded = $deviceValidationNeeded;
    }

    public function getVersion()
    {
        return $this->version;
    }

    public function setVersion(string $version = null)
    {
        $this->version = $version;
    }

    public function getAuthType()
    {
        return $this->authType;
    }

    public function setAuthType(TwoFactorAuthenticationType $authType = null)
    {
        $this->authType = $authType;
    }
}
