<?php

declare(strict_types=1);

namespace App\Model\Agency;

use App\Model\Geography\City;
use App\Model\Geography\MacroZone;

class AgencyEvaluationZone
{
    /** @var int */
    private $id;

    /** @var City */
    private $city;

    /** @var MacroZone */
    private $macroZone;

    /** @var AgencyEvaluationZoneType */
    private $type;

    /** @var bool */
    private $locked;

    /** @var string|null */
    private $dateFrom;

    /** @var string|null */
    private $dateTo;

    /** @var ContractTypeEvaluationZoneAgency|null */
    private $contractType;

    /** @var BundleAgencyZone|null */
    private $bundle;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id)
    {
        $this->id = $id;
    }

    public function getCity()
    {
        return $this->city;
    }

    public function setCity(City $city)
    {
        $this->city = $city;
    }

    public function getMacroZone()
    {
        return $this->macroZone;
    }

    public function setMacroZone(MacroZone $macroZone)
    {
        $this->macroZone = $macroZone;
    }

    public function getType()
    {
        return $this->type;
    }

    public function setType(AgencyEvaluationZoneType $type)
    {
        $this->type = $type;
    }

    public function isLocked()
    {
        return $this->locked;
    }

    public function setLocked(bool $locked)
    {
        $this->locked = $locked;
    }

    public function getDateFrom()
    {
        return $this->dateFrom;
    }

    public function setDateFrom(string $dateFrom)
    {
        $this->dateFrom = $dateFrom;
    }

    public function getDateTo()
    {
        return $this->dateTo;
    }

    public function setDateTo(string $dateTo)
    {
        $this->dateTo = $dateTo;
    }

    public function getContractType()
    {
        return $this->contractType;
    }

    public function setContractType(ContractTypeEvaluationZoneAgency $contractType)
    {
        $this->contractType = $contractType;
    }

    public function getBundle()
    {
        return $this->bundle;
    }

    public function setBundle(BundleAgencyZone $bundle)
    {
        $this->bundle = $bundle;
    }
}
