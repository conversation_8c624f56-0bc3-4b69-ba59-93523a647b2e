<?php

namespace App\Model\Agency;

class SsoProductInfo
{
    /**
     * @var int|null
     */
    private $id;

    /**
     * @var string|null
     */
    private $key;

    /**
     * @var bool|null
     */
    private $skip2fa;

    /**
     * @return int|null
     */
    public function getId()
    {
        return $this->id;
    }

    public function setId(int $id = null)
    {
        $this->id = $id;
    }

    /**
     * @return string|null
     */
    public function getKey()
    {
        return $this->key;
    }

    public function setKey(string $key = null)
    {
        $this->key = $key;
    }

    /**
     * @return bool|null
     */
    public function getSkip2fa()
    {
        return $this->skip2fa;
    }

    public function setSkip2fa(bool $skip2fa = null)
    {
        $this->skip2fa = $skip2fa;
    }
}
