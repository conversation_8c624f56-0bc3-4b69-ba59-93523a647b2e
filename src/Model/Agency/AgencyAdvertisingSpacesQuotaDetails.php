<?php

namespace App\Model\Agency;

use App\Model\Property\Contract;

class AgencyAdvertisingSpacesQuotaDetails
{
    /** @var Contract */
    private $type;

    /** @var int|null */
    private $applicable;

    /** @var int|null */
    private $applied;

    /** @var int|null */
    private $available;

    public function getType()
    {
        return $this->type;
    }

    public function setType(Contract $type)
    {
        $this->type = $type;
    }

    public function getApplicable()
    {
        return $this->applicable;
    }

    public function setApplicable(int $applicable = null)
    {
        $this->applicable = $applicable;
    }

    public function getApplied()
    {
        return $this->applied;
    }

    public function setApplied(int $applied = null)
    {
        $this->applied = $applied;
    }

    public function getAvailable()
    {
        return $this->available;
    }

    public function setAvailable(int $available = null)
    {
        $this->available = $available;
    }
}
