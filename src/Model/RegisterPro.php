<?php

namespace App\Model;

use Symfony\Component\Validator\Constraints as Assert;

/**
 * Class RegisterPro
 */
class RegisterPro
{
    /**
     * @Assert\Choice(choices = {"0", "1"}, message = "Inserire una tipologia valida")
     */
    private $tipoimpresa;
    /**
     * @Assert\NotBlank(message = "Inserire il nome")
     * @Assert\Length(max = "40", maxMessage = "Nome non valido")
     */
    private $nome;
    /**
     * @Assert\NotBlank(message = "Inserire il cognome")
     * @Assert\Length(max = "40", maxMessage = "Cognome non valido")
     */
    private $cognome;
    /**
     * @Assert\NotBlank(message = "Inserire il nome della società")
     * @Assert\Length(max = "100", maxMessage = "Nome società non valido")
     */
    private $nomesocieta;
    /**
     * @Assert\NotBlank(message = "Inserire un indirizzo email")
     * @Assert\Email(message = "Inserire un indirizzo email valido")
     * @Assert\Length(max = "100", maxMessage = "Email non valida")
     */
    private $email;

    /**
     * @Assert\NotBlank(message = "Inserire il prefisso")
     * @Assert\Length(min = "2", minMessage = "Telefono non valido")
     */
    private $prefisso;

    /**
     * @Assert\NotBlank(message = "Inserire il telefono")
     * @Assert\Length(min = "5", minMessage = "Telefono non valido")
     */
    private $numero;
    /**
     * @Assert\NotBlank(message = "Inserire il comune")
     * @Assert\Length(max = "100", maxMessage = "Comune non valido")
     */
    private $comune;

    private $idComune;

    /**
     * @Assert\IsTrue(message = "Accettare le regole sulla privacy")
     */
    private $privacy;

    /**
     * @return mixed
     */
    public function getTipoimpresa()
    {
        return $this->tipoimpresa;
    }

    /**
     * @param mixed $tipoimpresa
     */
    public function setTipoimpresa($tipoimpresa)
    {
        $this->tipoimpresa = $tipoimpresa;
    }

    /**
     * @return mixed
     */
    public function getNome()
    {
        return $this->nome;
    }

    /**
     * @param mixed $nome
     */
    public function setNome($nome)
    {
        $this->nome = $nome;
    }

    /**
     * @return mixed
     */
    public function getCognome()
    {
        return $this->cognome;
    }

    /**
     * @param mixed $cognome
     */
    public function setCognome($cognome)
    {
        $this->cognome = $cognome;
    }

    /**
     * @return mixed
     */
    public function getNomesocieta()
    {
        return $this->nomesocieta;
    }

    /**
     * @param mixed $nomesocieta
     */
    public function setNomesocieta($nomesocieta)
    {
        $this->nomesocieta = $nomesocieta;
    }

    /**
     * @return mixed
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * @param mixed $email
     */
    public function setEmail($email)
    {
        $this->email = $email;
    }

    /**
     * @return mixed
     */
    public function getPrefisso()
    {
        return $this->prefisso;
    }

    /**
     * @param mixed $prefisso
     */
    public function setPrefisso($prefisso)
    {
        $this->prefisso = $prefisso;
    }

    /**
     * @return mixed
     */
    public function getNumero()
    {
        return $this->numero;
    }

    /**
     * @param mixed $numero
     */
    public function setNumero($numero)
    {
        $this->numero = $numero;
    }

    /**
     * @return mixed
     */
    public function getComune()
    {
        return $this->comune;
    }

    /**
     * @param mixed $comune
     */
    public function setComune($comune)
    {
        $this->comune = $comune;
    }

    /**
     * @return mixed
     */
    public function getIdComune()
    {
        return $this->idComune;
    }

    /**
     * @param mixed $idComune
     */
    public function setIdComune($idComune)
    {
        $this->idComune = $idComune;
    }

    /**
     * @return bool
     */
    public function getPrivacy()
    {
        return $this->privacy;
    }

    /**
     * @param bool $privacy
     */
    public function setPrivacy($privacy)
    {
        $this->privacy = $privacy;
    }
}
