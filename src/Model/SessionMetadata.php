<?php

namespace App\Model;

class SessionMetadata
{
    public $agent_id;
    public $product_tags;

    public static function map($metadata)
    {
        return new self(
            $metadata->agent_id,
            $metadata->product_tags
        );
    }

    public function __construct($agentId, array $productTags)
    {
        $this->agent_id     = $agentId;
        $this->product_tags = $productTags;
    }

    public function addProductTag($productTag)
    {
        if (!\in_array($productTag, $this->product_tags)) {
            $this->product_tags[] = $productTag;
        }
    }
}
