<?php

namespace App\Model;

abstract class AbstractJsonSerializableModel implements \JsonSerializable
{
    public function jsonSerialize()
    {
        return self::mapToArray($this);
    }

    public static function mapToArray($data)
    {
        if (\is_array($data)) {
            return array_map(static function ($item) {
                return self::mapToArray($item);
            }, $data);
        }

        if (!\is_object($data)) {
            return $data;
        }

        $dataArray       = [];
        $reflectionClass = new \ReflectionClass($data);
        foreach ($reflectionClass->getProperties() as $reflectionProperty) {
            $reflectionProperty->setAccessible(true);

            $propertyName  = $reflectionProperty->getName();
            $propertyValue = $reflectionProperty->getValue($data);

            $dataArray[$propertyName] = self::mapToArray($propertyValue);
        }

        return $dataArray;
    }
}
