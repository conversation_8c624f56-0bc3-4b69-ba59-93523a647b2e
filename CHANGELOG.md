# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [6.74.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.73.0...v6.74.0)    (2025-08-26)


### Features

* **ad-detail:** add get languages map lookup on init ([cbe207e](https://gitlab.pepita.io/getrix/mls-site/commit/cbe207e54ee8722515fad31785b6ebc520d47558))
* **ad-detail:** new Dates section ([e11cf38](https://gitlab.pepita.io/getrix/mls-site/commit/e11cf383f0041cdd5f47813baf58f294b8b92cf1))
* **ad-detail:** new geolocalization field to show map preferences ([03e083e](https://gitlab.pepita.io/getrix/mls-site/commit/03e083e7d7a6680774e2525e20cbe9a2389242b8))
* add ActivationError component for handling activation errors and validation feedback ([b8e8482](https://gitlab.pepita.io/getrix/mls-site/commit/b8e84825cadcec21d8931f44f87bc30a9a9b430a))
* **unified-properties:** popover of disabled getrix actions [non getrix users | actions dropdown] ([bc99ee2](https://gitlab.pepita.io/getrix/mls-site/commit/bc99ee2a2fcf5cf142ec17efd8516d79ef7f8697))


### Bug Fixes

* **CrmSummary:** update fallback to display zero results correctly ([63f1ccb](https://gitlab.pepita.io/getrix/mls-site/commit/63f1ccb79314fc4251a5c6f6a1763f2d20d185db))
* **ad-detail:** taking proper descriptions array for new construction ([5348a27](https://gitlab.pepita.io/getrix/mls-site/commit/5348a27727b05b1ee5e45b99d3d5c9c2557b0b65))
* add logged condition to prevend unwanted call on login page ([59edf45](https://gitlab.pepita.io/getrix/mls-site/commit/59edf45ce150981ede278aabdd7752a49e92e4f3))
* **calendar:** range border radius ([dbaeb23](https://gitlab.pepita.io/getrix/mls-site/commit/dbaeb23c769bcf86c5df7966552aed44539575e9))
* correct `PAGE_COUNT` calculation logic in `SuccessListResponse` ([01ab690](https://gitlab.pepita.io/getrix/mls-site/commit/01ab690e83b59522ca27f9af31f3f2772114a097))
* **crm-autocomplete:** options aligned to left ([f984408](https://gitlab.pepita.io/getrix/mls-site/commit/f984408f4f5a9f7c29e0ca0f66ca25b593c4eeab))
* fix(property-list): keep emptyState centered on horizontal scroll
See merge request getrix/mls-site!3812 ([b4779bf](https://gitlab.pepita.io/getrix/mls-site/commit/b4779bfd8e82aabc2dd11625763a61623f9c8d15))
* handle data changes request failure in query-factory.ts ([230f903](https://gitlab.pepita.io/getrix/mls-site/commit/230f903517d911eeb279dcd8667fea706ee04820))
* **lista:** calendar border-radius ([983145a](https://gitlab.pepita.io/getrix/mls-site/commit/983145aa3643c1c5ac5bb99a664c618b397bfec4))
* **list:** ellipsis button alway visible ([6d8d1e7](https://gitlab.pepita.io/getrix/mls-site/commit/6d8d1e752b3da3415c0ee3685fec0ffb7027b36c))
* **list:** unlock icon getrix dropwdown ([048b27c](https://gitlab.pepita.io/getrix/mls-site/commit/048b27cc12e8042420f550b212c8f84510d49792))
* **quick-filters:** slide to end when recalculating childrens ([57b730b](https://gitlab.pepita.io/getrix/mls-site/commit/57b730b2e9dc8b742ae1edc369fafde0076a630c))
* sentry manager ([3c61eff](https://gitlab.pepita.io/getrix/mls-site/commit/3c61eff93c0ff0a9eac64f45bcf282b97dc3ae83))


### Refactor

* app-shell.js ([46d8a60](https://gitlab.pepita.io/getrix/mls-site/commit/46d8a600062f9986e87f588048605e13ee0e60c2))


### Other

* changed getrix common images paths to mls public ones ([a5187f8](https://gitlab.pepita.io/getrix/mls-site/commit/a5187f865550a72429b55558ca9d5a18adf9b534))
* changed images path to mls ones ([06c846d](https://gitlab.pepita.io/getrix/mls-site/commit/06c846d49053f4251a1a402ec0f0a7fc6362657a))
* reemoved unused llb ([cd592e3](https://gitlab.pepita.io/getrix/mls-site/commit/cd592e33c8d69e4e3ebb54c7c432c8c9cc033cae))
* **table:** remove demo folder ([f7ddc1d](https://gitlab.pepita.io/getrix/mls-site/commit/f7ddc1d7806b4552fc2d6d307e2d7c0e3d695176))
* unused libs ([9020cd0](https://gitlab.pepita.io/getrix/mls-site/commit/9020cd0c852163a8cf93d6f824b0c1cad91d3118))
* **validation-errors:** adds unhandled cases ([15f793c](https://gitlab.pepita.io/getrix/mls-site/commit/15f793c28776ee60135884f97c4c62ad8ee2c628))

## [6.73.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.72.0...v6.73.0)    (2025-08-05)


### Features

* **force-password:** migration to react ([6c47921](https://gitlab.pepita.io/getrix/mls-site/commit/6c47921dc5b128946db0f5d475a8026a92882d4a))


### Bug Fixes

* back button should always close the window ([a0b5dcc](https://gitlab.pepita.io/getrix/mls-site/commit/a0b5dcc5184c16f09dcbb0da6882ec0bd4c39e25))
* **list:** padding table and check overflow ([f71d263](https://gitlab.pepita.io/getrix/mls-site/commit/f71d2633ea742ff77216e2b98aad1d0f47ef6e49))


### Other

* **module-landing:** remove unused assets ([7c83180](https://gitlab.pepita.io/getrix/mls-site/commit/7c83180934f51305ded05635ff1f634bddecb05a))
* **register:** removed old unused js bundle ([30b1e64](https://gitlab.pepita.io/getrix/mls-site/commit/30b1e6448de4cdb376de823a4004a213ebb2da97))
* removed unused utils ([e379fd7](https://gitlab.pepita.io/getrix/mls-site/commit/e379fd76f876d244270c0b92ddc293556fbac721))
* **settings:** removed old settings bundle ([43ea965](https://gitlab.pepita.io/getrix/mls-site/commit/43ea9658f14a84a6cf4ab80e0d38b906d4c43a7d))
## [6.72.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.71.1...v6.72.0)    (2025-08-04)


### Features

* Add mandate expiry date filters to PropertyList components ([e2ab6bf](https://gitlab.pepita.io/getrix/mls-site/commit/e2ab6bfa847d57e53a66256c49d87df66126853b))
* **PropertyListFilters:** add mandate date filters and disable logic based on typology ([333aac4](https://gitlab.pepita.io/getrix/mls-site/commit/333aac45f7afb83b4cddd36b9ef24651287fb3c3))
* **PropertyListFilters:** update date range placeholders for mandate filters ([c5e6b3b](https://gitlab.pepita.io/getrix/mls-site/commit/c5e6b3b23a4366fd5f779098c4a87afe8d917671))
* **ad-detail:** maxHeight modal ([b3d4886](https://gitlab.pepita.io/getrix/mls-site/commit/b3d4886fff0162ac7e2e67c7a640a215df5c5d3d))
* add `firstPublicationDate` to Ad model and data mappers ([049db71](https://gitlab.pepita.io/getrix/mls-site/commit/049db71774154b68d36f40e19724bdeb2f905da5))
* **dropdown-range-input:** Improvement and replace of dropdownrangeinput ([69ce6a6](https://gitlab.pepita.io/getrix/mls-site/commit/69ce6a6111cb6f59c23181624757b3144a3492bb))
* improve handling of `performanceRelativeIndexIds` in `ListPropertiesFiltersRequest` ([a7cf5af](https://gitlab.pepita.io/getrix/mls-site/commit/a7cf5afaf4af14e291d241abe7dc248ed8facefe))
* **storybook:** Storybook introduction ([9f00b49](https://gitlab.pepita.io/getrix/mls-site/commit/9f00b49aef9800b60102531f3b0fc4aa5ddc32c1))


### Bug Fixes

* **SidebarViewWrapper:** correct dropdown reference in SectionContent ([15de6aa](https://gitlab.pepita.io/getrix/mls-site/commit/15de6aad3f0901446efa6b9b15ad99e40e0c00cb))
* **billboard:** no more positive photoNumber even if there are no images to show ([2593639](https://gitlab.pepita.io/getrix/mls-site/commit/2593639a2dfbe8aa692376104c9e8fe7c6306d2f))
* changed listglobally endpoints ([6ff791f](https://gitlab.pepita.io/getrix/mls-site/commit/6ff791f34cdafd63c4f022c5d4170f2452fbcfe8))
* **messaging:** proper error message when oldThreadId or oldAgentId are unavailable ([1fe3e14](https://gitlab.pepita.io/getrix/mls-site/commit/1fe3e149b98492ca574d86060c1321c200b2dd8b))
* **properties-list:** label activated success ([20fa376](https://gitlab.pepita.io/getrix/mls-site/commit/20fa376ecfd846eea138025af5091a385d109856))
* **quick-filters:** Fix container size swiper calculation ([966ae4a](https://gitlab.pepita.io/getrix/mls-site/commit/966ae4a23a48e11c36769313c18bed1ae00d1db5))
* revert 333aac45f7afb83b4cddd36b9ef24651287fb3c3 ([df1d166](https://gitlab.pepita.io/getrix/mls-site/commit/df1d16660cead0e5fb28f03cdf6296574fd89aad))
* show property title on property item ([bb30f3c](https://gitlab.pepita.io/getrix/mls-site/commit/bb30f3ce541aef131d4adda9e9019d5e95df24e2))


### Test

* **unified-properties:** SendAdEmailDialog ([322ad20](https://gitlab.pepita.io/getrix/mls-site/commit/322ad206ccf70cb8bcc9c54952a8e64692b02ff9))


### Other

* add `mandateFrom` filter to `ListPropertiesFiltersRequest` ([ca90f99](https://gitlab.pepita.io/getrix/mls-site/commit/ca90f9977ae87bb57ecb3f88e93ae38c2ebba9b0))
## [6.71.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.71.0...v6.71.1)    (2025-07-31)


### Bug Fixes

* keep confirm button loading until redirect occurs ([dc09791](https://gitlab.pepita.io/getrix/mls-site/commit/dc09791e2f815f8b435f6473c6926b988b67d04c))
* **performance:** border color property item ([d25d858](https://gitlab.pepita.io/getrix/mls-site/commit/d25d8581b4c6a7936d8145cf1773bbeb9e9ba1c0))
## [6.71.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.70.3...v6.71.0)    (2025-07-31)


### Features

* **growthbook:** added countryTag to attributes ([9b235d1](https://gitlab.pepita.io/getrix/mls-site/commit/9b235d110e81f559f7d28ebf54050e51d80f548b))
* **unified-list:** mixpanel properties_portal_area_access + properties_list_view_changed ([c73903d](https://gitlab.pepita.io/getrix/mls-site/commit/c73903da8ddbf05408265efcc838225d2b480886))
* **unified-properties:** Customer email Autocomplete to accept custom email and select on key enter ([080f958](https://gitlab.pepita.io/getrix/mls-site/commit/080f958c0d218b77e2536beabc3093faab58b00b))
* **unified-properties:** mixpanel properties_filter_selection ([8c16011](https://gitlab.pepita.io/getrix/mls-site/commit/8c160116b34d380d381167c9761bf058c79d5d18))
* **unified-properties:** mixpanel properties_mark_as_favorite_listing ([5e9adf2](https://gitlab.pepita.io/getrix/mls-site/commit/5e9adf25a34b32e51243dc6c3c1bbb7d880d3171))


### Bug Fixes

* **ShareButton:** stop propagation on actions click ([f5b1fe5](https://gitlab.pepita.io/getrix/mls-site/commit/f5b1fe59bd21db175123ba7cef3f9e8e9bf05aa7))
* **columns:** prevent event propagation on checkbox click in property list ([6e56c2d](https://gitlab.pepita.io/getrix/mls-site/commit/6e56c2d2fb810dfc136bf39ce53ba39b68521034))
* **listing:** row actions ([196a559](https://gitlab.pepita.io/getrix/mls-site/commit/196a5597b92f11ee6e1f03df2e9913bf807b8041))
* **messaging:** changed mechanism to show/hide contactName and contactThreadsUrl ([78f7c3a](https://gitlab.pepita.io/getrix/mls-site/commit/78f7c3a0e9a934c380c1e5be7fcf5542459232cf))
* **property-performance:** improve back action ([3789783](https://gitlab.pepita.io/getrix/mls-site/commit/3789783077f8639c1df8a5e7aa83d7b4d16cce9d))
* show pagination for 10 results or more ([995a19e](https://gitlab.pepita.io/getrix/mls-site/commit/995a19e8401d7895ea93ca0e99ba1cc6fc06bda1))
* style ([9d3b4cd](https://gitlab.pepita.io/getrix/mls-site/commit/9d3b4cd8c7cb3fca93874bd60ee0bc3daf4828b3))


### Refactor

* created helper function to build performance url based on the query params ([1d419b0](https://gitlab.pepita.io/getrix/mls-site/commit/1d419b090c749f68fce22d12c940a23e647a32ec))


### Other

* cleanup immobiliare_sdk.yml ([707ec66](https://gitlab.pepita.io/getrix/mls-site/commit/707ec66718eeefa3852b27a8b8b6b6ad29cfbf4a))
* removed soa sdk dependency and cleanup - imported soa stubs ([c6a90c7](https://gitlab.pepita.io/getrix/mls-site/commit/c6a90c76efb9103b850d2b22062e2144f787ff4a))
* update cms trans to 1.83.3 ([7e7b471](https://gitlab.pepita.io/getrix/mls-site/commit/7e7b4712d024bb654c1f77edbc601f3ab85e9c23))
* upgdated common to 7.56.0 ([ad983b4](https://gitlab.pepita.io/getrix/mls-site/commit/ad983b4eb566b3321ba3a3496f7ef7252ee912a6))
## [6.70.3](https://gitlab.pepita.io/getrix/mls-site/compare/v6.70.2...v6.70.3)    (2025-07-29)


### Other

* **properties-lists:** title label key changed ([4b930e3](https://gitlab.pepita.io/getrix/mls-site/commit/4b930e3d7a3b4302bc919bb8e4e1ed1c1cbb2e38))
## [6.70.2](https://gitlab.pepita.io/getrix/mls-site/compare/v6.70.1...v6.70.2)    (2025-07-29)


### Bug Fixes

* **menu:** label.on_singular HR/SR ([e921fa4](https://gitlab.pepita.io/getrix/mls-site/commit/e921fa4ac2f964830c5c7e836fb33e6f7a0d59f1))
## [6.70.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.70.0...v6.70.1)    (2025-07-29)


### Other

* cms and cms-contents updated ([855f0f1](https://gitlab.pepita.io/getrix/mls-site/commit/855f0f1587181e3281fc62e66892c4b3cb109bda))
## [6.70.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.69.0...v6.70.0)    (2025-07-28)


### Features

* Refactor SortableHeaderButton to accept children and remove redundant SortableButton component ([3851be3](https://gitlab.pepita.io/getrix/mls-site/commit/3851be3be57026b46da35b072ee6c1ffbfe0db7b))
* **calendar:** DateRangePickerInput now can render calendar on top, based on window's available space ([7ecf9d5](https://gitlab.pepita.io/getrix/mls-site/commit/7ecf9d549d384d47219c111c0a96ae9d2dffdb50))
* remove mixpanel properties_obtain_cadastral_data_listing ([b6ffedc](https://gitlab.pepita.io/getrix/mls-site/commit/b6ffedcc5451ffd2bbb28782b71af8629528e52c))
* **unified-list:** mixpanel properties_sort_list ([c0ec2b3](https://gitlab.pepita.io/getrix/mls-site/commit/c0ec2b326f1b97d0635002d0b400fe782a5b2d5e))


### Bug Fixes

* **billboard:** workaround for dirty stored data for billboard images ([75a7fd9](https://gitlab.pepita.io/getrix/mls-site/commit/75a7fd946fb8bb38136a6e4be3d53988da905f20))
* **multisend:** on send all action the properties params was not set generating a 400 bad request ([bf2fb1d](https://gitlab.pepita.io/getrix/mls-site/commit/bf2fb1d016383a5a1f2f1b6f1d37d17199f46413))
* **properties-list:** reset input value to prevent showing previous suggestions on input focus ([1d4b994](https://gitlab.pepita.io/getrix/mls-site/commit/1d4b99438bd203fd0780c4e828d77169f9244f69))
* **remote-visits:** test ([fc629f5](https://gitlab.pepita.io/getrix/mls-site/commit/fc629f5846790084963537b795ce6e48c874fd4e))


### Other

* re-introduce `mandateTo` filters ([a7a75d3](https://gitlab.pepita.io/getrix/mls-site/commit/a7a75d366b1a7094ffae9c25c2c633e9dccf0c53))
* **settings-remote-visits:** changed folder name from setteing-visite-a-distanza to settings-remote-visits ([859da20](https://gitlab.pepita.io/getrix/mls-site/commit/859da2086a8a9c5db7c0e35329654d7ba4f67b7e))

## [6.69.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.68.0...v6.69.0)    (2025-07-24)


### Features

* add back button to performance header ([6edafc5](https://gitlab.pepita.io/getrix/mls-site/commit/6edafc5a51b4eec642f52b4c69ce40ffc6ced2e1))
* autoscroll to selected property ([8e69c6c](https://gitlab.pepita.io/getrix/mls-site/commit/8e69c6c39943fde3864ad530d736266f646df491))
* **property-list:** add new version of emptyState ([401dd43](https://gitlab.pepita.io/getrix/mls-site/commit/401dd4364e10bd139d085c0f5dc36121ae113f91))


### Bug Fixes

* **list:** unlock icon ([20ec8e0](https://gitlab.pepita.io/getrix/mls-site/commit/20ec8e03fec90aed0bb9092f236c294b56af7a21))
* messaging add additional check on favourite filter on list threads ([37780d9](https://gitlab.pepita.io/getrix/mls-site/commit/37780d9b0f6ffea96dda1dc52b113278c7f54846))
* **performance:** listening to isFetching for initial page load ([50000c1](https://gitlab.pepita.io/getrix/mls-site/commit/50000c19b4e73fddb72238d115e4a1049afa881a))
* **unified-properties:** duplicate ad error fix ([8d9a03f](https://gitlab.pepita.io/getrix/mls-site/commit/8d9a03f9c94c43755c1307f3ffb95bf654d0c75c))
* **unified-properties:** prevent crashing on empty autocomplete input ([512e6c0](https://gitlab.pepita.io/getrix/mls-site/commit/512e6c0ab8d6ee9a17203c84d0d823dcb7906c7e))


### Other

* **menu:** changed invoices endpoint for LU ([2d672f0](https://gitlab.pepita.io/getrix/mls-site/commit/2d672f07186965e820eeb1804e4062f10dba4da7))
* upgrade cms-contents to 1.13.2. Replaced kuce with kuće ([440369c](https://gitlab.pepita.io/getrix/mls-site/commit/440369cd7b2d790b8264c67321d90a85472b150d))
* upgraded cms-contents trans to 1.13.8 ([0140e6c](https://gitlab.pepita.io/getrix/mls-site/commit/0140e6c1e417916ac8b376aa44a692bff9b872d2))
## [6.68.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.67.0...v6.68.0)    (2025-07-23)


### Features

* Add search functionality to PropertyListAutocomplete and reset form on close ([7d3b2ca](https://gitlab.pepita.io/getrix/mls-site/commit/7d3b2ca10b7a3302c0207c6b8fe40d1e6c85fc18))
* add translation for search label in PropertyListAutoComplete ([a60a124](https://gitlab.pepita.io/getrix/mls-site/commit/a60a124261b94bdbdad3f65d48f80574874121c8))
* improve number character for autocomplete, display error ([5cc912f](https://gitlab.pepita.io/getrix/mls-site/commit/5cc912ffa9d1fb9d241cc2b0cc74a7db83d2c321))
* **invoice:** add pay button ([16908f9](https://gitlab.pepita.io/getrix/mls-site/commit/16908f999f1c6436c580f8c278718fda1ac90de4))
* **remote-settings:** switch fapi on on prod ([9d9282d](https://gitlab.pepita.io/getrix/mls-site/commit/9d9282dc7ec33c75e060f0e53fcc87c0dae29c1f))
* **unified-properties:** mixpanel properties_open_details_listing ([5f9a291](https://gitlab.pepita.io/getrix/mls-site/commit/5f9a291f1e2d11d96dafa2fc1c2c9974dc536fb4))
* **unififed-properties:** mixpanel properties_edit_listing from detail dialog edit btn click ([e1a6069](https://gitlab.pepita.io/getrix/mls-site/commit/e1a606945036242a8b1fdac3edd71e47d0141126))


### Bug Fixes

* add key prop to MemoIdCell and Checkbox in PropertyList columns ([52962c1](https://gitlab.pepita.io/getrix/mls-site/commit/52962c12b528bc136a692aec3e269dfc79466ce7))
* add search behavior ([1644f1f](https://gitlab.pepita.io/getrix/mls-site/commit/1644f1f47d3a6d0c676e70c0c0055dd9bf9c57de))
* **dashboard:** show all - list endpoint ([4421500](https://gitlab.pepita.io/getrix/mls-site/commit/4421500761649f5683050b76ede9dd04a0a52c16))
* **menu:** added temporary patch to handle auction menu. Removed unique and match logic from gx-navigation ([da05fa7](https://gitlab.pepita.io/getrix/mls-site/commit/da05fa78a35957f0bf93da51350f68aa06c25943))
* **messaging:** removing invalid filter values from list query string api call ([3a4ca26](https://gitlab.pepita.io/getrix/mls-site/commit/3a4ca2631a710c377e1c98ea35de5c020a5daa00))
* **portal-properties-list:** filter agents firstname and lastname check ([0db1e17](https://gitlab.pepita.io/getrix/mls-site/commit/0db1e170ee6677cfc55d76d97de8ec1c3c2bb1c4))
* **property-performance:** remove fixed height if left sidebar is off ([0537fb6](https://gitlab.pepita.io/getrix/mls-site/commit/0537fb6a593adea5b5a79826dd55189a087e10fe))
* typo rebase ([d6ed3be](https://gitlab.pepita.io/getrix/mls-site/commit/d6ed3be7db941db5ba07b5cd50d22f0eb1f29a60))
* **unified-properties:** fix deselectable publication filter ([6987a8a](https://gitlab.pepita.io/getrix/mls-site/commit/6987a8a2c337d0b20544cb3c96e03fdd444f8384))


### Other

* added payment link to invoice ([17c778c](https://gitlab.pepita.io/getrix/mls-site/commit/17c778cf4b6ab1f4babe6d52e4bf4f4cf5b3c58b))
* dev env endpoiont ([50f87f4](https://gitlab.pepita.io/getrix/mls-site/commit/50f87f4db7b2fb17011ce26f81e3a5f57dad48af))

## [6.67.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.66.0...v6.67.0)    (2025-07-21)


### Features

* **unified-properties:** mixpanel premium activate/deactivate action ([caea0c7](https://gitlab.pepita.io/getrix/mls-site/commit/caea0c7f3efb6b20b1830973b4e027a4711584e1))


### Bug Fixes

* **performance:** hover sidebar property ([82528b9](https://gitlab.pepita.io/getrix/mls-site/commit/82528b9d4468d82ae068d33a2f1e71030e5b0267))
* **property-performance:** introduce growthbook flag for left sidebar ([65512a1](https://gitlab.pepita.io/getrix/mls-site/commit/65512a1424ffe6785d059f0f712de76add29196a))


### Other

* comment bagde promotion temporary ([79ef96b](https://gitlab.pepita.io/getrix/mls-site/commit/79ef96bc63c5960c184df1a0f0e53d9431a44c47))
* restored promotion badge on list menu elements ([14c9c40](https://gitlab.pepita.io/getrix/mls-site/commit/14c9c4058c1a90cc202495008d18c8b16f7eacb7))
## [6.66.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.65.2...v6.66.0)    (2025-07-17)


### Features

* **property-performance-sidebar:** implementation of the sidebar inside property performance ([0eb9b60](https://gitlab.pepita.io/getrix/mls-site/commit/0eb9b603f32e344c6b0b60c5bb13d440d49f2874))


### Bug Fixes

* **list:** side item hover ([c74df50](https://gitlab.pepita.io/getrix/mls-site/commit/c74df50375dbbf2a0952b639d8694a2f12da452f))
* **menu:** properties list promotion badge ([ced7854](https://gitlab.pepita.io/getrix/mls-site/commit/ced7854cd0f9892d9f80d5bd068b04ec9376f347))
* **multisend-portals:** residual calculation ([6d0920c](https://gitlab.pepita.io/getrix/mls-site/commit/6d0920c7a0121625c8c52ad4c0d4ab483ce1ada4))
* **printProvider:** Use old print api when getrix is enabled ([194e193](https://gitlab.pepita.io/getrix/mls-site/commit/194e193eaf2c6e9b707334b87e2cc35dbfc12ffb))
* **properties-list:** change behaviour of Getrix print to link new trunk endpoint ([19f7aac](https://gitlab.pepita.io/getrix/mls-site/commit/19f7aac097464977ea77da5bba2d4f5f9209b223))
* **properties-list:** turistic more actions ([3e83a0c](https://gitlab.pepita.io/getrix/mls-site/commit/3e83a0c4ca565405716af472518b20c1a5eeb76f))
* **signup:** typo in serbia link ([f328ba0](https://gitlab.pepita.io/getrix/mls-site/commit/f328ba01fa6b28e83b7719ea0b88803c4461cbbc))
* **unified-properties:** useMemo on query client ([e35871b](https://gitlab.pepita.io/getrix/mls-site/commit/e35871b6b9628af7d3235961d40a6c7f6f2b1607))


### Refactor

* replace errorBoundary gtx-react ([eb3eeea](https://gitlab.pepita.io/getrix/mls-site/commit/eb3eeea6262e6a7ac011c8f55e98100d0547716f))


### Other

* update AuctionDataMapper to handle null checks and translated labels ([aab5487](https://gitlab.pepita.io/getrix/mls-site/commit/aab5487ac25ab25e50cde69e92a24b12ef470ca1))
* update cms-contents trans to 1.13.1 ([f4c6684](https://gitlab.pepita.io/getrix/mls-site/commit/f4c6684290f6fb931f24d65accec5f3929a7d813))
* **vitest:** increase timeout of async operations ([28f8bff](https://gitlab.pepita.io/getrix/mls-site/commit/28f8bff4e45394b7a8cdbab982a2bf2a445f79c1))

## [6.65.2](https://gitlab.pepita.io/getrix/mls-site/compare/v6.65.1...v6.65.2)    (2025-07-15)


### Bug Fixes

* **list:** columns resize z-index ([19ebdd0](https://gitlab.pepita.io/getrix/mls-site/commit/19ebdd09a5ca09d1c5d1f4a2ac35fa09313d863a))
* **properties-list:** endpoint convert to rented typo ([aa7c6d2](https://gitlab.pepita.io/getrix/mls-site/commit/aa7c6d29ba97e4a902b75d5794d97a4e1b6c83bd))


### Other

* **menu:** remove old annunci structure for IT ([caff073](https://gitlab.pepita.io/getrix/mls-site/commit/caff0731212b37ce247f8f2e551029d6ebff2c44))
## [6.65.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.65.0...v6.65.1)    (2025-07-14)


### Bug Fixes

* **properties-list:** restored initial Loader ([2f9ca6c](https://gitlab.pepita.io/getrix/mls-site/commit/2f9ca6cd6e75ce6c9e28b7a945de0581694a51e9))
## [6.65.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.64.0...v6.65.0)    (2025-07-14)


### Features

* **unified-properties:** share quick action ([14194de](https://gitlab.pepita.io/getrix/mls-site/commit/14194dea6b07c90a6e7c399b599fd6b53e55ad00))


### Bug Fixes

* add missing properties to `SuggestionsDataMapper` and `ListPropertiesFiltersRequest` ([b68b850](https://gitlab.pepita.io/getrix/mls-site/commit/b68b8506886e43f6f8ec114096a4bcb8a13bfe7a))
* **list:** columns size ([88fd193](https://gitlab.pepita.io/getrix/mls-site/commit/88fd193a311213eafa948065eee81eb23328089f))
* **list:** filters dropdown ([5ed90db](https://gitlab.pepita.io/getrix/mls-site/commit/5ed90dbccf33fa7e74ad644d6d805b894b6cd1cb))
* **list:** various fixes ([d684440](https://gitlab.pepita.io/getrix/mls-site/commit/d68444039ca1cbc549150add249ebaba348eee7b))


### Other

* **list:** skeleton table ([ae86174](https://gitlab.pepita.io/getrix/mls-site/commit/ae86174189066d63c13f1d8fc0702aee91307c8f))
* update translatiosn cms to 1.78.2 ([f04d4e5](https://gitlab.pepita.io/getrix/mls-site/commit/f04d4e53b5e9b6306a6df8413307e36c986aabaf))
## [6.64.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.63.0...v6.64.0)    (2025-07-11)


### Features

* introduce `AbstractDomainException` and improve exception handling ([7b83f6c](https://gitlab.pepita.io/getrix/mls-site/commit/7b83f6cc82dd064f0f1bd24894d3055e46e43c36))

## [6.63.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.62.1...v6.63.0)    (2025-07-11)


### Features

* add `onPortal` filter in `ListPropertiesFiltersRequest` ([16ccbb1](https://gitlab.pepita.io/getrix/mls-site/commit/16ccbb1cfd514d08302ef59bbaef3f2cff64a7a1))
* **filters:** add publication filter to PropertyList components ([0e1e53c](https://gitlab.pepita.io/getrix/mls-site/commit/0e1e53c2040d2a5eef0e9e0eed7647adde80b1d2))
* **portal-properties:** share button ([28bf04a](https://gitlab.pepita.io/getrix/mls-site/commit/28bf04a8945d5381745bfd8ba4b68318a66a34ce))
* **share-button:** remove anteprima param from url ([dfbc7cf](https://gitlab.pepita.io/getrix/mls-site/commit/dfbc7cf8cf256b4fe1e604a7691fd5ec1c6503d0))
* **unified-properties:** added visibilities popovers ([9f2cede](https://gitlab.pepita.io/getrix/mls-site/commit/9f2cede2d4e1b4e959eb5ea1e19e1a542dafd715))


### Bug Fixes

* **ActionsCell:** update visibility logic to exclude 'archived' and 'draft' statuses ([67eda93](https://gitlab.pepita.io/getrix/mls-site/commit/67eda93ae973e8a648b119c6da6456f6e67bf902))
* **ActionsCell:** update visibility toggle logic to include 'sky' condition ([23cb481](https://gitlab.pepita.io/getrix/mls-site/commit/23cb48184c00b31b1242eb10baed8ebb7d15b9a8))
* ensure `on_portal` is validated correctly in `ListPropertiesFiltersRequest` ([39470af](https://gitlab.pepita.io/getrix/mls-site/commit/39470afac3e987fe6595f524ca08292e12922fa4))
* **properties-list:** hide position on archived and draft view ([0a94e69](https://gitlab.pepita.io/getrix/mls-site/commit/0a94e69f658223faa336673e286c50f54108c86c))
* **properties-list:** publication filters only on getrix ([a62235c](https://gitlab.pepita.io/getrix/mls-site/commit/a62235cf9cbf648695f970a31d39b55ddce31364))
* **properties-list:** show only deactivate premium if related contract premium spaces are over ([4aa8d1c](https://gitlab.pepita.io/getrix/mls-site/commit/4aa8d1c9291ca7cf3b1eb831355b7e713aad959a))


### Other

* **properties-list:** agency page only label ([68c35b4](https://gitlab.pepita.io/getrix/mls-site/commit/68c35b4fce46d8ef965ab3f81f4412bae95f207d))

## [6.62.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.62.0...v6.62.1)    (2025-07-09)


### Bug Fixes

* **properties-list:** changed sold rent action label ([8423271](https://gitlab.pepita.io/getrix/mls-site/commit/8423271fd1650232d4998e7778dbbbeb2250856d))


### Other

* new construction counter depends only on its extension status ([ec25690](https://gitlab.pepita.io/getrix/mls-site/commit/ec25690318577033ebc1b634f8367fa379724deb))
## [6.62.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.61.0...v6.62.0)    (2025-07-09)


### Features

* **ad-detail-modal:** map section improvements ([6a01e23](https://gitlab.pepita.io/getrix/mls-site/commit/6a01e23dcfea13897220b7fe8d9299f2fc29f7ec))
* enhance extravisibility handling for agency pages and add new visibility types ([ddade11](https://gitlab.pepita.io/getrix/mls-site/commit/ddade1122fb6d0a9d6c72404dde36f6270021389))
* **properties-list:** moved visibility column at 2nd position ([cdea100](https://gitlab.pepita.io/getrix/mls-site/commit/cdea10065671e79657cc72e101fb356e4b2debf9))


### Bug Fixes

* **actions-cell, extravisibility-cell:** integrate new construction module checks in actions ([d5a639e](https://gitlab.pepita.io/getrix/mls-site/commit/d5a639e30b4a31522668f6282453a12f31a7cb09))
* **delete-property:** show notification on delete error ([f3e5003](https://gitlab.pepita.io/getrix/mls-site/commit/f3e500323df838e219b27802d764aea7e944d4ba))
* **list:** quick filters ([df0a170](https://gitlab.pepita.io/getrix/mls-site/commit/df0a170a4f6ee29c1c8d69dd070343ec562f8403))
* **price-filter:** added missing input placeholders ([0b6e5eb](https://gitlab.pepita.io/getrix/mls-site/commit/0b6e5ebb0c25800526ae2beb0e39f6ac22503c4f))
* **properties-list:** active getrix property has to show promote cta ([f8a3f5e](https://gitlab.pepita.io/getrix/mls-site/commit/f8a3f5e3fd491e9a766765472770f6d960a95887))
* **properties-list:** adds conditions on actionCell component ([037f1bb](https://gitlab.pepita.io/getrix/mls-site/commit/037f1bbda9ad2ad549d823176a5ebf48d9783b71))
* **properties-list:** avoid to show agency page on draft and archived ([ae1ed4d](https://gitlab.pepita.io/getrix/mls-site/commit/ae1ed4de2096fa08b8b24211d3275a8314846096))
* **properties-list:** promote cta on status 21 ads ([61d7bfb](https://gitlab.pepita.io/getrix/mls-site/commit/61d7bfbe9663eb713cab66bd8ef8189837995336))
* **properties-list:** redirect on new construction landing page if module not enabled ([045623b](https://gitlab.pepita.io/getrix/mls-site/commit/045623b90e462623633b1155a7cc66dc73576df6))
* **properties-list:** remove address from filters on autocomplete reset ([bec7ba1](https://gitlab.pepita.io/getrix/mls-site/commit/bec7ba12fa77511854a957cc4fd92cf311307d71))
* **properties-list:** search by city and address from autocomplete ([6f90cb1](https://gitlab.pepita.io/getrix/mls-site/commit/6f90cb16c16683cec4951a3f1ddde0d5308fe6a9))
* update ListPropertiesController filters and refactor DeletePropertyController attributes ([e97b722](https://gitlab.pepita.io/getrix/mls-site/commit/e97b72239e1c57ed798230d6574a80c8432c063e))


### Other

* **properties-list:** adds badge for agency page visibility ([dc1c561](https://gitlab.pepita.io/getrix/mls-site/commit/dc1c5614800f028fbb9b5763255ef39e192768e8))
* **properties-list:** only agency page text into visibility column ([9228aaf](https://gitlab.pepita.io/getrix/mls-site/commit/9228aafeddf6f6d791d4bb55ac0dd15eaa16b183))
* update cms translations to 1.76.0 ([3b667e1](https://gitlab.pepita.io/getrix/mls-site/commit/3b667e1ec3d29cc8590b53331a1af6d1620f7ae1))
## [6.61.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.60.0...v6.61.0)    (2025-07-08)


### Features

* **PropertyListAutocomplete:** enhance suggestions handling and display logic ([b3f286d](https://gitlab.pepita.io/getrix/mls-site/commit/b3f286df0f8a8f97db0b459a08165ab38e2f5e5b))
* add CountersHandler for property counters ([9e9ef77](https://gitlab.pepita.io/getrix/mls-site/commit/9e9ef77623c860d6ef414885f87b6108057c2a1a))
* add tracking to property dialogs and components ([f53d0eb](https://gitlab.pepita.io/getrix/mls-site/commit/f53d0ebd0b9ba138175b08454ee3911b93cbfc7a))
* add type 'property' to api properties list ([be901ee](https://gitlab.pepita.io/getrix/mls-site/commit/be901ee4993d0654aa08bf253634ab57819bb662))
* enhance property filters and add new construction module support ([3885fa4](https://gitlab.pepita.io/getrix/mls-site/commit/3885fa471366ddbb434e06d3a88a2e4d29be36db))
* introduce `ListPropertiesFiltersRequest` DTO and argument resolver ([60e3247](https://gitlab.pepita.io/getrix/mls-site/commit/60e32472cffc5101068477c608a3a57d146aa6db))
* **messaging:** temporary disabling getAgencyStats due to performance reasons ([ff5620f](https://gitlab.pepita.io/getrix/mls-site/commit/ff5620f63b70b14242c3fb037915ddb28ecd18e8))


### Bug Fixes

* **ActionsCell:** update visibility handling to use correct extra visibilities payload ([3fe6f26](https://gitlab.pepita.io/getrix/mls-site/commit/3fe6f26cdd36abbea1bc1ab0ad4aa1b9a47769d1))
* DTO re-mapping ([f1502c2](https://gitlab.pepita.io/getrix/mls-site/commit/f1502c2c3d053bdd0f6697c8471eeeac77843f26))
* **PropertyList:** rename cityId to city and update related filter handling ([e714fe5](https://gitlab.pepita.io/getrix/mls-site/commit/e714fe5bfa648265b493e3c7b5d8531b795e1f63))
* **PropertyListAutocomplete:** trim debounced input value ([6ec58cb](https://gitlab.pepita.io/getrix/mls-site/commit/6ec58cb35eb6f6ebc3c48505930b0def8a6ed687))
* Rent Contract should be 2 ([58d0976](https://gitlab.pepita.io/getrix/mls-site/commit/58d0976444b6f6fe27860c94bfe1e67cec4c1980))
* **Sortable:** integrate table reset functionality on reset button click ([1036045](https://gitlab.pepita.io/getrix/mls-site/commit/1036045c0e05fda1c4ab3904b65224622bad0a31))
* added modules to rest profile me endpoint ([56bb1b2](https://gitlab.pepita.io/getrix/mls-site/commit/56bb1b2b6a695fe8c2527b670585e4fc14195986))
* **cancel-button-modal-ghost:** fixed variant for cancel button in modal ([9a94355](https://gitlab.pepita.io/getrix/mls-site/commit/9a94355aebe046c278921e292ba0085e3a92b726))
* draft status in list properties ([b2c5d21](https://gitlab.pepita.io/getrix/mls-site/commit/b2c5d2133e88616da8d2f8c60c7b5f11d2db167f))
* **growthbook:** corrected Growthbook behavior in countries where it's disabled. ([c413f94](https://gitlab.pepita.io/getrix/mls-site/commit/c413f948f9414c244e0232f605de02da537fe7fe))
* handle cadastral data exceptions in AdDataMapper ([a84a15e](https://gitlab.pepita.io/getrix/mls-site/commit/a84a15e17ed1d7e061da7289570638d0a255fd43))
* **list:** cell id ([4eeddc1](https://gitlab.pepita.io/getrix/mls-site/commit/4eeddc16820c267091402144b2686c448e3a6e13))
* **properties-list:** add existance test toa void bad visualization of counters ([831f0e7](https://gitlab.pepita.io/getrix/mls-site/commit/831f0e7d2cf54bc2955ee60ad1fc59a498925831))
* **properties-list:** export csv endpoints ([1d2d8ae](https://gitlab.pepita.io/getrix/mls-site/commit/1d2d8ae28740112a956508ebed059ead4e5c1c39))
* **properties-list:** not short visualizazion if one extra visibility active ([bdd3c73](https://gitlab.pepita.io/getrix/mls-site/commit/bdd3c73061787598a9dc9e9d5dd7e76e1645b89a))
* **properties-list:** target blank on explore cta ([229f8e1](https://gitlab.pepita.io/getrix/mls-site/commit/229f8e160253dd2cbface8565f16a2b57278e8eb))
* **propertyListMapper:** update status assignment logic for property visibility ([30e9b83](https://gitlab.pepita.io/getrix/mls-site/commit/30e9b836f2d9bd5767d76c328346ba9276c09d5c))
* **style-send-email-form:** fix style ([d92ce09](https://gitlab.pepita.io/getrix/mls-site/commit/d92ce09b77f2e30ce66826861f8f6738e8de746a))
* **unified-properties:** Archive icon change and hide quick filters on small breakpoints ([14c8678](https://gitlab.pepita.io/getrix/mls-site/commit/14c8678311a5e65b63d5cd52242597c21dc774db))
* **unified-properties:** disable all extravisibilites when disabling premium extravisibility ([4a53fdf](https://gitlab.pepita.io/getrix/mls-site/commit/4a53fdfcad3598c4d6f0fceac71413e1f8e5bd79))
* **unified-properties:** fixed api call to extravisibility change ([124a654](https://gitlab.pepita.io/getrix/mls-site/commit/124a654a71b0ead43baff71192bc4903b6cdffbe))
* **unified-properties:** various list fixes ([a400c5a](https://gitlab.pepita.io/getrix/mls-site/commit/a400c5abb39e59dcff946722b7820959f4001101))
* **unified-properties:** various list fixes ([f0a7da4](https://gitlab.pepita.io/getrix/mls-site/commit/f0a7da4407fbcb9418c07ce6104adea27a18a07b))
* **utils:** include contract in getCategoriesByTypology return value ([b285a4a](https://gitlab.pepita.io/getrix/mls-site/commit/b285a4a3832c73ce7b3244585729f156288eb411))
* workstation quantity ([168a1bf](https://gitlab.pepita.io/getrix/mls-site/commit/168a1bf5e47606d22dda1da4f5bbb2ced4616a56))


### Other

* set country in logo for page error ([ec8b306](https://gitlab.pepita.io/getrix/mls-site/commit/ec8b3068267ae73431232a0514dfc7381282057b))
* **webpack:** temporary disable sentryWEbpackPlugin ([8b8890f](https://gitlab.pepita.io/getrix/mls-site/commit/8b8890f85a72759b88eb7615cd2c2b5707c7ad6e))
## [6.60.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.59.0...v6.60.0)    (2025-07-04)


### Features

* **PropertyList:** enhance state persistence with status handling ([3fbd965](https://gitlab.pepita.io/getrix/mls-site/commit/3fbd9652e5e0132d15a46e6ae1804bb015850b9c))


### Bug Fixes

* **PerformanceIndicator:** update button to render as anchor element ([900c735](https://gitlab.pepita.io/getrix/mls-site/commit/900c7357844bf250e32be99fa27b0eea65fc7dc3))
* **Sortable:** update onReset to accept items and conditionally render reset button ([f594d1f](https://gitlab.pepita.io/getrix/mls-site/commit/f594d1f6ff2dbc4565d3381696ef188a56f600ec))
* new construction visibility ([8ea6562](https://gitlab.pepita.io/getrix/mls-site/commit/8ea6562379b4d8b89a6e69986401811f02a91bf1))
* **properties-list:** handle of sold status param ([0d566a2](https://gitlab.pepita.io/getrix/mls-site/commit/0d566a24021e4cfd25e3c1f0266e5bd987a6a0ca))
* **properties-list:** sold active status mapping ([31f10e1](https://gitlab.pepita.io/getrix/mls-site/commit/31f10e15b5ab4c9654ef29d6914298aa12163c16))
* removed unsed fields from agency general data ([2104ba6](https://gitlab.pepita.io/getrix/mls-site/commit/2104ba61622626a069034da016fe95ee6c21046b))

## [6.59.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.58.0...v6.59.0)    (2025-07-03)


### Features

* **PropertyList:** add empty state for no results and improve filter handling ([4c3e552](https://gitlab.pepita.io/getrix/mls-site/commit/4c3e5527365e527388b0d9342aa0fa231bdf8487))
* **filters:** implement default status handling for sold and rent properties ([37011bc](https://gitlab.pepita.io/getrix/mls-site/commit/37011bc65347b35c7d0bd77e23f56ef49f3161db))


### Bug Fixes

* **ExtravisibilityCell:** hide 'searchable' visibility when multiple types are active ([f1f28df](https://gitlab.pepita.io/getrix/mls-site/commit/f1f28df0c5d7580da0831c6a53806ad6286f20ec))
* **properties-list:** mixpanel configs ([75efcd2](https://gitlab.pepita.io/getrix/mls-site/commit/75efcd2a6b5f9b9194a293a1111ee160a60b9e03))
* **property-performance:** handle back by growthbook flag ([49d9ca5](https://gitlab.pepita.io/getrix/mls-site/commit/49d9ca520ca7496d45b3556e3b7c3078653a254b))


### Refactor

* **visibility-summary:** rename and restructure error handling components ([88fd8b7](https://gitlab.pepita.io/getrix/mls-site/commit/88fd8b7c38ae9e7c95dee43e46a6b4e9bbc79f7f))
## [6.58.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.57.0...v6.58.0)    (2025-07-03)


### Features

* **admin-contract:** add secret visibility row ([33a7cfe](https://gitlab.pepita.io/getrix/mls-site/commit/33a7cfe451a3bb0e1c1bd2af43969e8231239afa))
* **filters:** add status filter options for sold and rent properties ([5630bb6](https://gitlab.pepita.io/getrix/mls-site/commit/5630bb6dcecb18ae75a6b6862e76f10d8d6b8d6f))
* **scrollable-row:** Removed items props in favor of children map ([cf761ec](https://gitlab.pepita.io/getrix/mls-site/commit/cf761ec4fa6ab4276602bef2df1fbe13bf34fe3d))
* send email modal porting [not-working][moving-to-openstack] ([c8cab28](https://gitlab.pepita.io/getrix/mls-site/commit/c8cab287c2978d80d6fb92a46dbd467973fbb5e3))


### Bug Fixes

* **billboard:** handle backurl by new list flag ([bb9f8c3](https://gitlab.pepita.io/getrix/mls-site/commit/bb9f8c31c2e09a6e81eca00d3a31eddfadab1199))
* **list:** filters and sticky shadow ([99c93a7](https://gitlab.pepita.io/getrix/mls-site/commit/99c93a756e77e0ace8b9763e51d31b82179067f4))
* **properties-list:** trans label convert to rented ([d413130](https://gitlab.pepita.io/getrix/mls-site/commit/d413130340f6cb6f12bb5d1b5d8215ceffa1cf37))
* **property-list-mapper:** use logical OR for title assignment ([fded100](https://gitlab.pepita.io/getrix/mls-site/commit/fded1004c9eed52da663e290fdb89a91ff1e654c))
* update filters logic and visibility exception handling for properties ([aab2b22](https://gitlab.pepita.io/getrix/mls-site/commit/aab2b222d682ad380d217b5ea126bfbafd57267b))

## [6.57.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.56.3...v6.57.0)    (2025-07-02)


### Features

* **dashboard:** adds properties list links retrieval by growthbook flag ([5fc2f2a](https://gitlab.pepita.io/getrix/mls-site/commit/5fc2f2a7c6e98598164849041c2a83a0a627cadf))
* enhance PerformanceLink component to conditionally hide for auction and new construction types ([b4c81b5](https://gitlab.pepita.io/getrix/mls-site/commit/b4c81b54b16e48f5f31f38f8dda8f30483b43c07))
* enhance sidebar scrolling behavior and update property filters ([0c41df7](https://gitlab.pepita.io/getrix/mls-site/commit/0c41df7828cf3990792b1b086ec8dc4bf67093e4))
* **registration:** hide business type on rs/hr ([5ea640d](https://gitlab.pepita.io/getrix/mls-site/commit/5ea640d63d7eefe7b95edd1a989a79043331acb5))
* **sidebar:** improved usePersistedSidebar props ([1229766](https://gitlab.pepita.io/getrix/mls-site/commit/122976637f4b887ebc9666380cc47f450e565de2))
* **unified-properties:** Added Price Range dropdown quick filter ([e727426](https://gitlab.pepita.io/getrix/mls-site/commit/e727426118b29e709a70667bb63a179bc860a69d))
* **visibility-summary:** implement countersSelect function and add tests for visibility counters ([b5ba833](https://gitlab.pepita.io/getrix/mls-site/commit/b5ba8335b7592e8a3d7d7b1c72bd5e74513b24a8))


### Bug Fixes

* **PropertyList:** conditionally render VisibilitySummary based on property status ([bf89b8f](https://gitlab.pepita.io/getrix/mls-site/commit/bf89b8f9d9560f5c835a8ecbc66ad47832ae4263))
* add exception type check to prevent calling of undefined method ([b0bc747](https://gitlab.pepita.io/getrix/mls-site/commit/b0bc747a06f5811975ef606b21aef9bc1f25e8b5))
* **list:** property list fixes ([9a51bd7](https://gitlab.pepita.io/getrix/mls-site/commit/9a51bd72e2f9fce33425f12e06466a3437651eec))
* make newConstruction property optional in ListPropertiesCounters type ([cd826a4](https://gitlab.pepita.io/getrix/mls-site/commit/cd826a4bd73c295ffff583c7caaab7ef6d3b835e))
* **new-spacing-class:** resolved merge conflicts ([82d74d8](https://gitlab.pepita.io/getrix/mls-site/commit/82d74d81dac22cb8bc8bff30faaf0cf38884ed5d))
* **properties-list:** flip closed/opened integration flag mapping ([21b6255](https://gitlab.pepita.io/getrix/mls-site/commit/21b6255ee37fc1811d55e0232ee106422b9252be))
* **quick-filters:** Replaced Scrollable Filters with Swiper and added it to Visibility to ([b915ec7](https://gitlab.pepita.io/getrix/mls-site/commit/b915ec77497bdc52827e3389986c21aa1f0d9fc8))
* restore filters without applying them twice ([624cb5b](https://gitlab.pepita.io/getrix/mls-site/commit/624cb5bcc15514f74623f1d0af9c38db8000e2b0))
* **unified-prop:** enable detail print ([cfdc962](https://gitlab.pepita.io/getrix/mls-site/commit/cfdc962e9d5409aecc51757a984df5dbdcccb617))
* **unified-properties:** various fixes ([e703916](https://gitlab.pepita.io/getrix/mls-site/commit/e703916bb3f8d71d8de6cd244bc8ee2a8efb9806))
* update json-schema for the suggestion type allowed ([6521444](https://gitlab.pepita.io/getrix/mls-site/commit/6521444ddb25d7f7abcf9fd1276fb917fdb550c4))


### Refactor

* **agency-estimates:** remove all ([49fc42f](https://gitlab.pepita.io/getrix/mls-site/commit/49fc42f83bb95d3e1c7e8009248750a79ba5bb99))


### Other

* **property-detail:** removes growthbook check ([2bc278c](https://gitlab.pepita.io/getrix/mls-site/commit/2bc278c0d95f5781c48f62ef823da2ba502ebaff))
* **psalm:** fix some psalm category 022 errors ([709a612](https://gitlab.pepita.io/getrix/mls-site/commit/709a6129a44d4568175f4fdf18f7b9928509adb5))
* release v6.57.0 ([9d637ee](https://gitlab.pepita.io/getrix/mls-site/commit/9d637ee8bfff300c79cedbe9ac06c336df37ce6e))
* trimmed extra spaces ([dd6ee14](https://gitlab.pepita.io/getrix/mls-site/commit/dd6ee1441f1731b2445731e623396580c192b693))

## [6.57.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.56.3...v6.57.0)    (2025-07-02)


### Features

* **dashboard:** adds properties list links retrieval by growthbook flag ([5fc2f2a](https://gitlab.pepita.io/getrix/mls-site/commit/5fc2f2a7c6e98598164849041c2a83a0a627cadf))
* enhance PerformanceLink component to conditionally hide for auction and new construction types ([b4c81b5](https://gitlab.pepita.io/getrix/mls-site/commit/b4c81b54b16e48f5f31f38f8dda8f30483b43c07))
* enhance sidebar scrolling behavior and update property filters ([0c41df7](https://gitlab.pepita.io/getrix/mls-site/commit/0c41df7828cf3990792b1b086ec8dc4bf67093e4))
* **registration:** hide business type on rs/hr ([5ea640d](https://gitlab.pepita.io/getrix/mls-site/commit/5ea640d63d7eefe7b95edd1a989a79043331acb5))
* **sidebar:** improved usePersistedSidebar props ([1229766](https://gitlab.pepita.io/getrix/mls-site/commit/122976637f4b887ebc9666380cc47f450e565de2))
* **unified-properties:** Added Price Range dropdown quick filter ([e727426](https://gitlab.pepita.io/getrix/mls-site/commit/e727426118b29e709a70667bb63a179bc860a69d))
* **visibility-summary:** implement countersSelect function and add tests for visibility counters ([b5ba833](https://gitlab.pepita.io/getrix/mls-site/commit/b5ba8335b7592e8a3d7d7b1c72bd5e74513b24a8))


### Bug Fixes

* **PropertyList:** conditionally render VisibilitySummary based on property status ([bf89b8f](https://gitlab.pepita.io/getrix/mls-site/commit/bf89b8f9d9560f5c835a8ecbc66ad47832ae4263))
* add exception type check to prevent calling of undefined method ([b0bc747](https://gitlab.pepita.io/getrix/mls-site/commit/b0bc747a06f5811975ef606b21aef9bc1f25e8b5))
* **list:** property list fixes ([9a51bd7](https://gitlab.pepita.io/getrix/mls-site/commit/9a51bd72e2f9fce33425f12e06466a3437651eec))
* make newConstruction property optional in ListPropertiesCounters type ([cd826a4](https://gitlab.pepita.io/getrix/mls-site/commit/cd826a4bd73c295ffff583c7caaab7ef6d3b835e))
* **new-spacing-class:** resolved merge conflicts ([82d74d8](https://gitlab.pepita.io/getrix/mls-site/commit/82d74d81dac22cb8bc8bff30faaf0cf38884ed5d))
* **properties-list:** flip closed/opened integration flag mapping ([21b6255](https://gitlab.pepita.io/getrix/mls-site/commit/21b6255ee37fc1811d55e0232ee106422b9252be))
* **quick-filters:** Replaced Scrollable Filters with Swiper and added it to Visibility to ([b915ec7](https://gitlab.pepita.io/getrix/mls-site/commit/b915ec77497bdc52827e3389986c21aa1f0d9fc8))
* restore filters without applying them twice ([624cb5b](https://gitlab.pepita.io/getrix/mls-site/commit/624cb5bcc15514f74623f1d0af9c38db8000e2b0))
* **unified-prop:** enable detail print ([cfdc962](https://gitlab.pepita.io/getrix/mls-site/commit/cfdc962e9d5409aecc51757a984df5dbdcccb617))
* **unified-properties:** various fixes ([e703916](https://gitlab.pepita.io/getrix/mls-site/commit/e703916bb3f8d71d8de6cd244bc8ee2a8efb9806))
* update json-schema for the suggestion type allowed ([6521444](https://gitlab.pepita.io/getrix/mls-site/commit/6521444ddb25d7f7abcf9fd1276fb917fdb550c4))


### Other

* **property-detail:** removes growthbook check ([2bc278c](https://gitlab.pepita.io/getrix/mls-site/commit/2bc278c0d95f5781c48f62ef823da2ba502ebaff))
* **psalm:** fix some psalm category 022 errors ([709a612](https://gitlab.pepita.io/getrix/mls-site/commit/709a6129a44d4568175f4fdf18f7b9928509adb5))
* trimmed extra spaces ([dd6ee14](https://gitlab.pepita.io/getrix/mls-site/commit/dd6ee1441f1731b2445731e623396580c192b693))
## [6.56.3](https://gitlab.pepita.io/getrix/mls-site/compare/v6.56.2...v6.56.3)    (2025-07-01)


### Bug Fixes

* **menu:** unified hierarchy. Reverted step 2, introduced step 1 ([bd17a48](https://gitlab.pepita.io/getrix/mls-site/commit/bd17a487b47f7ce0a0fe1bf1dc853f835bbc5fb0))
* temporary rollback alterstatus func ([d5ecafc](https://gitlab.pepita.io/getrix/mls-site/commit/d5ecafc27584966cd6ad7aad62fe4344e9078d4d))
## [6.56.2](https://gitlab.pepita.io/getrix/mls-site/compare/v6.56.1...v6.56.2)    (2025-07-01)


### Bug Fixes

* growthbook vip internal prod ([05a1eb6](https://gitlab.pepita.io/getrix/mls-site/commit/05a1eb6db8b77c2edc2c623d15f5933aaa8108e8))
* **portal-propertis:** move feature test from react app to php controller ([1c6bf9a](https://gitlab.pepita.io/getrix/mls-site/commit/1c6bf9a7d91e9bf559f338152c78d501ae55efb3))


### Other

* alignment envs files ([2ef9835](https://gitlab.pepita.io/getrix/mls-site/commit/2ef9835e8c45588001947ebafbd87215feed4837))
* alignments envs ([7f9aaab](https://gitlab.pepita.io/getrix/mls-site/commit/7f9aaab7fdd8a8d8beeab7f7d00b33d48d7d841b))
* improve multilanguage descriptions ([ae1ef82](https://gitlab.pepita.io/getrix/mls-site/commit/ae1ef8237f9bcb7fb77de74e42e75307243f748c))
## [6.56.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.56.0...v6.56.1)    (2025-07-01)


### Bug Fixes

* **menu:** changed id for new ads hierarchy ([40d0a78](https://gitlab.pepita.io/getrix/mls-site/commit/40d0a7882a81a7ea548ca78199892f717402e235))


### Other

* update cms translations to 1.74.0 ([d042768](https://gitlab.pepita.io/getrix/mls-site/commit/d04276899d32233c3f6adafb387776d066a19360))
## [6.56.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.55.0...v6.56.0)    (2025-07-01)


### Features

* **ActionsCell:** integrate ActionListBuilder for improved action handling ([45b1210](https://gitlab.pepita.io/getrix/mls-site/commit/45b12104387968499d25161833b16470d7a8142f))
* **PropertyListAutocomplete:** add placeholder and Italian search prompt ([89d9088](https://gitlab.pepita.io/getrix/mls-site/commit/89d90887a52917c8e41cf75821b2a80dea3316ae))
* **ad-detail-modal:** secret visibility ([67f9e48](https://gitlab.pepita.io/getrix/mls-site/commit/67f9e48bf9f819ba3f0989048d5fa3528ae640dc))
* add VisibilitySummary component and update Extravisibility usage ([66b1a02](https://gitlab.pepita.io/getrix/mls-site/commit/66b1a026ea6777561e20cd4a108940f9d0e537e1))
* add details dialog ([fcba941](https://gitlab.pepita.io/getrix/mls-site/commit/fcba941b3a5678809fb1886a48d0235a61e9e2ae))
* add type 'property' to api properties list ([670c2d1](https://gitlab.pepita.io/getrix/mls-site/commit/670c2d1dcea81123daad296b1400807a089d7317))
* enabled growthbook on messaging v2 ([e032b33](https://gitlab.pepita.io/getrix/mls-site/commit/e032b337be275684cf9515300fff5c1d47095bc1))
* implement CrmAutocomplete component ([8882f4c](https://gitlab.pepita.io/getrix/mls-site/commit/8882f4c568ab0677eb49e17a863d74f0cb475934))
* **properties-list:** navigation ([dd7072b](https://gitlab.pepita.io/getrix/mls-site/commit/dd7072bcb26518c78d38d58fe74b47630197e732))
* refactor toggle button implementation and enhance typology handling ([75f3e85](https://gitlab.pepita.io/getrix/mls-site/commit/75f3e852e58a019a68876b057a0e6f2ebee6d65e))
* secret property switch feat flag from env to growthbook ([c067592](https://gitlab.pepita.io/getrix/mls-site/commit/c067592c563b3417a90f1040543937884b542d3f))


### Bug Fixes

* Filters as Visibility on Properties List ([54fd23c](https://gitlab.pepita.io/getrix/mls-site/commit/54fd23c2ef975b9f578d24f3f1f3812689c11b29))
* **VisibilitySummary:** remove redundant label prefix from visibility descriptions ([9ddf514](https://gitlab.pepita.io/getrix/mls-site/commit/9ddf5141afe2fccba6ceba7cc20059a3793c9a45))
* condition on messaging sections - growthbook ([c17bce4](https://gitlab.pepita.io/getrix/mls-site/commit/c17bce42ebd968c8408bc50a69d2e6c47f350102))
* **editProfile:** edit biography initialValue fix ([ff6cb04](https://gitlab.pepita.io/getrix/mls-site/commit/ff6cb04f63c9571d17ea227315aa41478c66d4b6))
* growthbook feature dependencies ([67223fd](https://gitlab.pepita.io/getrix/mls-site/commit/67223fdd94a677063cfbfe7e5fbcae8e6d097369))
* growthbook proxy injection ([4a7548b](https://gitlab.pepita.io/getrix/mls-site/commit/4a7548b9e91ad928199102c4428990baa5ce9940))
* growthbook twig ([c46e6e3](https://gitlab.pepita.io/getrix/mls-site/commit/c46e6e33abb5c1e70e1c0d73bab7e0b9f4d1c941))
* **listing:** columns resizing ([d92acff](https://gitlab.pepita.io/getrix/mls-site/commit/d92acffae345e5d18f7e7d628af4ba2989bb4a2c))
* **property-performance:** adds growthbook provider ([0fae0f3](https://gitlab.pepita.io/getrix/mls-site/commit/0fae0f32e894696273de1458a1212aeefa723172))
* simplify visibility check for extra visibilities ([e4171a8](https://gitlab.pepita.io/getrix/mls-site/commit/e4171a86b7872d5b4f63e16b60c499a334ddb992))
* update growthbook bundle ([6bd9716](https://gitlab.pepita.io/getrix/mls-site/commit/6bd97164dea6a2bc40ec839afa06fec4b6ee0128))


### Other

* align json schema with frontend needs ([bcb3543](https://gitlab.pepita.io/getrix/mls-site/commit/bcb35437c4e5b3bb2c8735d5902d8394d4ce7167))
* define json-schema ([9b4cfdf](https://gitlab.pepita.io/getrix/mls-site/commit/9b4cfdf03b3cd766dd78c3d5055439c1df03c392))
## [6.55.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.54.0...v6.55.0)    (2025-06-30)


### Features

* **QualityCell:** integrate viewport visibility check and optimize quality query handling ([0b41131](https://gitlab.pepita.io/getrix/mls-site/commit/0b4113156331ef4b8bb4175f90f7347c1116645e))
* add mappers and models for portions and coworking timetable ([78ff0a5](https://gitlab.pepita.io/getrix/mls-site/commit/78ff0a56bf5cb40c692f8964140c29bbf9dfb83f))
* add modal ([839d8dd](https://gitlab.pepita.io/getrix/mls-site/commit/839d8ddfd365477db30a47350259d3a005cff919))
* secret property switch feat flag from env to growthbook ([c55c15e](https://gitlab.pepita.io/getrix/mls-site/commit/c55c15e2ee701acbb657b054ee6e4e13408cd2b6))


### Bug Fixes

* add missing mapping for geography information property ([bcc860f](https://gitlab.pepita.io/getrix/mls-site/commit/bcc860fd24257970af0b3a3910a24c50636bba18))
* cleanuo ([afc7021](https://gitlab.pepita.io/getrix/mls-site/commit/afc70212e5ea85a1a520975ba07af9cb31038460))
* disable/reset price fields for new construction and auction properties ([9e320f6](https://gitlab.pepita.io/getrix/mls-site/commit/9e320f613df68581b7fc00e589454aef77708211))
* **ecommerce-products:** move secret property visibility at the bottom ([dd56cda](https://gitlab.pepita.io/getrix/mls-site/commit/dd56cda9ab0f28a85a2f93f880344b852dbe91af))
* geography information can be null ([56afb5f](https://gitlab.pepita.io/getrix/mls-site/commit/56afb5f7c7b88811b32cadf4c812ef1786c65f0c))
* **portal-properties:** hide position value if property is secret ([7e4c7c0](https://gitlab.pepita.io/getrix/mls-site/commit/7e4c7c018384e60e72f15a560fe33cda9bcf6857))
* **properties-list:** add check on new construction extension status ([a2333c4](https://gitlab.pepita.io/getrix/mls-site/commit/a2333c40d2a06e32892aa4164f5764d88270debd))


### Refactor

* enhance ShopDataMapper logic and update ShopResponse properties ([cf04979](https://gitlab.pepita.io/getrix/mls-site/commit/cf049793c3a23c41602ed6404546c3330cd63a9a))
* parametrize geocode language ([fdb4c65](https://gitlab.pepita.io/getrix/mls-site/commit/fdb4c65da26eb39374f71aa6b15c18ed6d90065b))


### Other

* Immobiliare PRO agency must not see for status 2x ([f9d5a21](https://gitlab.pepita.io/getrix/mls-site/commit/f9d5a21649c0ae43fb41fb7d1763dc73e8e6772a))
* chore: add new print provider
add print detail in unified-prop

See merge request getrix/mls-site!3535 ([a3c0380](https://gitlab.pepita.io/getrix/mls-site/commit/a3c0380fc821e594515a6cd24db2333aa7e6eedd))
* **unified-properties:** remove button add dropdown ([03c7f67](https://gitlab.pepita.io/getrix/mls-site/commit/03c7f6777313c6f6738347b4ce959eeb0d543b71))

## [6.54.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.53.1...v6.54.0)    (2025-06-25)


### Features

* hide secret from visibilities in contract page ([f78732d](https://gitlab.pepita.io/getrix/mls-site/commit/f78732d2739bf4bf408b534058182b6d3c8fa911))
* implement getDefaultQuickFilterLabel function and update QuickFilterLabel usage ([35816dd](https://gitlab.pepita.io/getrix/mls-site/commit/35816dd23f0df9a847d0b34dee6c3586d0faadd1))


### Bug Fixes

* **property-performance:** use new improvePropertyQualityContainer ([96d8396](https://gitlab.pepita.io/getrix/mls-site/commit/96d8396bfcf5d945147551eaa2913cf24a3132f0))


### Other

* **serbia:** updates common with new logos ([0ac98b2](https://gitlab.pepita.io/getrix/mls-site/commit/0ac98b21800073f569ebb0160c509cafb359693e))
## [6.53.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.53.0...v6.53.1)    (2025-06-24)


### Bug Fixes

* **ad-detail-modal:** prefetching plans to avoid layout shifting ([22e0f92](https://gitlab.pepita.io/getrix/mls-site/commit/22e0f92ce682c43bc34f5bdedaf2f933962f9c5f))
* added 100 more agencies to messaging v2 ([3bcedb0](https://gitlab.pepita.io/getrix/mls-site/commit/3bcedb050c205f336b4754c088aa739baa47c14d))
* **portal-list:** hide secret visibility filter on auction and new construction lists ([e88501e](https://gitlab.pepita.io/getrix/mls-site/commit/e88501e5ce4e245c603d12e71e7aa021e57a2bc0))
* **properties-list:** isFromBackoffice condition on ranking modal ([da104fc](https://gitlab.pepita.io/getrix/mls-site/commit/da104fc73c50e64f741d1877f282d594d3bdb27b))
* restored secret property element in ecommerce-products response ([57cd25c](https://gitlab.pepita.io/getrix/mls-site/commit/57cd25c814c63e1f53f6960509376d9ef32b7b28))


### Other

* add flagAddress in geography information ([05c69bc](https://gitlab.pepita.io/getrix/mls-site/commit/05c69bc85d6edc912b888c4911b09f33ea3b3ce7))
## [6.53.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.52.0...v6.53.0)    (2025-06-23)


### Features

* adds responsible data into /rest/profile/me endpoint ([9106bed](https://gitlab.pepita.io/getrix/mls-site/commit/9106bede5fcf62a336d1c306565721a4d88f535c))
* **contacts-stats:** Disabled smart calls if feature doesnt exist ([a976846](https://gitlab.pepita.io/getrix/mls-site/commit/a9768461746d04fed1dfb82e1c754b4fdb7955ab))
* reduce columns and actions ([bbb7d23](https://gitlab.pepita.io/getrix/mls-site/commit/bbb7d23bbd5184b77e5495818efba54ee4e153ec))


### Bug Fixes

* **PhotoplanSelectBgMobile:** rebased from develop and updated version of packages ([82c0f37](https://gitlab.pepita.io/getrix/mls-site/commit/82c0f37413caf19d401af20a2d13fd3345a331ac))
* **crm:** menu new list and performance column ([99e4181](https://gitlab.pepita.io/getrix/mls-site/commit/99e4181b59a21e48da7f3dee6bba849da8b3c945))
* **messaging:** category label switched to tipologyV2 ([0a4e495](https://gitlab.pepita.io/getrix/mls-site/commit/0a4e49511f2c0185fc4a17fabecc27de992cda25))
* **properties-list:** send to action only visible on getrix ([b8358a9](https://gitlab.pepita.io/getrix/mls-site/commit/b8358a9014a01b141accc0f5bf47036739c6fbf9))
* reduced agencies messaging v2 ([d5af86c](https://gitlab.pepita.io/getrix/mls-site/commit/d5af86cd0b91f7dac9c7655fcdb90235845690bb))
* **table:** resize column style ([f0534d3](https://gitlab.pepita.io/getrix/mls-site/commit/f0534d32f4ae3343fd419062da14e3070fc241d4))


### Refactor

* **dropdown-range-input:** Refactor completed ([8934bec](https://gitlab.pepita.io/getrix/mls-site/commit/8934bec316d1d18bcf21d86a78b265bd080083d4))
* **import-quality-modal:** separated fetching data and ui into different components ([fd4c390](https://gitlab.pepita.io/getrix/mls-site/commit/fd4c390fd188e3362636ca41a1b8daedc02cfc68))


### Other

* **contract-administration:** revert of commit 88b82b111cdc931ce90ce8eed95ee8262b08d4a1 ([24a013b](https://gitlab.pepita.io/getrix/mls-site/commit/24a013b952114fda0e0157144f62a24477089d58))
* updated cms translations to 1.7.1 ([924894e](https://gitlab.pepita.io/getrix/mls-site/commit/924894edea6dc93512ccd63e704842762f84314a))
## [6.52.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.51.0...v6.52.0)    (2025-06-19)


### Features

* **new-detail-modal:** showing ad id, removed translation ordering mechanism ([fd783e3](https://gitlab.pepita.io/getrix/mls-site/commit/fd783e34f926e5ef685a262f0b397e98ee879c02))


### Bug Fixes

* **administration-contract:** foreign spaces within the response ([88b82b1](https://gitlab.pepita.io/getrix/mls-site/commit/88b82b111cdc931ce90ce8eed95ee8262b08d4a1))
## [6.51.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.50.0...v6.51.0)    (2025-06-19)


### Features

* **sidebar:** usePersistedSidebar ([a171d58](https://gitlab.pepita.io/getrix/mls-site/commit/a171d582063482681865ecd0b846c896aa00517a))


### Bug Fixes

* added secret ads to active count on dashboard page ([49cae2a](https://gitlab.pepita.io/getrix/mls-site/commit/49cae2a519890de19a450c50fb7760cf9212e81d))
* **list:** sticky header and colum new table ([49d107b](https://gitlab.pepita.io/getrix/mls-site/commit/49d107b84b0fe9c1dd723f0ddf468fbebe9d8131))


### Other

* cleaned up composer scripts ([814f3ee](https://gitlab.pepita.io/getrix/mls-site/commit/814f3eea2cb5834fcdc18753b7cbab2ac7334e39))
* enabled 1270 agencies to new messaging ([4b6e441](https://gitlab.pepita.io/getrix/mls-site/commit/4b6e441a29962766a560b9aa5ba030ee8aafaeda))
## [6.50.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.49.0...v6.50.0)    (2025-06-18)


### Features

* Language of first description is tenant language ([b1d889d](https://gitlab.pepita.io/getrix/mls-site/commit/b1d889de58f5b3f5e5ace13bafce147278250378))
* add hasPhotoPlan property in ExternalMedia ([0ea8ce7](https://gitlab.pepita.io/getrix/mls-site/commit/0ea8ce764b8538b65387a46b9fd8672dda3560f4))
* configurations for new serbia installation ([3fd2e23](https://gitlab.pepita.io/getrix/mls-site/commit/3fd2e23ef41ced91547b291cf31fda5e11c46342))
* feat(detail-ad-modal): new-constructions, gallery image showing parent's images
See merge request getrix/mls-site!3563 ([d30a73a](https://gitlab.pepita.io/getrix/mls-site/commit/d30a73af95a819703a36223c22feb92702f1b89c))
* **growthbook:** add decryptionKey option for prod IT ([9655ad6](https://gitlab.pepita.io/getrix/mls-site/commit/9655ad6a48e944032fd93a5fd3ff27e87f81888c))
* media button for fotoplan ([3e7bad0](https://gitlab.pepita.io/getrix/mls-site/commit/3e7bad0f7af7f3eaaa44d18f04b793a0efb8a5b5))
* **properties-list:** growthbook integration ([4f65410](https://gitlab.pepita.io/getrix/mls-site/commit/4f65410086fa4b9cc7016c96dd9801326b0f88bd))


### Bug Fixes

* Miss Office Interior Subdivision ([f5275f8](https://gitlab.pepita.io/getrix/mls-site/commit/f5275f81b20ef034136539b4f702fede51d369ff))
* Miss Rent Contract Type ([39eef80](https://gitlab.pepita.io/getrix/mls-site/commit/39eef8073aa57f26ef682316594801fa2c999f93))
* **abroad:** inserted correct points ([54638d3](https://gitlab.pepita.io/getrix/mls-site/commit/54638d393256f49e7fca0cb70586264bc4d2329c))
* cast to string for translate content method ([88b7488](https://gitlab.pepita.io/getrix/mls-site/commit/88b74885f05bdadc4f80a63c295daf892719f8ad))
* improve null check on composition ([73a6152](https://gitlab.pepita.io/getrix/mls-site/commit/73a615232c4827b4766d0224a519cf2e7af35482))
* parameters passed to api ([6b1ba41](https://gitlab.pepita.io/getrix/mls-site/commit/6b1ba41bcea31124d90a427785422747d14a157c))
* **portal-properties:** secret status row actions ([52b78b4](https://gitlab.pepita.io/getrix/mls-site/commit/52b78b4d2abacb7331a1d4728c09a90d3945c12f))
* **properties-list:** button explore label ([4841623](https://gitlab.pepita.io/getrix/mls-site/commit/48416237eaa0d7a2d789c18e83656330201167b3))
* **property-data-mapper:** casting translateContent input to string ([3b01c19](https://gitlab.pepita.io/getrix/mls-site/commit/3b01c19bdce1c00da398bba1485f0779b9224402))
* replace PropertyImageFormatter with PropertyPlanFormatter and update configuration accordingly ([0a51268](https://gitlab.pepita.io/getrix/mls-site/commit/0a512685f2cb4cdf649c3614555c338983e38023))
* **secret-property:** fixed at immobiliare.it list ([e1dde8f](https://gitlab.pepita.io/getrix/mls-site/commit/e1dde8fffee54bdbf4d4c1d52bbf1236cf9357f8))
* sort description fails if there isn't for tenant language ([4b04c37](https://gitlab.pepita.io/getrix/mls-site/commit/4b04c37bed15371a116c38b1cdd003f4fe24babb))
* use label instead int for overlooking ([bc26c5b](https://gitlab.pepita.io/getrix/mls-site/commit/bc26c5b87d371108eefa4474c5c67d61af130f8b))


### Refactor

* return cadastral income formatted ([115e72b](https://gitlab.pepita.io/getrix/mls-site/commit/115e72b8e76846a478726c1a2feea63a2906a115))


### Other

* **growthbook:** switch on in prod it ([864ebc8](https://gitlab.pepita.io/getrix/mls-site/commit/864ebc8cbdae2c2e8cf73f03ad38043e2685ab4a))
* rename ActiveProperty to Property across PropertyList components ([7cc5867](https://gitlab.pepita.io/getrix/mls-site/commit/7cc5867758beccdf27b2990e08fb4cf52be49b18))
* typo in staging fotoplan baseurl ([d506345](https://gitlab.pepita.io/getrix/mls-site/commit/d50634530cac73da7ed1593b14d8f744bedfb455))
## [6.49.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.48.4...v6.49.0)    (2025-06-16)


### Features

* add unified properties page and related components ([afd7921](https://gitlab.pepita.io/getrix/mls-site/commit/afd7921ce83ee12e64b7de6cac5b2657e610242b))
* **portal-lists:** new real estate detail modal ([441bd9d](https://gitlab.pepita.io/getrix/mls-site/commit/441bd9d768119763e8de43bfcae382c4730e7b51))
* promote always visible in visibility column ([8889885](https://gitlab.pepita.io/getrix/mls-site/commit/88898859a62d54444274a83c40c943d7b88f0b01))


### Bug Fixes

* **ad-detail-modal:** carousel crash on empty list, warning for missing consistencies ([66593f7](https://gitlab.pepita.io/getrix/mls-site/commit/66593f7f8b4a15bb9f3a2348a1e333a73a93e01e))
* **crm:** sort column list dropdown ([29e42c3](https://gitlab.pepita.io/getrix/mls-site/commit/29e42c332d918dfde5bd246d2589f56d97ef3b61))
* **crm:** various fix tooltip and spacing ([3260159](https://gitlab.pepita.io/getrix/mls-site/commit/32601593ae40ffbf5dfbf738489b383e9c6a449b))
* fix(messaging): change endpoint updateDate
add prop key

See merge request getrix/mls-site!3528 ([8ef48ca](https://gitlab.pepita.io/getrix/mls-site/commit/8ef48cac48611c6895f27b3cedd528ca36785752))
* **growthbook:** changed custom attribute and introduced dev mode enabled env ([b75c4b5](https://gitlab.pepita.io/getrix/mls-site/commit/b75c4b53de688b392680b9e216b74e67ca2ea79e))
* miss proposer and workProgress in brokerage data ([62b9b38](https://gitlab.pepita.io/getrix/mls-site/commit/62b9b38cba89c7d9604727429be4663de4e364f3))
* **properties-list:** translations ([42fc040](https://gitlab.pepita.io/getrix/mls-site/commit/42fc040aa3d3a4676c1105ca86e34591e57d8119))
* **unified-properties:** Separated place column to multiple lines with city, zones and address ([c468a95](https://gitlab.pepita.io/getrix/mls-site/commit/c468a95690bfba9153575e87a9c4d2f9cf04a31c))
* **unified-properties:** various bug fixes ([5ce8926](https://gitlab.pepita.io/getrix/mls-site/commit/5ce8926cb25475b19abee17674271dd256782964))


### Refactor

* add auction-specific data models and data mappers for property handling ([60052c8](https://gitlab.pepita.io/getrix/mls-site/commit/60052c82311917711aa5a8a04dfd157a2bbc4473))


### Documentation

* Add json schema for filter LisListingAdsFiltersRequest ([6fd0066](https://gitlab.pepita.io/getrix/mls-site/commit/6fd0066dffff3b525d9e28bf21cc07c5b950bd1b))


### Other

* docker env typo ([b0e7678](https://gitlab.pepita.io/getrix/mls-site/commit/b0e76788a811980c43439b525fb267858a0cdcc9))
* missing key in test.env ([b716a6f](https://gitlab.pepita.io/getrix/mls-site/commit/b716a6f92c38c9e80dfe58d5d796ad058be14e86))
* property list route ([2f216b9](https://gitlab.pepita.io/getrix/mls-site/commit/2f216b9064bff4fae3cefffd27e3120c76045ea4))
* restored commented sentry webpack plugin ([bab85cb](https://gitlab.pepita.io/getrix/mls-site/commit/bab85cb9135077ed00330aec4d8acd1b0e211202))
## [6.48.4](https://gitlab.pepita.io/getrix/mls-site/compare/v6.48.3...v6.48.4)    (2025-06-11)


### Bug Fixes

* restore some deleted endpoints ([2243f55](https://gitlab.pepita.io/getrix/mls-site/commit/2243f55a7721d33ac2984161672924984d8ba47f))


### Other

* enabled more agencies to messaging v2 ([5d77adb](https://gitlab.pepita.io/getrix/mls-site/commit/5d77adb3adbda86b1b5f5cc6b1a71dfb7512e51a))
* env linting ([3adea77](https://gitlab.pepita.io/getrix/mls-site/commit/3adea775bd4152bf793df18d1239cffd69bea854))
* **fotoplan:** adds baseurls by tenant ([9741028](https://gitlab.pepita.io/getrix/mls-site/commit/9741028644065c039190119e818cab338d91b708))
## [6.48.3](https://gitlab.pepita.io/getrix/mls-site/compare/v6.48.2...v6.48.3)    (2025-06-11)


### Bug Fixes

* **lands:** cast to string input param of translateContent ([d44c7ee](https://gitlab.pepita.io/getrix/mls-site/commit/d44c7eea387de1e507150edcf7febd53d1fb4030))


### Other

* **messaging-v2:** adds enabled agency ([d686a76](https://gitlab.pepita.io/getrix/mls-site/commit/d686a76f03a8e8930a86a9499a67258b870fa469))
## [6.48.2](https://gitlab.pepita.io/getrix/mls-site/compare/v6.48.1...v6.48.2)    (2025-06-11)


### Other

* **gx-navigation:** CurtomerService clean up ([ee7288b](https://gitlab.pepita.io/getrix/mls-site/commit/ee7288b419f004fb4804fa4147e7e2296319e3de))
* **helpjuice:** revert account url to help-crm.immobiliare.it ([15cbd67](https://gitlab.pepita.io/getrix/mls-site/commit/15cbd67aa82928b283b859e2c61ea61b6e370b97))
## [6.48.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.48.0...v6.48.1)    (2025-06-10)


### Bug Fixes

* **helpjuice:** temporary changed url. Avoid hardcoded value ([3acd602](https://gitlab.pepita.io/getrix/mls-site/commit/3acd602bd20c7c05f2f106277ffeebf2a84aa9db))


### Refactor

* **customer-landing-page:** created new react page with functionality ([a86fb98](https://gitlab.pepita.io/getrix/mls-site/commit/a86fb987cb4ff807ccc13eedc4e79d75b95b6a0b))
## [6.48.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.47.0...v6.48.0)    (2025-06-10)


### Features

* new SwiperCarousel component ([36a1864](https://gitlab.pepita.io/getrix/mls-site/commit/36a186409b5fa70fbac30dcc4e575ebcd9497b49))


### Bug Fixes

* add price in child property ([4e9e2e9](https://gitlab.pepita.io/getrix/mls-site/commit/4e9e2e9d30078847c7d4bfbc83c31769c940bd15))
* **crmlist:** style bug FF ([112772d](https://gitlab.pepita.io/getrix/mls-site/commit/112772df385c8dcd7c2ac1c515df9a139bdb55ef))
* enabled more agencies to messaging v2 ([9652312](https://gitlab.pepita.io/getrix/mls-site/commit/9652312d88ed085b22a24443fc5894ba4d6ad5f7))
* miss Land AccessType translation ([36d3a66](https://gitlab.pepita.io/getrix/mls-site/commit/36d3a6670322df933b2b9e01c200f01bd271bf53))
* miss room type translation ([b1135d3](https://gitlab.pepita.io/getrix/mls-site/commit/b1135d3d55c58381c53e15118a233f54210d71e4))
* **properties-list:** avoid to show preview quick action if not expected ([47941da](https://gitlab.pepita.io/getrix/mls-site/commit/47941da28701eb599feccf155b9466270a62d9fe))
* **properties-list:** handling of property type to show proper icon ([b15bccf](https://gitlab.pepita.io/getrix/mls-site/commit/b15bccf60963483b0dea77fbf599fbed3f518ef9))
* use values of balconies and terrace instead boolean response ([058da6f](https://gitlab.pepita.io/getrix/mls-site/commit/058da6f7b2cbc106b11a09151028f11489c9015a))


### Refactor

* add extra information on exception in similar-ads ([d574b44](https://gitlab.pepita.io/getrix/mls-site/commit/d574b4489a7da8557c6ace15b81db254b73a3922))


### Other

* **refactor:** remove unused endpoint ([c353221](https://gitlab.pepita.io/getrix/mls-site/commit/c3532210ecb50b110c256e5690128bb3153a1bd9))
* switched phpunit engine to plain phpunit ([3c44f1f](https://gitlab.pepita.io/getrix/mls-site/commit/3c44f1f577f0ee7910d7caf0cfa0916bda7932ea))

## [6.47.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.46.1...v6.47.0)    (2025-06-05)


### Features

* add agency notes to Ad and AdResponse models ([76ec2e0](https://gitlab.pepita.io/getrix/mls-site/commit/76ec2e042094c2c0d1e49b9628d00021cb62f1cc))


### Bug Fixes

* add 'enclosed' mapping in CompositionDataMapper ([37da4c5](https://gitlab.pepita.io/getrix/mls-site/commit/37da4c5355fd2fb09628234f1ac01efb807c21c6))
* **customer:** add path '/manuali' in endpoint ([7a7b705](https://gitlab.pepita.io/getrix/mls-site/commit/7a7b7058b14442d510acff6c97e004b0851c1f0d))
* **estimates:** set empty array as default data value for empty lookup responses ([b29c1b2](https://gitlab.pepita.io/getrix/mls-site/commit/b29c1b28a2c97c78667fe31472148244a22755b2))
* initialize default values for ListAdsStatsRequest properties ([e6a6c05](https://gitlab.pepita.io/getrix/mls-site/commit/e6a6c051a6467f87a864472d1ce57d10d5f85756))
* **properties-list:** add link on add button within sidebars ([38a6a67](https://gitlab.pepita.io/getrix/mls-site/commit/38a6a67c626f8042e9a3c615432afd48e2e7dfb5))


### Refactor

* **ranking-detail-modal:** refactor completed ([4fb3c9e](https://gitlab.pepita.io/getrix/mls-site/commit/4fb3c9e9486466452d7b2e396e90fef7da4ecc1a))


### Other

* add flag to stats/ads/matches fpr agencies with new messaging system ([7d5bfe4](https://gitlab.pepita.io/getrix/mls-site/commit/7d5bfe488859a5f35acfcd2801854e22a467faed))
* **auctions-catalogue:** Removed old folder ([01e9288](https://gitlab.pepita.io/getrix/mls-site/commit/01e92881b0c7807e1d894bc644e2024cf06c6f4e))
* **growthbook:** adds dynamic api host and IT prod client key ([2b0fa31](https://gitlab.pepita.io/getrix/mls-site/commit/2b0fa31556409efe9572b48106e79bcec8377632))
* **messaging-v2:** adds other enabled agencies ([725f27d](https://gitlab.pepita.io/getrix/mls-site/commit/725f27d3ed9540b6ff791d73e1563445a14da915))
* **properties-list:** add temporary toggle ([7772393](https://gitlab.pepita.io/getrix/mls-site/commit/7772393af764fdbd5305c3957d9be77303c37d27))
* property list route ([cb27695](https://gitlab.pepita.io/getrix/mls-site/commit/cb276957daae71c224b786dddd388656ddeaaca5))
* psalm error ([db1ff5c](https://gitlab.pepita.io/getrix/mls-site/commit/db1ff5c0d163fb411a27174a33bbb19b90260822))
## [6.46.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.46.0...v6.46.1)    (2025-05-29)


### Other

* enable API MFA Send Pin ([f752897](https://gitlab.pepita.io/getrix/mls-site/commit/f752897583691f1e4fe6cc9f323910d95c156934))
## [6.46.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.45.2...v6.46.0)    (2025-05-28)


### Features

* add descriptions in property detail ([4721ab1](https://gitlab.pepita.io/getrix/mls-site/commit/4721ab17158d583da1a5d0a4545f01a1665106c5))
* add property visbility secret filter ([0a89c88](https://gitlab.pepita.io/getrix/mls-site/commit/0a89c885574dc1145d77b68172aabdccb41556e9))
* **sidebar:** Sidebars completed ([962d4f1](https://gitlab.pepita.io/getrix/mls-site/commit/962d4f19d75591da88d64484ff8085b3d306665b))


### Bug Fixes

* **auctions-catalogue:** post request tos ([3e6bbfa](https://gitlab.pepita.io/getrix/mls-site/commit/3e6bbfaa1ee31a9ad577c60a6b91190c15c631b3))
* **dashboard:** typo ([5311e5e](https://gitlab.pepita.io/getrix/mls-site/commit/5311e5ec633eb4c0bed5cf93a8bac314db5133b4))
* initialized  as empty array ([9352b05](https://gitlab.pepita.io/getrix/mls-site/commit/9352b05d666a804a8c110f5a419ccd7769c27ca5))


### Test

* Add phpunit and our skeleton ([8b49e79](https://gitlab.pepita.io/getrix/mls-site/commit/8b49e79fc381e622ed01c879251f87301d06c7e0))


### Other

* **GtxApp:** more generic product name in comments ([b8f056f](https://gitlab.pepita.io/getrix/mls-site/commit/b8f056f7af1d0eae11f838629408f9333d6a46aa))
* add trace for clear cookie action ([cdd52aa](https://gitlab.pepita.io/getrix/mls-site/commit/cdd52aa386b9241ab3f85f43d6ff7ffbcdf5d829))
## [6.45.2](https://gitlab.pepita.io/getrix/mls-site/compare/v6.45.1...v6.45.2)    (2025-05-21)


### Other

* **login:** dea jwt token password for prod ([9905356](https://gitlab.pepita.io/getrix/mls-site/commit/9905356726edfde068063aa8d85bc1f997034382))
## [6.45.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.45.0...v6.45.1)    (2025-05-21)


### Other

* new geography tag ([da76a4c](https://gitlab.pepita.io/getrix/mls-site/commit/da76a4c9646e4c0498e7609cf7555aa36e94a181))
## [6.45.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.44.0...v6.45.0)    (2025-05-21)


### Features

* add consistence id in property detail ([a7a1a3d](https://gitlab.pepita.io/getrix/mls-site/commit/a7a1a3d4eceb46ca367e6a6e36fa80bf66d8c1b9))
* geocoder uri change ([0f6cd11](https://gitlab.pepita.io/getrix/mls-site/commit/0f6cd11368b0897b9b0790ed8cbca4677ad16377))
* **matches:** add secret badge in property list cell ([fe01810](https://gitlab.pepita.io/getrix/mls-site/commit/fe01810f95be2ed8c775fe6af0fe8c4a6b66bcdf))
* **portal-list:** add visibility secret badge ([3c2680f](https://gitlab.pepita.io/getrix/mls-site/commit/3c2680f673dbaed0ab7213110599348ae025cff1))


### Bug Fixes

* temporarily excluded secret property from dashboard ([b2287d7](https://gitlab.pepita.io/getrix/mls-site/commit/b2287d71deef116044828e63618a6be871296c16))
* **user-tracking:** added FF to webpush tracking check ([c5b6e21](https://gitlab.pepita.io/getrix/mls-site/commit/c5b6e213f4273507af53b7132423c5deef25b55f))


### Other

* **ci:** updated php image used ([cd11b41](https://gitlab.pepita.io/getrix/mls-site/commit/cd11b416dd0c0b3e500638ccbd023fbb3bfa4895))
* update cms-contents to 1.12.2 ([02b45f4](https://gitlab.pepita.io/getrix/mls-site/commit/02b45f413b7264974f3e53f5ccd03a45191938a7))

## [6.44.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.43.0...v6.44.0)    (2025-05-19)


### Features

* add contactId and socialsThreadId to getThread (new messaging) ([42ddbc8](https://gitlab.pepita.io/getrix/mls-site/commit/42ddbc8a2f118c3e1e4ad4beab2089ab10374a88))


### Bug Fixes

* **login:** wrong template for client app login no phone ([b9af136](https://gitlab.pepita.io/getrix/mls-site/commit/b9af1360a1711b5b717d961fb08361b4e5d86c4d))
* **performance:** your property sticky ([eb2bd46](https://gitlab.pepita.io/getrix/mls-site/commit/eb2bd46f781a155df96f905ebf06fe1a59714acf))


### Refactor

* AbstractApiGtxDataProvider.php and inheritors and timeout ([7e9118f](https://gitlab.pepita.io/getrix/mls-site/commit/7e9118fde72a0e9ef727df26f086852606fdf50e))


### Other

* **msw:** Removed unnecessary extra logs ([449d4b4](https://gitlab.pepita.io/getrix/mls-site/commit/449d4b493d98c83840714af9ee2b8abb89c7307a))

## [6.43.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.42.1...v6.43.0)    (2025-05-19)


### Features

* introduce new table for unified list ([3b065dc](https://gitlab.pepita.io/getrix/mls-site/commit/3b065dcdbc62349f37576638e945eaa921847409))
* **login:** new client app login ([97872cb](https://gitlab.pepita.io/getrix/mls-site/commit/97872cb5e6e8eb3d54cf9e2dd1fd5ebda59fd761))
* **menu-counters:** add max retries env variable ([fe85da4](https://gitlab.pepita.io/getrix/mls-site/commit/fe85da4810b5889d9bb77efcafc9ac505499416c))
* messaging - getThreadId by socialsThreadId ([7d423b4](https://gitlab.pepita.io/getrix/mls-site/commit/7d423b40dd01136b38bbed27a58fb77492b5b6d1))
* **scheduled-visits:** new list and filters to change real estate for a scheduled visit ([7a88152](https://gitlab.pepita.io/getrix/mls-site/commit/7a88152fb57d7ceab1d59917865721b81d2451b4))


### Bug Fixes

* added listeners for billboard page mobile header buttons ([6ea5f6a](https://gitlab.pepita.io/getrix/mls-site/commit/6ea5f6a5175e4cf184dca520367be89bc5f53f66))
* removed saveBillboard action from billboard header buttons ([cbf5dc7](https://gitlab.pepita.io/getrix/mls-site/commit/cbf5dc71b1999e9bab8f71134b248ae8ba5b5e63))


### Refactor

* **auctions-catalogue:** Refactor complete ([de5b043](https://gitlab.pepita.io/getrix/mls-site/commit/de5b043083e856b3f5cbb1f280a3f0a90075fdbc))


### Other

* **deps:** bump getrix/common to 7.49.0 ([1c05a59](https://gitlab.pepita.io/getrix/mls-site/commit/1c05a59393c94bf585444446ea20378febd801ae))
* **gx-design:** update components react ([65ffb5d](https://gitlab.pepita.io/getrix/mls-site/commit/65ffb5d15f696e4f56e7ee9820b9db856b5ca99e))
* update IT dev/staging geography tag to R6 version ([b3463f1](https://gitlab.pepita.io/getrix/mls-site/commit/b3463f1deda4c978307fd17ddc89ef12093f0172))
## [6.42.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.42.0...v6.42.1)    (2025-05-08)


### Bug Fixes

* adjust error field scroll to account for header height ([1de6d96](https://gitlab.pepita.io/getrix/mls-site/commit/1de6d965e01783f6dff023d5730192ddad919489))
## [6.42.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.41.2...v6.42.0)    (2025-05-05)


### Features

* **gx-navigation:** add badge and switch counters to notificationBadge ([2bcfdae](https://gitlab.pepita.io/getrix/mls-site/commit/2bcfdaeb4111a960b721fda8eb5609042525e8f2))
## [6.41.2](https://gitlab.pepita.io/getrix/mls-site/compare/v6.41.1...v6.41.2)    (2025-04-24)

## [6.41.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.41.0...v6.41.1)    (2025-04-23)


### Bug Fixes

* **old-messagging:** missing AD_PORTAL placeholder value ([88a474a](https://gitlab.pepita.io/getrix/mls-site/commit/88a474ad7b6acd1364b539e1cffae401257041b7))
## [6.41.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.40.1...v6.41.0)    (2025-04-16)


### Features

* **lookup:** implements localization for lookup results ([e10c7e6](https://gitlab.pepita.io/getrix/mls-site/commit/e10c7e66519f70bee53a3899be1180a181846173))


### Bug Fixes

* **users:** add condition on send activation email action ([f7cbbd8](https://gitlab.pepita.io/getrix/mls-site/commit/f7cbbd896f50fc87d7b8f700fe9aa98644b4b7a0))
* **youdomus:** removes duplicated script tag includes ([f86cab2](https://gitlab.pepita.io/getrix/mls-site/commit/f86cab21102826e2da306d421b0f74523525a6df))


### Other

* adds ignoreErrors array to sentry configs ([85e7a91](https://gitlab.pepita.io/getrix/mls-site/commit/85e7a91d7ca3929712936f7f43c6a59db459bc7e))
* updated event trace label ([19a92a0](https://gitlab.pepita.io/getrix/mls-site/commit/19a92a092231df8dc1511d0bd9b98e9fdea6faee))
## [6.40.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.40.0...v6.40.1)    (2025-04-14)


### Other

* **menu:** remove hidden property from add actions in menu configuration ([57349ff](https://gitlab.pepita.io/getrix/mls-site/commit/57349ff20d199572300cd2b54d3c243efbd72bf0))
## [6.40.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.39.1...v6.40.0)    (2025-04-14)


### Features

* **env:** enable ADD_PROPERTY in al HR environment files ([bb4b6ac](https://gitlab.pepita.io/getrix/mls-site/commit/bb4b6ac25514eec2f428c09c9d24bd66d63a1cfa))
## [6.39.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.39.0...v6.39.1)    (2025-04-11)


### Bug Fixes

* **billboard:** missing translation ([1120613](https://gitlab.pepita.io/getrix/mls-site/commit/1120613ddcb06a8e065ad882328bcda60e8fc9b6))
* **sso:** used bo source for products with internal role ([dfc5ffc](https://gitlab.pepita.io/getrix/mls-site/commit/dfc5ffcec4b2bc40e9e758f92dd7fb3a287034d9))


### Other

* **TraceAccess:** retrieve agent by email in recovery password ([7da0d81](https://gitlab.pepita.io/getrix/mls-site/commit/7da0d8139547e69b5f83de3adcba282901709c2e))
* activate new messaging on agency with id 2867 ([7575afc](https://gitlab.pepita.io/getrix/mls-site/commit/7575afc5d0b9014741826d6313c6b80361d23184))
* check if agent and agency ids are positive int before contact api ([8618c09](https://gitlab.pepita.io/getrix/mls-site/commit/8618c097bc585327a6f451bfe973c1eb6b3fbccf))
* **ci:** allowed php security task to fail ([2823a2d](https://gitlab.pepita.io/getrix/mls-site/commit/2823a2d33002cb64261bc567a91377f219052d93))
* **react:** upgrade mls and gx-navigation to 18.3.1 ([a44ac21](https://gitlab.pepita.io/getrix/mls-site/commit/a44ac21b61856b19b84b2d121c1b696cf1531ff5))
* translate gitlab templates ([33d1ca8](https://gitlab.pepita.io/getrix/mls-site/commit/33d1ca8dc7de421c3719498426489dea2ffbadea))
* update cms translations ([36a4aa9](https://gitlab.pepita.io/getrix/mls-site/commit/36a4aa900abfb94cb75b63d41315e9c08a3a6d03))

## [6.39.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.38.0...v6.39.0)    (2025-04-07)


### Features

* new component YearPickerInput ([3517854](https://gitlab.pepita.io/getrix/mls-site/commit/35178544aa0ab152baf7cc1e66651ac4f96e49c1))
* **report-insights:** mixpanel area access event ([2ba4f3e](https://gitlab.pepita.io/getrix/mls-site/commit/2ba4f3e0f62ab1b8b42eb2566b618fb0c7a90ad9))
## [6.38.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.37.1...v6.38.0)    (2025-04-07)


### Features

* **agency:** include tenant language description in multilanguageDescriptions API update ([fc7d527](https://gitlab.pepita.io/getrix/mls-site/commit/fc7d527836cf4ea274653b2aacdbec3ad2f755ea))
* **report-insights:** iframe error handler ([a2b039f](https://gitlab.pepita.io/getrix/mls-site/commit/a2b039f8da2e61fcc432051029c852280f223864))
* **report-insights:** new badge ([e61c0cf](https://gitlab.pepita.io/getrix/mls-site/commit/e61c0cf5ddf79c2383cac0511d4aa127d27b0584))
* **stats:** updated ekbl_stats namespace for the Croatia ([18b3e00](https://gitlab.pepita.io/getrix/mls-site/commit/18b3e00e590b36fbd48c7a6126833254aeff909d))


### Bug Fixes

* **billboard:** wait creation of billboard before edtiting ([1a7e403](https://gitlab.pepita.io/getrix/mls-site/commit/1a7e4031012b9c951c8d4736bf74729971fe4ca6))
* **gx-navigation:** isAdmin prop value ([d9f31fc](https://gitlab.pepita.io/getrix/mls-site/commit/d9f31fc39fea1f331be7d551d3c0ece87705897d))
* **report-insights:** badge new ([d6ca68f](https://gitlab.pepita.io/getrix/mls-site/commit/d6ca68fdd3913c4fd6beefb5f51b777f5620d8d0))


### Other

* add agency ID on MixPanel event trace ([f4bff35](https://gitlab.pepita.io/getrix/mls-site/commit/f4bff35744b49b0641a431c8a4cfa582f50a8f44))
* **docs:** updated deploy.md ([80f1545](https://gitlab.pepita.io/getrix/mls-site/commit/80f1545a17ce84c722b1e419b3def49851151d89))
* fix conflict package creation with backstage registration ([b976ae3](https://gitlab.pepita.io/getrix/mls-site/commit/b976ae3939225b0eb4cb5c637447467b12df7063))
## [6.37.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.37.0...v6.37.1)    (2025-04-01)


### Bug Fixes

* **reports:** report insights use dea internal vip in production ([7d5580b](https://gitlab.pepita.io/getrix/mls-site/commit/7d5580b9309f33f7738e39c13cd5e50e6168ec6b))
## [6.37.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.36.0...v6.37.0)    (2025-04-01)


### Features

* **property:** add fields for the Croatia in property details ([5cbd0ed](https://gitlab.pepita.io/getrix/mls-site/commit/5cbd0ed936fae66b4bdb7347b809c75a2664fefa))


### Bug Fixes

* **photoplan:** remove button color ([e9f8160](https://gitlab.pepita.io/getrix/mls-site/commit/e9f8160a4613ab559f7147034a3e316cefb3377a))


### Other

* **reports:** new endpoint for report insights iframe ([c55ba9b](https://gitlab.pepita.io/getrix/mls-site/commit/c55ba9bf5202dd317102e649325227065e616cf0))
* **reports:** use https instead than http in prod ([1f1ccf7](https://gitlab.pepita.io/getrix/mls-site/commit/1f1ccf7caf8c9d5add19e3144fe18776059012c7))
## [6.36.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.35.0...v6.36.0)    (2025-04-01)


### Features

* **FeatureSection:** added a new section with static component and new scss ([d97a245](https://gitlab.pepita.io/getrix/mls-site/commit/d97a24514b5b425db8d9c6e40d812d4166239cfd))
* add property ft flag ([631248c](https://gitlab.pepita.io/getrix/mls-site/commit/631248c48dcac1b3ffdadf5240f0744a4e86198d))
* add property ft flag ([f056bd7](https://gitlab.pepita.io/getrix/mls-site/commit/f056bd7a9258f4874473eb5e48fc68a3f08222fb))
* first page tag ([6d49abe](https://gitlab.pepita.io/getrix/mls-site/commit/6d49abed7b5d473a7a848a16efe0d198cfa967a4))
* **first-page:** Show cockade only when position 1 ([a0680f4](https://gitlab.pepita.io/getrix/mls-site/commit/a0680f4fa6ff0032a21d26cb1a79343ea3b7ec60))


### Bug Fixes

* **acquisition-estimates:** reset page to 1 when resetting filters ([fcf661d](https://gitlab.pepita.io/getrix/mls-site/commit/fcf661d63d5c217af7804299ebb335420588cb37))


### Other

* **dashboard:** handle header action responsivness ([7da29c8](https://gitlab.pepita.io/getrix/mls-site/commit/7da29c89042fe72c500cb5e8da118942227fcfa6))
* readme project description ([d72a31a](https://gitlab.pepita.io/getrix/mls-site/commit/d72a31a8a264c933e2578d20e6a9d2044ef48fbe))
* **report-insights:** enable new report insights in PROD ([d02498d](https://gitlab.pepita.io/getrix/mls-site/commit/d02498dd97aa5bde9c2d9efc723ff8feb9cdf307))
* **searches:** rename new agency notification settings endpoint ([19a63e6](https://gitlab.pepita.io/getrix/mls-site/commit/19a63e63f91a49cf8b0e8898753ab1f0ad19358e))

## [6.35.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.34.0...v6.35.0)    (2025-03-31)


### Features

* **settings:** translated countries list in headquarter page ([9246c1d](https://gitlab.pepita.io/getrix/mls-site/commit/9246c1d06daa400285fcd2df69ba4a2a2a4f711b))


### Bug Fixes

* ad (property) on getThread and listThread ([15777ac](https://gitlab.pepita.io/getrix/mls-site/commit/15777acc0195fd1fcce92c7b7295579ad54f0e5a))
* add event name in message payload ([d3250be](https://gitlab.pepita.io/getrix/mls-site/commit/d3250be3696dc4f5607282cac791655cd5a1befb))


### Other

* edits media urls for docker it envs ([e943c01](https://gitlab.pepita.io/getrix/mls-site/commit/e943c014824ecf95f5e2ab552c76cb61cfba47c7))
## [6.34.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.33.0...v6.34.0)    (2025-03-28)


### Features

* **metrics:** update METRICS_SITE configuration across environments ([50b847c](https://gitlab.pepita.io/getrix/mls-site/commit/50b847c08da16a741a81da4a775ad6e9755e1017))
* remove dependencies from gx-navigation ([923a7f2](https://gitlab.pepita.io/getrix/mls-site/commit/923a7f223e9d2b5c1055dfba9c879c43aa920ec8))


### Bug Fixes

* messaging_v2_agency_enabled polulate env value ([22511b0](https://gitlab.pepita.io/getrix/mls-site/commit/22511b0b61dac344a1bec8e854fd0d8951a69fab))
* retrieve Agent Uuid if not already in traceAccess (if possible) ([59fe4cb](https://gitlab.pepita.io/getrix/mls-site/commit/59fe4cbf12573da397335dc1f7e144dc10f0d826))
* update profile URL constant in gx-navigation template ([e89253a](https://gitlab.pepita.io/getrix/mls-site/commit/e89253a41b29dce60970b31eecd17909a9b815aa))


### Refactor

* **email:** complete profile ([708e2d4](https://gitlab.pepita.io/getrix/mls-site/commit/708e2d4ff3a635b39ff3e2e0eec4ff17d11ce9af))


### Other

* adds hr tenant to docker configs ([dd2bbdc](https://gitlab.pepita.io/getrix/mls-site/commit/dd2bbdc7e818f80c43212ba7ea5f531dc01049e8))
* cms and cms-contents bump ([457b44a](https://gitlab.pepita.io/getrix/mls-site/commit/457b44a078d35a60124ce9272d57913d467e73f1))
* **gx-navigation:** typo in twig object ([1c142ee](https://gitlab.pepita.io/getrix/mls-site/commit/1c142ee84b63a9a87042bd3dcfd1bfac0d691a76))

## [6.33.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.32.0...v6.33.0)    (2025-03-27)


### Features

* generate event in trace case ([cfb54f3](https://gitlab.pepita.io/getrix/mls-site/commit/cfb54f3242262ee850734510ff1148e8c35f195d))


### Bug Fixes

* **acquisition-sales:** minor pagination and loading overlay fixes ([70ee65a](https://gitlab.pepita.io/getrix/mls-site/commit/70ee65a3df236db403ab5cef895e0bd3bb53f7ce))
* **envs:** add missing environment parameters value ([e94147c](https://gitlab.pepita.io/getrix/mls-site/commit/e94147c9126465a2120e07918f940eb8373b71d9))
* **menu:** highlight ads ad-insert into menu ([fbd1520](https://gitlab.pepita.io/getrix/mls-site/commit/fbd15203f03454e3cfecb1bba89ba7103b1c1e59))
* premium visibility name from param ([f913c55](https://gitlab.pepita.io/getrix/mls-site/commit/f913c55583059f040d2ac8d858f4f72cb25c7e6b))
* **report-insights:** commercial amp should be raw ([058116c](https://gitlab.pepita.io/getrix/mls-site/commit/058116c6b826b1424c45483e070d783b034e95d2))
* **report-insights:** make all get actions available and just check for post put and delete ([83ab5a2](https://gitlab.pepita.io/getrix/mls-site/commit/83ab5a2900867554fbf6d9b3c6288048cd665e86))
* show province name instead of id in new constructions ([dc9f0cb](https://gitlab.pepita.io/getrix/mls-site/commit/dc9f0cbc460268752bee04b52ea103962bc0fef7))


### Other

* **report-insights:** handle dea api exception and set request timeouts ([c5cc829](https://gitlab.pepita.io/getrix/mls-site/commit/c5cc8297f25c560abb39823f7e78f64fcd63ac99))
* **report-insights:** update dea api base url ([f3e3b28](https://gitlab.pepita.io/getrix/mls-site/commit/f3e3b28231815dd0880d0539a9b8155747451d1d))

## [6.32.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.31.0...v6.32.0)    (2025-03-17)


### Features

* Hide cadastral data ([9fddb9d](https://gitlab.pepita.io/getrix/mls-site/commit/9fddb9d528e7dd52fcd2afb27f69f02b3dce4bd4))
* **ad-performance:** set up ad performance parameters for the Croatia (HR) ([6b6c5f4](https://gitlab.pepita.io/getrix/mls-site/commit/6b6c5f40d306b2c421553d78c7fc3346389cb8c5))
* **menu:** add featureToggle feature for contract menu item ([7f1e090](https://gitlab.pepita.io/getrix/mls-site/commit/7f1e0909e76a766a380fa44ca81e32d98357939c))
* **menu:** disable menu counter in Croatia (HR) ([c7fdaec](https://gitlab.pepita.io/getrix/mls-site/commit/c7fdaecb46e67723a28ab167919c7d5dd07943ee))
* **menu:** disabled agency ad list for the Croatia (HR) ([e0d01a3](https://gitlab.pepita.io/getrix/mls-site/commit/e0d01a3c8a677f41622ca2bb082a4432df16bf48))
* new messaging ([80c7ce9](https://gitlab.pepita.io/getrix/mls-site/commit/80c7ce923209b55155fb9c7ef0de5da73c330cdd))
* **new-costructions:** use province name instead of province id ([13dc568](https://gitlab.pepita.io/getrix/mls-site/commit/13dc568388dbe4f7600335cdfa49f56b43a93c9d))
* report insights dea integration ([ea5b9d9](https://gitlab.pepita.io/getrix/mls-site/commit/ea5b9d90e8e2e9305ac9d92e6030aa26e903af63))
* **report-insights:** remove sub elements from report menu ([3ba5743](https://gitlab.pepita.io/getrix/mls-site/commit/3ba5743c0cb73cd88e34a809a5aaaa6f5f7a8be1))


### Bug Fixes

* **geocoder:** corrected wrong geocoder consumerKey for Croatia ([cee2c45](https://gitlab.pepita.io/getrix/mls-site/commit/cee2c459e0c9b44febe9bfbf715d6a6872447cab))
* **login:** ad portal links from params ([b189f0b](https://gitlab.pepita.io/getrix/mls-site/commit/b189f0b5f666ee87e58359426153bddf0cb6bfc1))
* **photoplan:** update ([f481ecd](https://gitlab.pepita.io/getrix/mls-site/commit/f481ecd89bf1f6fdd0ff793b4b7db3143bb65e45))
* **report-insights:** hiding mls footer margin + missing site_section ([97051c1](https://gitlab.pepita.io/getrix/mls-site/commit/97051c1191a146aad396cb32ec928cd72d7890b7))
* **report-pro:** fix rooms field on similar-properties ([fda6dee](https://gitlab.pepita.io/getrix/mls-site/commit/fda6deebac6eb767f8852a1ea5f803e4d06ffcf3))
* wrong memcache host in prod Croatia ([fb6d589](https://gitlab.pepita.io/getrix/mls-site/commit/fb6d589af3da38f6b2c7432465a4910f6cfba76a))


### Other

* **DEV-17100:** disable access trace in prod ([59f9b8b](https://gitlab.pepita.io/getrix/mls-site/commit/59f9b8b5140e0cd4049cbd6a9e3699ecdb4eab6c))
* **ci:** added additional pipeline after deploy ([6b92609](https://gitlab.pepita.io/getrix/mls-site/commit/6b92609481f6ebcb25dd2f34dff99ad0752a70df))
* **common:** update countries logo ([7f317bb](https://gitlab.pepita.io/getrix/mls-site/commit/7f317bbeb217e0d8930e3381724b471e81f39c9c))
* **deps:** nugget/motion resolution ([ecbef7e](https://gitlab.pepita.io/getrix/mls-site/commit/ecbef7e59bdb4786d915ed3c31b0a9cc4839d08c))

## [6.31.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.30.0...v6.31.0)    (2025-03-10)


### Features

* PROPERTY_LIST_MATCH_COLUMN env to 0 for HR ([36e639a](https://gitlab.pepita.io/getrix/mls-site/commit/36e639a75b5a0e655c9f12ff6dfd4143fc99c936))
* enable old ask help in staging ([5545ba5](https://gitlab.pepita.io/getrix/mls-site/commit/5545ba5a595f11aad914b4c8f2da37389f16a6b4))
* **parameter:** set up parameters for the Croatia ([d874716](https://gitlab.pepita.io/getrix/mls-site/commit/d874716b9f280275417a2b65f74ad5eec5a5f500))
* **parameters:** set up time zone for the Croatia ([e1dce23](https://gitlab.pepita.io/getrix/mls-site/commit/e1dce232dbfff55e116d54cb0dc86e6783a7b055))
* **report-insights:** hiding actions on report detail if user hasReportInsights ([0e52527](https://gitlab.pepita.io/getrix/mls-site/commit/0e52527958e42ba50507b5913a91eee9f6dd91dc))


### Bug Fixes

* add missed localization of the generic error page ([cf57053](https://gitlab.pepita.io/getrix/mls-site/commit/cf570532f3b7b462fbb2239b88569b9ef78cf3d2))
* fixed public page metadata and portal listing preview url ([7e4ef32](https://gitlab.pepita.io/getrix/mls-site/commit/7e4ef320a9d350355eb887d0731a79fa0cd1bfd7))
* **gx-navigation:** remove redundant id from MenuItem component ([236d887](https://gitlab.pepita.io/getrix/mls-site/commit/236d887a43f2e446f545fe8d093a745f687361eb))
* **hr:** contract logo ([8aff6c6](https://gitlab.pepita.io/getrix/mls-site/commit/8aff6c62941309164bb7d0383077e65cad4ab388))
* **hr:** favicon parameters ([881e5dd](https://gitlab.pepita.io/getrix/mls-site/commit/881e5ddd92a4954d2439a10368f55f21989cdc8b))
* **hr:** parameters nekretnine logo ([13f8da9](https://gitlab.pepita.io/getrix/mls-site/commit/13f8da9a1db7031b7cbe71edb5b99f79cbb7e2cb))
* **hr:** update common gx-navigation ([b5ef08a](https://gitlab.pepita.io/getrix/mls-site/commit/b5ef08afaa2edce85536cc04fcb058b432d6d5b8))
* **report-insights:** enable footer ([ebdb760](https://gitlab.pepita.io/getrix/mls-site/commit/ebdb7603b8c7747ddf38b8b46939d747fd78df98))
* reviewed somo HR env configurations ([5e4c107](https://gitlab.pepita.io/getrix/mls-site/commit/5e4c107bfb3946afab25ee29499e1edf164b5092))


### Refactor

* **useOnClickOutside:** replaced hook with the version from gx-design ([44b2ad0](https://gitlab.pepita.io/getrix/mls-site/commit/44b2ad0ae7bbcd8f7f79508704f128b1182fa70b))


### Other

* **TraceAccess:** define constants to identify trace actions ([19414da](https://gitlab.pepita.io/getrix/mls-site/commit/19414dabef630ee6bf0ab43ad3a10836f94093e0))
* **configuration:** rename searches notification classes ([0fe0227](https://gitlab.pepita.io/getrix/mls-site/commit/0fe0227af683a96939abcf6f4245cc992c389abf))
* fix hosts deploy ([4c463df](https://gitlab.pepita.io/getrix/mls-site/commit/4c463df9a908cea63761935ed8d595fe6a880eba))
* indomio-translations/cms bump ([5c08f51](https://gitlab.pepita.io/getrix/mls-site/commit/5c08f515abff104a1b576f93337f390942621f38))
* package.json ([f60cabc](https://gitlab.pepita.io/getrix/mls-site/commit/f60cabceeae825c1d37fb7effc55f884257cabaf))

## [6.30.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.29.1...v6.30.0)    (2025-03-04)


### Features

* added useFormPrompt for notification-settings modal ([fd51c24](https://gitlab.pepita.io/getrix/mls-site/commit/fd51c2477e9b76f781d4d27567c0f2b0df2e7563))
* **hr:** nekretnine logo ([a8e398a](https://gitlab.pepita.io/getrix/mls-site/commit/a8e398afc1de5c330548beaef6879de09b1764d8))
* **signup:** hiding vat field for hr ([4f39450](https://gitlab.pepita.io/getrix/mls-site/commit/4f39450d829972048f7209c77d6d10b7ed1e04a8))
* **wallet:** now can disable digital wallet for some tenants (disabled in Croatia) ([e27cfa2](https://gitlab.pepita.io/getrix/mls-site/commit/e27cfa23e4f279627086b9e26c6b6802d1191360))


### Bug Fixes

* enable property_ads feature toggle for hr ([36fbeb9](https://gitlab.pepita.io/getrix/mls-site/commit/36fbeb9f016731e14464d6569cc8c4acde7cb0f0))


### Other

* **braze:** removed no more window injection ([4671552](https://gitlab.pepita.io/getrix/mls-site/commit/4671552c716a7c082e9e35ea5d8e3b8c821cdea3))
* **youdomus:** removed console.log ([9b72173](https://gitlab.pepita.io/getrix/mls-site/commit/9b72173b1c5f673ac8fd3cf8f4670a9fe8969c7d))

## [6.29.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.29.0...v6.29.1)    (2025-03-03)


### Bug Fixes

* **sales-request:** check items value before footer print ([cdef0a3](https://gitlab.pepita.io/getrix/mls-site/commit/cdef0a33c84e34bc05f181f6181a79da798ffa1e))
## [6.29.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.28.1...v6.29.0)    (2025-03-03)


### Features

* **AccessTrace:** add headers of request ([cb88262](https://gitlab.pepita.io/getrix/mls-site/commit/cb88262501f56ba3e80daf17891c0ecc14815b9b))
* **Security:** use separate email address for security related actions ([cc1e24a](https://gitlab.pepita.io/getrix/mls-site/commit/cc1e24a7337a9dacd05ad68e264a70927a031f9f))
* **config:** replace service customer telephone number for Croatia (HR) ([fbd532a](https://gitlab.pepita.io/getrix/mls-site/commit/fbd532ae3c68e10a4a6e01f85bd418b6d906d6ba))
* **dashboard:** allow disabling ad stats categories for tenants ([8575df9](https://gitlab.pepita.io/getrix/mls-site/commit/8575df961d8f16f9f285f9ab781739399de4b3bd))


### Bug Fixes

* **config:** wrong default_lang_code for the Croatia (HR) ([1508257](https://gitlab.pepita.io/getrix/mls-site/commit/15082576760e9350c0cb3ce4ae16a67891564ebf))
* **report-insights:** fixes ([3e29cc4](https://gitlab.pepita.io/getrix/mls-site/commit/3e29cc4e35acfc245400c98cc237e415a8cf9189))
* **sales-requests:** check variable fix ([2414f99](https://gitlab.pepita.io/getrix/mls-site/commit/2414f994017bb333848d5412da484b7e2a5b8cd8))


### Other

* **hr:** update translations ([ad1a3e4](https://gitlab.pepita.io/getrix/mls-site/commit/ad1a3e423b904b5d56dd6898461067048509c50e))
* **report-insights:** added endpoint to check if agency has report insights ([d3545bb](https://gitlab.pepita.io/getrix/mls-site/commit/d3545bb69c804f9bd9586d44fea6274e5b6b362e))
* **report-insights:** use getrix version to manage new report insights ([bf7b667](https://gitlab.pepita.io/getrix/mls-site/commit/bf7b66716d01fa2b45f9558c034786915f088369))
## [6.28.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.28.0...v6.28.1)    (2025-02-25)


### Features

* feat report pro iframe new section

See merge request getrix/mls-site!3271 ([a7d76c1](https://gitlab.pepita.io/getrix/mls-site/commit/a7d76c19464329a67b4a515c9a825fece2da9f75))


### Bug Fixes

* Referrer in same condition can have a null value ([657a215](https://gitlab.pepita.io/getrix/mls-site/commit/657a215191b746ef802ac9d38490b526e1a9858a))
* **auction-catalogue:** normalizes document name ([56bbe6f](https://gitlab.pepita.io/getrix/mls-site/commit/56bbe6f7c8d59d88ba013c19288294cc846486f9))


### Other

* **braze:** temporary inject braze into window object ([ee9e636](https://gitlab.pepita.io/getrix/mls-site/commit/ee9e636e6a433e97ece73b94755b241d0922eb32))
* **docs:** backstage ([2603b3f](https://gitlab.pepita.io/getrix/mls-site/commit/2603b3fed232d5129b488e9bb90d391ed7bdf251))
* removed old billboard folder ([692f0f4](https://gitlab.pepita.io/getrix/mls-site/commit/692f0f4b94455ac514cbc49777c23fcadd4a904b))
* **report:** enable report insights agencies in staging ([5a09d70](https://gitlab.pepita.io/getrix/mls-site/commit/5a09d700c56d922503baf4091b79a2ccd26d719e))
* **typologyenums:** added enums for better typing and readability ([a00ed9c](https://gitlab.pepita.io/getrix/mls-site/commit/a00ed9c43256edccd8604d9250716dfc7c2cd224))

## [6.28.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.27.2...v6.28.0)    (2025-02-21)


### Features

* **agency:** get agency description languages from getrix api ([5b8c7bd](https://gitlab.pepita.io/getrix/mls-site/commit/5b8c7bde44ae764869ff5664c4fbf1776847070c))
* **config:** set up smart phone service in Croatia (HR) ([5281ddd](https://gitlab.pepita.io/getrix/mls-site/commit/5281ddd4b6ba7672fd1e59093a877daf38f25916))
* getLanguagesData method of Localization helper now retrieves agency... ([83d3bf9](https://gitlab.pepita.io/getrix/mls-site/commit/83d3bf99a434c1d228b371b23c6cdaa028f510c3))
* **scaffolder:** add react query base configuration ([99cfa98](https://gitlab.pepita.io/getrix/mls-site/commit/99cfa9886409cc4f63ca478d8f9ef80146205dcb))
* **visibilities:** changed names for hr tenant ([8aa868f](https://gitlab.pepita.io/getrix/mls-site/commit/8aa868f65a249e6cc228984856ccd3af66386abc))


### Bug Fixes

* Croatian language missing from agency's available languages ([f625600](https://gitlab.pepita.io/getrix/mls-site/commit/f6256005b6c445833f5352a57994e9b65510e162))
* GetrixUser with traceId uninitialized ([3f70dde](https://gitlab.pepita.io/getrix/mls-site/commit/3f70dde78f81587579d4c99d7de120bfecd00d10))
* set max-char length on user-agent and referer ([b4d078f](https://gitlab.pepita.io/getrix/mls-site/commit/b4d078f3c0b107bc763a362084273bd27decba88))

## [6.27.2](https://gitlab.pepita.io/getrix/mls-site/compare/v6.27.1...v6.27.2)    (2025-02-19)


### Other

* enable trace on production ([ba90920](https://gitlab.pepita.io/getrix/mls-site/commit/ba90920f565a6110abc3d57c0cb2a9b0953cf67c))
## [6.27.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.27.0...v6.27.1)    (2025-02-18)


### Bug Fixes

* miss variable $agente ([e3be66b](https://gitlab.pepita.io/getrix/mls-site/commit/e3be66b4e73b401d789b5f4ff88a1d981952a553))
## [6.27.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.26.0...v6.27.0)    (2025-02-17)


### Features

* Trace Access anonymous to logged user ([a5d1ea1](https://gitlab.pepita.io/getrix/mls-site/commit/a5d1ea19be8f7db07365c9369a4ff5a19ffcd169))
* httpclient sentry timeout ([819c0db](https://gitlab.pepita.io/getrix/mls-site/commit/819c0dbfed362bb7ba9009c2e0c4ffba9ce970ae))
* **menu:** disabled agency ad list for the Croatia ([75ec68e](https://gitlab.pepita.io/getrix/mls-site/commit/75ec68e707000732af74828f09c0c372832f168f))
* new useFormPrompt hook ([7244e68](https://gitlab.pepita.io/getrix/mls-site/commit/7244e6837a8d678e96e5b0bcd413e0dc6544551e))
* typing mixpanel event as a documentation ([a8e35a4](https://gitlab.pepita.io/getrix/mls-site/commit/a8e35a427c311be0bedba20f8af2ce1542eac2d0))


### Bug Fixes

* **dashboard:** checking Matches feature and env (instead of zones) to show/hide matches counter ([a58d1ae](https://gitlab.pepita.io/getrix/mls-site/commit/a58d1ae225e79075df30919b0555a901dc1e0865))
* miss "api" word on access trace endpoint ([50c5283](https://gitlab.pepita.io/getrix/mls-site/commit/50c528372a6b35830201846057eeedef4c4e49d4))
* **searches:** QueryClientProvider in pages wrapper instead of in components ([df11fd3](https://gitlab.pepita.io/getrix/mls-site/commit/df11fd3f2bed3fb9b69d419bca07ff48e766ce23))
* update memcached dsn for docker env ([a46b856](https://gitlab.pepita.io/getrix/mls-site/commit/a46b8561664de8f03c162123234a981af7b40ead))


### Other

* **menu:** spain settings ([18851e8](https://gitlab.pepita.io/getrix/mls-site/commit/18851e8c9193a004d90cbbc54d146c1880ab3002))
## [6.26.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.25.4...v6.26.0)    (2025-02-12)


### Features

* **menu:** disable invoice info menu item for Croatia (HR) and refactor visibility settings ([7e55915](https://gitlab.pepita.io/getrix/mls-site/commit/7e55915bc726ca8cb9cb8f3d5955e786091f4e7e))


### Bug Fixes

* **braze:** move service worker to public folder ([1e1c417](https://gitlab.pepita.io/getrix/mls-site/commit/1e1c417b5db6261a875fbbaa76c673933f12ab40))
* **envs:** wrong memcache url in dev it ([99a5e3c](https://gitlab.pepita.io/getrix/mls-site/commit/99a5e3c9a476c7066af5b5b0fa6475311cc32624))
* **matches+active-searches:** error page on failed fetch ([8e7c54d](https://gitlab.pepita.io/getrix/mls-site/commit/8e7c54d4b1939b82985f8dbaac93f470cf3bda68))
* **psalm:** fix category 042 ([5e8ff9d](https://gitlab.pepita.io/getrix/mls-site/commit/5e8ff9d17adbfd2254fed71d8252c9989c492c07))


### Test

* **gx-navigation:** add unit tests for MenuCustomMenu and getHeaderConfiguration ([e81f271](https://gitlab.pepita.io/getrix/mls-site/commit/e81f271e3a143b0a07c3fb073358eca8c3e74673))


### Other

* checked sso product role to retrieve agency to login ([7891281](https://gitlab.pepita.io/getrix/mls-site/commit/789128167c054eb6f7cc06cb74b55c08ee4401e8))
* **envs:** memcached ES dev endpoint ([703dd78](https://gitlab.pepita.io/getrix/mls-site/commit/703dd784f2ed029397f780f36fb1460427c2716f))
* **sso-login:** added client ip check ([e04b66f](https://gitlab.pepita.io/getrix/mls-site/commit/e04b66fe612e036b58c6bff5be57e3c236eee5a1))

## [6.25.4](https://gitlab.pepita.io/getrix/mls-site/compare/v6.25.3...v6.25.4)    (2025-02-10)


### Bug Fixes

* update default language retrieval to use gtxConstants instead from document ([1122dbd](https://gitlab.pepita.io/getrix/mls-site/commit/1122dbd7d29ab6da2beb860342a2d6eed95a3e39))
## [6.25.3](https://gitlab.pepita.io/getrix/mls-site/compare/v6.25.2...v6.25.3)    (2025-02-07)


### Bug Fixes

* AuctionsSearchFilter courts check if is_array ([f99460a](https://gitlab.pepita.io/getrix/mls-site/commit/f99460a4b7d608a0fe9f555d1180ac8b71e1b1ee))
* **report:** remove hard-coded id from Checkbox component ([470b266](https://gitlab.pepita.io/getrix/mls-site/commit/470b266bae979e7f7bc5273b0ac871f3ad9d6520))
## [6.25.2](https://gitlab.pepita.io/getrix/mls-site/compare/v6.25.1...v6.25.2)    (2025-02-06)


### Other

* rollout a3f ([6daf185](https://gitlab.pepita.io/getrix/mls-site/commit/6daf185bfdbe5490251d16fba694624151c06ddc))
## [6.25.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.25.0...v6.25.1)    (2025-02-04)


### Bug Fixes

* **auctions:** auctions catalogue filters type ([28a9507](https://gitlab.pepita.io/getrix/mls-site/commit/28a95070a3881a9d8f8716268cf8f3bbdcfa5e6c))
* geocoder url endpoint and consumerKey ([ca3ed76](https://gitlab.pepita.io/getrix/mls-site/commit/ca3ed76b2ed8c6f44b47b1eff79988d0fe8d74c4))


### Test

* **mortgage-advice:** simplify user input simulation in tests and adjust request handling ([6b2408a](https://gitlab.pepita.io/getrix/mls-site/commit/6b2408aa2d78fc460512198e6bc4be5304f6ec91))


### Other

* bump translations for croatia hr_HR file ([fad2225](https://gitlab.pepita.io/getrix/mls-site/commit/fad222521fb48df8f478a117088c7fb3726b2b0d))
## [6.25.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.24.0...v6.25.0)    (2025-02-03)


### Features

* **app-shell:** add polyfills for fill, fromEntries, and toBlob ([3fff0ec](https://gitlab.pepita.io/getrix/mls-site/commit/3fff0ecc3869826fc614c85d7ac9285fef73e8d6))


### Bug Fixes

* Billboard ([3bf8b75](https://gitlab.pepita.io/getrix/mls-site/commit/3bf8b75e21794dffaa99e54d629b464d14879951))
* **braze:** filter log enabled users ([d148eae](https://gitlab.pepita.io/getrix/mls-site/commit/d148eae4afdc679e4798069ba87eacb5c20f7aef))


### Other

* **braze:** selective logging ([26441a8](https://gitlab.pepita.io/getrix/mls-site/commit/26441a8348cf6c2cf214dfd67b5ff4b79943abb8))
* remove console.log ([7ffff29](https://gitlab.pepita.io/getrix/mls-site/commit/7ffff2943ee1f3e9e6d6be4d7c5585610923e1f8))

## [6.24.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.23.0...v6.24.0)    (2025-01-30)


### Features

* **envs:** update memcached urls for dev references ([e04e348](https://gitlab.pepita.io/getrix/mls-site/commit/e04e3485b7259a8001b862f2ca5c1691f2a966c6))
* **notification-settings:** add mixpanel notification update event ([52a5e14](https://gitlab.pepita.io/getrix/mls-site/commit/52a5e143d53b9207335fb73378e966ac79d11529))


### Bug Fixes

* **notification-settings:** switch mixpanel area and section ([f7a51fe](https://gitlab.pepita.io/getrix/mls-site/commit/f7a51fe46835c752425d2f158c0f30c5c32fb962))
## [6.23.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.22.0...v6.23.0)    (2025-01-28)


### Features

* IP Address is legit if also it's country and country app are equal ([c3e5c52](https://gitlab.pepita.io/getrix/mls-site/commit/c3e5c5281da086c1805f97577f35db5a33328714))
* **fapi:** set default content-language header in requests ([b7953d7](https://gitlab.pepita.io/getrix/mls-site/commit/b7953d7f0e67b0a4a6372f651a5daef7522eedb7))


### Bug Fixes

* in ActionSearchFilter date are string and not datetime ([72ff947](https://gitlab.pepita.io/getrix/mls-site/commit/72ff947e511e70f3991e48eb8ff272873933e243))
* **login:** better if check to set authentication tokens ([05b8393](https://gitlab.pepita.io/getrix/mls-site/commit/05b83938b1a479e28bc791be70a6275d35ad295a))


### Other

* **braze:** prod IT api key ([4a1fc48](https://gitlab.pepita.io/getrix/mls-site/commit/4a1fc4879e3222cb28a9d3f3b29c157386ca6b3e))
* **braze:** request permission callback ([1990176](https://gitlab.pepita.io/getrix/mls-site/commit/1990176da77236385461b9bfc6729502028e1b80))

## [6.22.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.21.1...v6.22.0)    (2025-01-21)


### Features

* **youdomus:** adds new ape certification menu entry ([4dd0514](https://gitlab.pepita.io/getrix/mls-site/commit/4dd051484d68a192169b605d2f88c22d8b187c33))


### Other

* **braze:** open in-app messages and push notification in prod ([97cc199](https://gitlab.pepita.io/getrix/mls-site/commit/97cc1990bbbe9df29e83547df36da83feea18bd2))
## [6.21.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.21.0...v6.21.1)    (2025-01-20)


### Bug Fixes

* restore removed method AgencyAdapter::getLanguagesKeys ([8529b7d](https://gitlab.pepita.io/getrix/mls-site/commit/8529b7dea48286854a0f482f29e40a11f44b0b0f))
## [6.21.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.20.0...v6.21.0)    (2025-01-20)


### Features

* **menu:** set up menu items for the Croatia ([189de5d](https://gitlab.pepita.io/getrix/mls-site/commit/189de5d043b9356fe6ce5a6d27024bcc261137d8))


### Bug Fixes

* **braze:** creates an endpoint returning braze sw content ([6e5a5b0](https://gitlab.pepita.io/getrix/mls-site/commit/6e5a5b083b737e209fe12950eaf6f1dc2291836d))
* psalm error category 007 ([85e6c43](https://gitlab.pepita.io/getrix/mls-site/commit/85e6c43e9163b339e9c61d2392a4049bdc864ce8))
* psalm error category 128 ([83cc0f2](https://gitlab.pepita.io/getrix/mls-site/commit/83cc0f2db604e873008c8b4da08421a793f39589))
* yarn build sequential commands ([ac3005e](https://gitlab.pepita.io/getrix/mls-site/commit/ac3005e74bddde6700a3a5b7e44046f064ef235c))


### Other

* deleted env in the root project ([dcafebb](https://gitlab.pepita.io/getrix/mls-site/commit/dcafebb000cf155ba9cb7c09e51060940540dfbf))
* **helpjuice:** restored modal mode. Replaced account url env ([d306013](https://gitlab.pepita.io/getrix/mls-site/commit/d306013bc1d3fb46a0f53245fcf999aafdfcd5f3))
* **helpjuice:** turn off modal mode ([3f33652](https://gitlab.pepita.io/getrix/mls-site/commit/3f33652c932f989706e3718e0148a9701134a40e))
* **searches:** limit matches and active searches counter by date ([a2038fb](https://gitlab.pepita.io/getrix/mls-site/commit/a2038fb1063272d9bd224f3d7ce80d27a65feac2))
## [6.20.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.19.2...v6.20.0)    (2025-01-13)


### Features

* **active-searches:** add city and macrozones search filter ([fad6822](https://gitlab.pepita.io/getrix/mls-site/commit/fad68224b4ea86ba6aa94cc6aafa62bab21b425d))
* add Object.fromEntries() polyfill ([1bbcf4e](https://gitlab.pepita.io/getrix/mls-site/commit/1bbcf4e2490b8a528203c53c92afa63b2539226c))
* **gx-navigation:** introduce tests ([e4d4934](https://gitlab.pepita.io/getrix/mls-site/commit/e4d493423144dc30ef347fa9c8bbb2694b135285))


### Bug Fixes

* environment for Croatia ([3a12dc6](https://gitlab.pepita.io/getrix/mls-site/commit/3a12dc64c624b379be8074ef58c3282086feb084))
* **gx-navigation:** revert of introduce tests ([251eeb7](https://gitlab.pepita.io/getrix/mls-site/commit/251eeb73639dad27f7a42f5a70f655c40f3cd5ba))
* **login:** use device fingerprint also for backoffice and multiagency login ([9e5ee57](https://gitlab.pepita.io/getrix/mls-site/commit/9e5ee57b51ffb0f6fb9f11d3e6cbd4697e3ad149))
* **settings:** tab translations ([e2f17b1](https://gitlab.pepita.io/getrix/mls-site/commit/e2f17b18d3e336989b778788bbfffb99eb1e7fdb))


### Other

* **billboard:** typo in get endpoint ([c80a4b9](https://gitlab.pepita.io/getrix/mls-site/commit/c80a4b94870d108605be97856dc7b05981e1dacc))
* **searches:** enable notification setting by zone to all agencies ([19fdfa1](https://gitlab.pepita.io/getrix/mls-site/commit/19fdfa11709e9b8d7c94fe456cc98f90a75590e4))

## [6.19.2](https://gitlab.pepita.io/getrix/mls-site/compare/v6.19.1...v6.19.2)    (2025-01-10)


### Bug Fixes

* **report:** footer width ([68098bb](https://gitlab.pepita.io/getrix/mls-site/commit/68098bbf26b0ecafd7bdb1a72cbb96c67f77e81c))
* temporary disabled hr languade consts ([f705713](https://gitlab.pepita.io/getrix/mls-site/commit/f70571319a7bf34599280e3cbc8e685a0d837b07))


### Other

* bump cms and cms-contents ([113ee6d](https://gitlab.pepita.io/getrix/mls-site/commit/113ee6d18d7053cf09f27d186c38418069a56645))
## [6.19.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.19.0...v6.19.1)    (2025-01-09)


### Bug Fixes

* fixed missing translations ([3ff1453](https://gitlab.pepita.io/getrix/mls-site/commit/3ff1453a2fa2829ba926eef74b45c315d6d390ef))


### Other

* revert cms and cms-contents tags ([a04069a](https://gitlab.pepita.io/getrix/mls-site/commit/a04069af703a992444cad94cab80e35e54ebfdeb))
## [6.19.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.18.6...v6.19.0)    (2025-01-09)


### Features

* **envs:** set event-server urls for all environments of the Croatia (HR) ([aa43aa6](https://gitlab.pepita.io/getrix/mls-site/commit/aa43aa6e96958b72fd3e2788f3462f7d0a1a8bb3))
* **settings-notification:** open version 2 to specified agencies ([fb373e2](https://gitlab.pepita.io/getrix/mls-site/commit/fb373e2e4349f2c4e3aaa74f2343d4d2d4544f7e))


### Bug Fixes

* **acquisition-privates:** load zones when single city filter is loaded ([ad8e480](https://gitlab.pepita.io/getrix/mls-site/commit/ad8e4809676dff6cf9a16431173aceebbaa55801))
* **acquisition-privates:** price table header ([74e97f1](https://gitlab.pepita.io/getrix/mls-site/commit/74e97f185f5bd2a1cc7bb5c8aa8ad9d8b330594d))
* bad syntax declaration into docker generate_certs ([bb43358](https://gitlab.pepita.io/getrix/mls-site/commit/bb43358c0fc8efff3a1e34b24a6a0e033253db80))
* **estimate:** missing onchange breaks estimate creation on step 3 ([686d9d8](https://gitlab.pepita.io/getrix/mls-site/commit/686d9d8b88805bde85a0f099011f93d9a2ee9ada))
* **geography-fields:** restored placeholders ([eefdd59](https://gitlab.pepita.io/getrix/mls-site/commit/eefdd5938b49395a1b35383055b0fa2871b24eec))


### Other

* chore/add js tests

See merge request getrix/mls-site!3186 ([11a5b21](https://gitlab.pepita.io/getrix/mls-site/commit/11a5b217b230d402f7bb0874167195f6f12404a5))
* **christmastime:** off ([0dbd220](https://gitlab.pepita.io/getrix/mls-site/commit/0dbd220733cd3f07cda336c33a4847bc6b40860f))
* **localization:** update cms and cms-contents to the latest version ([fc076d3](https://gitlab.pepita.io/getrix/mls-site/commit/fc076d33f5b7db6330a5f731a622496d315c29bf))
## [6.18.6](https://gitlab.pepita.io/getrix/mls-site/compare/v6.18.5...v6.18.6)    (2024-12-19)


### Refactor

* **checkbox:** replaced old component into auth-register and multiselect ([0d4b97d](https://gitlab.pepita.io/getrix/mls-site/commit/0d4b97dc47dca8798d237be89bd62c94fb5facd2))
* move to new format ([b9a2e88](https://gitlab.pepita.io/getrix/mls-site/commit/b9a2e888c9a7261b2bf7b19a14f47bb854c2c598))

## [6.18.5](https://gitlab.pepita.io/getrix/mls-site/compare/v6.18.4...v6.18.5)    (2024-12-18)

## [6.18.4](https://gitlab.pepita.io/getrix/mls-site/compare/v6.18.3...v6.18.4)    (2024-12-17)


### Bug Fixes

* extravisibility counters ([ec0b6e8](https://gitlab.pepita.io/getrix/mls-site/commit/ec0b6e8d376210532687c1342a7f6ef0b49c4ee8))
* fixed environment file for hr ([001a36d](https://gitlab.pepita.io/getrix/mls-site/commit/001a36df19339bcdb591ba996db45cadffa99ca5))
* **helpjuice:** ask to ai button temporary links to an external page ([8f4087f](https://gitlab.pepita.io/getrix/mls-site/commit/8f4087f56e19fa2b2866d05f766774b69189ee0e))
* replace telefonosmart helper ([d15d848](https://gitlab.pepita.io/getrix/mls-site/commit/d15d84829e5dcb27229cc8a0067557ef4a452c88))

## [6.18.3](https://gitlab.pepita.io/getrix/mls-site/compare/v6.18.2...v6.18.3)    (2024-12-17)


### Bug Fixes

* **composer:** updated soa/sdk dependencies ([5b2c75c](https://gitlab.pepita.io/getrix/mls-site/commit/5b2c75c5eec590b1d27fab129315709ae3b37c9b))
* revert userDataToken value ([5c0cd78](https://gitlab.pepita.io/getrix/mls-site/commit/5c0cd78d93884ab8c8e308aefd7d65edc0850ab5))
* updated env file for hr ([840e4bf](https://gitlab.pepita.io/getrix/mls-site/commit/840e4bfcf6b0a7c2bf6d8ffdad6bf9358c8e6a62))

## [6.18.2](https://gitlab.pepita.io/getrix/mls-site/compare/v6.18.1...v6.18.2)    (2024-12-16)


### Bug Fixes

* **customers:** removes duplicated customerType Select ([b684851](https://gitlab.pepita.io/getrix/mls-site/commit/b684851955e3d3f0c8fe1fbfa077d516f1845686))
## [6.18.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.18.0...v6.18.1)    (2024-12-13)

## [6.18.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.17.0...v6.18.0)    (2024-12-12)


### Features

* **remote-visits:** switch to FAPI in PRD ([fb6bca2](https://gitlab.pepita.io/getrix/mls-site/commit/fb6bca2184875e31998e1fcb31c7c787eefea3f4))
* **select:** replace old select components ([c670d66](https://gitlab.pepita.io/getrix/mls-site/commit/c670d66cb27e902ea7caa93410502b0d566b7fa4))


### Refactor

* **svg sprite:** remove ([9e7677c](https://gitlab.pepita.io/getrix/mls-site/commit/9e7677cf5e2fc85e5e3d1119b3c48dd2293ad48f))

## [6.17.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.16.0...v6.17.0)    (2024-12-09)


### Features

* **active-search+matches:** notification settings link btn hide logic and gx-nav portal ([da278b5](https://gitlab.pepita.io/getrix/mls-site/commit/da278b5dd6d395552346a61286319b01304949b2))
* copy all classes of soa/sdk and soa/sdk-bundle used into Model\SoaSdk ([2526132](https://gitlab.pepita.io/getrix/mls-site/commit/2526132d8ea3f964c99388d54eb2c7b22a325863))
* **header:** christmas lights ([d6592ea](https://gitlab.pepita.io/getrix/mls-site/commit/d6592eaa093ec88238b00f06e3a8232e33177d10))


### Bug Fixes

* **christmas-time:** end christmasTime ([0454a04](https://gitlab.pepita.io/getrix/mls-site/commit/0454a04fd428f7e7811bf59944fb7ac8e77a65a8))
* **config:** wrong configuration for the Croatia ([292c247](https://gitlab.pepita.io/getrix/mls-site/commit/292c247302c97e5b7cf6289f062d27badc2528c5))
* **login:** always set device fingerprint ([561b4a7](https://gitlab.pepita.io/getrix/mls-site/commit/561b4a70a3378b6fdd4c628e326f5109ab7862cb))
* **login:** always use new device fingerprint when login from backoffice ([39b6e49](https://gitlab.pepita.io/getrix/mls-site/commit/39b6e498bc2bd0259d7f0978dad670c0e6491066))


### Refactor

* **input:** replaced old components into customers and immovisita list sections ([09f8f1e](https://gitlab.pepita.io/getrix/mls-site/commit/09f8f1e82c93398445ce87637db182f31be9e23d))

## [6.16.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.15.0...v6.16.0)    (2024-12-05)


### Features

* **login:** use api login authentication tokens ([29d3e3b](https://gitlab.pepita.io/getrix/mls-site/commit/29d3e3b94854c0acc0517b444917eec2000e00f8))


### Bug Fixes

* **billboard:** edit text modal ([4f115f0](https://gitlab.pepita.io/getrix/mls-site/commit/4f115f085b3e61354356bef58fb3d6afbcc5ae5d))
* **helpjuice:** initiliaze only if logged ([78d98dc](https://gitlab.pepita.io/getrix/mls-site/commit/78d98dcd992222dbcc72c6939724de91b5615bcd))
* **login:** pass device fingerprint when login from backoffice ([1b5cb92](https://gitlab.pepita.io/getrix/mls-site/commit/1b5cb92ade3bf869f9afb9f949fba5a8047858a8))

## [6.15.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.14.0...v6.15.0)    (2024-12-02)


### Features

* Initialize Braze SDK ([590b07f](https://gitlab.pepita.io/getrix/mls-site/commit/590b07f9ac64a56e4fa7ff9007388b7c4c229491))


### Bug Fixes

* **active-searches:** no mixpanel events ([ff2ccf4](https://gitlab.pepita.io/getrix/mls-site/commit/ff2ccf46cc5782ff3a37c365a283d21b6a4d7052))

## [6.14.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.13.0...v6.14.0)    (2024-11-29)


### Features

* **settings-security:** add agent visibility only for administrators ([4e87a8b](https://gitlab.pepita.io/getrix/mls-site/commit/4e87a8bc36fd2daa4496ea0c54d103635b062513))


### Bug Fixes

* fixed ci custom build ([a1dd959](https://gitlab.pepita.io/getrix/mls-site/commit/a1dd9595fc41cb2d20f5a6e1c338216d859a77b0))

## [6.13.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.12.0...v6.13.0)    (2024-11-28)


### Features

* add whitelist and metrics for ip2location ([632fc8f](https://gitlab.pepita.io/getrix/mls-site/commit/632fc8f9697d20b800c2b06e17fdc98a79dfb20c))
* replace reflection with constant function ([78a3415](https://gitlab.pepita.io/getrix/mls-site/commit/78a3415111d1c34dd8dd4b7e40dc50cf2d26118d))


### Bug Fixes

* typo ([e58fd37](https://gitlab.pepita.io/getrix/mls-site/commit/e58fd372097db30e56363b4b1cfabcd59d5302ae))


### Refactor

* **input:** replaced old input components into billboard section ([006d12b](https://gitlab.pepita.io/getrix/mls-site/commit/006d12ba1fb71bbf56664862eb33ac16ac03b74f))

## [6.12.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.11.2...v6.12.0)    (2024-11-27)


### Features

* **settings:** set lowercase email and website in general settings ([769032e](https://gitlab.pepita.io/getrix/mls-site/commit/769032e577bd84adcc9809473e47f579d1eb7bae))


### Bug Fixes

* **mail:** common update ([ec0b90d](https://gitlab.pepita.io/getrix/mls-site/commit/ec0b90d289fd6fcde93f780c51037550e01e8d44))

## [6.11.2](https://gitlab.pepita.io/getrix/mls-site/compare/v6.11.1...v6.11.2)    (2024-11-26)


### Bug Fixes

* typo metrics prefix ([081e7ad](https://gitlab.pepita.io/getrix/mls-site/commit/081e7ad8f725b3279d6724a88e2374df103d655f))
## [6.11.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.11.0...v6.11.1)    (2024-11-26)


### Bug Fixes

* typo ([9cb5ae9](https://gitlab.pepita.io/getrix/mls-site/commit/9cb5ae9997ebf3af400b2325af3932be7049c93e))

## [6.11.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.10.0...v6.11.0)    (2024-11-26)


### Features

* Add Ip2Location check for valid ip source ([05a58fd](https://gitlab.pepita.io/getrix/mls-site/commit/05a58fdcf0e1c49d31ea6a7ff5278f1fc967ab31))


### Bug Fixes

* **gx-navigation:** adds onClose when ask to ai is clicked ([5f2abe7](https://gitlab.pepita.io/getrix/mls-site/commit/5f2abe7789b1959e815c79aba99f54c3ecf7a753))
* **portal-lists:** changed place labels ([0aeee6b](https://gitlab.pepita.io/getrix/mls-site/commit/0aeee6b3ef16253b93270187488d4f33f750e5ee))

## [6.10.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.9.1...v6.10.0)    (2024-11-25)


### Features

* Add Ip2Location check for valid ip source ([c2c59a6](https://gitlab.pepita.io/getrix/mls-site/commit/c2c59a6bd571b96eb8004e90703a7eac0d3da366))
* Add Ip2Location check for valid ip source ([e629bf4](https://gitlab.pepita.io/getrix/mls-site/commit/e629bf4d1befc8b6d2b122b7f197048a78ab9028))
* **revert:** Add Ip2Location check for valid ip source ([ec039db](https://gitlab.pepita.io/getrix/mls-site/commit/ec039dbd9c0e238da018b89db2bfc52f11631407))


### Bug Fixes

* **portal-properties:** removes tag beta from performance column ([5da7947](https://gitlab.pepita.io/getrix/mls-site/commit/5da7947c7ef8975d4e83f9fdc1fc441b8c6878c9))
* **searches:** auction filter should be null when category is not auction ([49c7e2e](https://gitlab.pepita.io/getrix/mls-site/commit/49c7e2eb0f3b46b72701a05cb928ffec9baa3ff1))
* typo ([e084178](https://gitlab.pepita.io/getrix/mls-site/commit/e0841780b8f4a28d2d5958eaf64dc50c3c0cbfee))

## [6.9.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.9.0...v6.9.1)    (2024-11-25)


### Bug Fixes

* check type of argument pprovided to PortalInfoFormatter::dateFormatter ([2fbbed8](https://gitlab.pepita.io/getrix/mls-site/commit/2fbbed8c265d4a46692f32ea8b4899bc5c52ffe0))
* check type of argument pprovided to PortalInfoFormatter::timeFormatter ([7c9b479](https://gitlab.pepita.io/getrix/mls-site/commit/7c9b47998ec84cff5ab590638b8243e5b01aab0f))
* src/Builder/Model/Portal/PortalInfoBuilder.php added check on date format ([c3bf1a2](https://gitlab.pepita.io/getrix/mls-site/commit/c3bf1a2bdd086cadf344a28013bacb88b0d70963))

## [6.9.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.8.0...v6.9.0)    (2024-11-25)


### Features

* bump vitest and related tools, fix tests due to the upgrade ([0e2f36b](https://gitlab.pepita.io/getrix/mls-site/commit/0e2f36b89978ca31a522cdb572c38fffba0b5367))
* enable Helpjuice integration and update configurations ([65cbd75](https://gitlab.pepita.io/getrix/mls-site/commit/65cbd75005a72130a6237df3685ea5cc9baf9359))
* **notification-settings:** enable notification by contract category ([032097d](https://gitlab.pepita.io/getrix/mls-site/commit/032097d71a57e8831d3aa3f210dc3d98963141f9))


### Bug Fixes

* **ci:** fixed ci after global task renaming ([a1d70bb](https://gitlab.pepita.io/getrix/mls-site/commit/a1d70bb6fb1be78f6ef05c3aec3608f0e864c46e))
* **performance:** table detail ([9324969](https://gitlab.pepita.io/getrix/mls-site/commit/9324969e5e45822cb2f41e53a570954e16c43cd6))
* **psalm:** some method argumenta are incompatible with the method signature or docblock one ([7f89101](https://gitlab.pepita.io/getrix/mls-site/commit/7f8910156743e2a0259c563cc0c87e4e6cee08a5))

## [6.8.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.7.0...v6.8.0)    (2024-11-14)


### Features

* **agents:** remove format of agent first and last name ([c047c18](https://gitlab.pepita.io/getrix/mls-site/commit/c047c187fd52cc822d570e0197f63a3f878c6958))
* enabling price proposal in IT prod ([fae8cfb](https://gitlab.pepita.io/getrix/mls-site/commit/fae8cfbd3ddecce1ce53698f37c42f5dc8fdbc44))
* implement GetDetailedThreadStatsController ([4a90016](https://gitlab.pepita.io/getrix/mls-site/commit/4a900163cf79c1d1a9e78eedba09dfd6d1a3c15a))
* **performance:** priceProposals controlled via env ([38439b1](https://gitlab.pepita.io/getrix/mls-site/commit/38439b16b8f0642b2152a5f2e3729bd2eacec6c1))


### Bug Fixes

* **daterangepicker:** separated inputs and arrow not going outside picker ([1cd4e8d](https://gitlab.pepita.io/getrix/mls-site/commit/1cd4e8d5bea1dbe4bd1786994e27cd184b230cc0))
* **psalm:** UndefinedClass issues ([711549f](https://gitlab.pepita.io/getrix/mls-site/commit/711549fb8f8be26ad8b5a0f2cc15e2741875447f))
* **validation-errors:** adds description entry ([c2c1746](https://gitlab.pepita.io/getrix/mls-site/commit/c2c1746c82a294bc6e3450ed44495de3b7f97c36))
* **virtual-tour:** getrix colors ([2db2e42](https://gitlab.pepita.io/getrix/mls-site/commit/2db2e42474716b959bd75acb6ce080b50654d1f6))


### Refactor

* **check-status:** react migration of tos page ([f8167cd](https://gitlab.pepita.io/getrix/mls-site/commit/f8167cd348a17e6840ee2967c952227214c238fb))

## [6.7.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.6.0...v6.7.0)    (2024-10-31)


### Features

* **envs:** add "reference" environment ([65106ba](https://gitlab.pepita.io/getrix/mls-site/commit/65106ba8bb3cb523228ba4ed2338bbba174983f5))
* messaging implement SetNotFavouriteThread ([391e1ff](https://gitlab.pepita.io/getrix/mls-site/commit/391e1ff91c1f685ff55300f4a80dae18b97351c7))
* **performance:** commented code about priceProposals metric ([1826472](https://gitlab.pepita.io/getrix/mls-site/commit/18264721a0466c72bb1f7448f649553b11e88867))


### Bug Fixes

* agency coordinates given as string instead of float ([68aceee](https://gitlab.pepita.io/getrix/mls-site/commit/68aceee8063dde31e57689f864fb687cbf89dcfb))
* aggiornato puntamento memcached spagna prod ([dbcc6e6](https://gitlab.pepita.io/getrix/mls-site/commit/dbcc6e64d1bd16ce4f896e753041e3eb60b1c0ac))
* **messaging:** reset 'unread' quick filter on tab change; 'code' filter does not reset other filters ([e88c527](https://gitlab.pepita.io/getrix/mls-site/commit/e88c527eec0f910924396826fe3c50e4b774c796))
* **performance:** avoiding malformed and duplicate calls to bulk properties stats api ([3f5f567](https://gitlab.pepita.io/getrix/mls-site/commit/3f5f5675a971803a23b1b3cc6303ec3872ef099d))

## [6.6.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.5.0...v6.6.0)    (2024-10-24)


### Features

* **gx-design:** custom properties ([c2bd79f](https://gitlab.pepita.io/getrix/mls-site/commit/c2bd79fb335d4884dde9519949de15a593d15391))


### Bug Fixes

* allow labelId to be null ([c1e3621](https://gitlab.pepita.io/getrix/mls-site/commit/c1e362172456cd19244ba6c17772c1223fe995b5))
* **copy:** from "visits" to "visits detail" ([d352352](https://gitlab.pepita.io/getrix/mls-site/commit/d3523529cc43788fba8a42386643661026862992))
* **mixpanel:** sending all object with keys in snake case ([4e0e7d8](https://gitlab.pepita.io/getrix/mls-site/commit/4e0e7d8a450d05578ae0ee59414d9cd5d9d1f288))
* **performance:** broken pie chart into statistics-only page ([0b5759f](https://gitlab.pepita.io/getrix/mls-site/commit/0b5759f978079b0d353bf04ef3d1f54ddff59314))
* riabilitate metriche stats ([5426e29](https://gitlab.pepita.io/getrix/mls-site/commit/5426e296caf6425d43f2e3b5887a4436f965116e))

## [6.5.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.4.0...v6.5.0)    (2024-10-21)


### Features

* **configurations:** added configurations for notification settings ([6e1242d](https://gitlab.pepita.io/getrix/mls-site/commit/6e1242dd24e5c30bca76bbcfd17ec07ccdc2d03b))
* notification settings wip ([68c9d25](https://gitlab.pepita.io/getrix/mls-site/commit/68c9d25b3cd9c691803cf261c9b226a5db738886))


### Bug Fixes

* **list-filters:** ucFirst on agent name and lastname ([9881fd7](https://gitlab.pepita.io/getrix/mls-site/commit/9881fd74393f8e647f910c32bb5f1547f525a0d3))
* **notification settings:** spacing and icon ([d5831e2](https://gitlab.pepita.io/getrix/mls-site/commit/d5831e2779fbed32b03760236be7d4c087d4fed0))
* **notification-settings:** adds page featue toggle and env flag ([8816cd1](https://gitlab.pepita.io/getrix/mls-site/commit/8816cd1dab63bae86276631f33984894520297ab))

## [6.4.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.3.0...v6.4.0)    (2024-10-16)


### Features

* add createImageTag - updateImageTag ([e9c18c1](https://gitlab.pepita.io/getrix/mls-site/commit/e9c18c1484dff4cded51493fda4da1cb9f5b9940))
* integrate fapi with mls 🎉 and refactor remote visits ([0c7b761](https://gitlab.pepita.io/getrix/mls-site/commit/0c7b7611047d25bb731390d536d2cfa8b38bcdd6))
* messaging implement setFavouriteThread ([06fee46](https://gitlab.pepita.io/getrix/mls-site/commit/06fee46101d17528f5370c3ca74120682b180208))
* **youdomus:** add agent uuid to iframe url ([fd5a893](https://gitlab.pepita.io/getrix/mls-site/commit/fd5a8930f23c33baad8a2ebae43792c8bebb923f))


### Bug Fixes

* aggiunte metriche per controller Rest Security ([e4b341e](https://gitlab.pepita.io/getrix/mls-site/commit/e4b341e2768930739b5d6cd9d5bc61a34d700534))
* corrected variables missing definition ([4323b3a](https://gitlab.pepita.io/getrix/mls-site/commit/4323b3afb1b533c9ac529e8907ab82c931cb3f87))
* **international-prefix:** order international prefixes according to country tag ([d5491af](https://gitlab.pepita.io/getrix/mls-site/commit/d5491af25f8a7c0917a075429771a1dc973a872f))
* **security:** missing class import for stats ([a257b77](https://gitlab.pepita.io/getrix/mls-site/commit/a257b779ed5df7d9f58db65ca889cd436fe37907))
* **settings-general:** removing obsolete fields fax and facebook from form ([8c1904a](https://gitlab.pepita.io/getrix/mls-site/commit/8c1904a3b23b32cb049a0aa5990d8dc6e1092b44))

## [6.3.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.2.0...v6.3.0)    (2024-10-08)


### Features

* messaging implement deleteThread ([f14a947](https://gitlab.pepita.io/getrix/mls-site/commit/f14a947f5a0dd4b95a4314fec5640191c5869cd0))


### Bug Fixes

* Trying to access array offset on value of type null ([2041196](https://gitlab.pepita.io/getrix/mls-site/commit/20411961473de6fef0bf37e8ad821cbec2633e82))
* corrected dev environment urls ([44ffc31](https://gitlab.pepita.io/getrix/mls-site/commit/44ffc31d6402398c302c606daf8eacb97da17850))
* **form:** border-radius update ([c89f4b9](https://gitlab.pepita.io/getrix/mls-site/commit/c89f4b9cd1f8dda22f31a406b9e292a21138614c))
* **menu:** add missing settings security to menu ES ([d903691](https://gitlab.pepita.io/getrix/mls-site/commit/d903691a54f273c05cc73bb87c12ba5909a4deb0))
* messaging list threads after rebase ([5c75c1e](https://gitlab.pepita.io/getrix/mls-site/commit/5c75c1e3a1485b905b0538e84033f678858e2c32))
* **new-constructions:** retrieves place date from project object ([653b9e7](https://gitlab.pepita.io/getrix/mls-site/commit/653b9e70b725b21984211c0d8ae14661d74368e4))

## [6.2.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.1.0...v6.2.0)    (2024-10-07)


### Features

* **gx-navigation:** enable GX navigation menu in ES ([f1edd56](https://gitlab.pepita.io/getrix/mls-site/commit/f1edd5693a1af7d25ed7716ac701494d8e77c832))
## [6.1.0](https://gitlab.pepita.io/getrix/mls-site/compare/v6.0.1...v6.1.0)    (2024-10-01)


### Features

* **header:** add tooltip to buttons ([11d5a4f](https://gitlab.pepita.io/getrix/mls-site/commit/11d5a4fdb4994f85b5978c942a0d4db820ad4b8c))
* messaging implement deleteMultipleThreads ([3999f4e](https://gitlab.pepita.io/getrix/mls-site/commit/3999f4e2370d76dd1b140bb2efa9566b0b261d11))


### Bug Fixes

* try to access array offset on value of type null ([aae0375](https://gitlab.pepita.io/getrix/mls-site/commit/aae03759376220f8dba5287582ef313a490f4ee7))

## [6.0.1](https://gitlab.pepita.io/getrix/mls-site/compare/v6.0.0...v6.0.1)    (2024-09-26)


### Bug Fixes

* **deps:** upgraded vt360 and photoplan deps. Also @nugget/* for compatibility ([913adc9](https://gitlab.pepita.io/getrix/mls-site/commit/913adc9fddf531d6613aee87ed02e59f08bac925))
* **docker:** missing VOLTA_HOME variable configuration ([216a075](https://gitlab.pepita.io/getrix/mls-site/commit/216a0758651b4c9ec957cb244458b24cb3701433))
* typing issue ([117a568](https://gitlab.pepita.io/getrix/mls-site/commit/117a568bc4a412a0d5a288e719465864b9fdcbd2))
* update photoplan version ([4ac0da2](https://gitlab.pepita.io/getrix/mls-site/commit/4ac0da2725559d9168ccbdefc47d0b7f0dad7a3a))
* virtual and photoplan colors on Getrix ([decf36b](https://gitlab.pepita.io/getrix/mls-site/commit/decf36b281b44249c8dae2c5362e66f2df2ccfda))
## [6.0.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.139.0...v6.0.0)    (2024-09-25)


### Features

* added new CI to php 7.4 version ([c20bf2c](https://gitlab.pepita.io/getrix/mls-site/commit/c20bf2c0db2a2fe613eb1c5b295d8a8c63a5fb12))
* bump version ([b3a2aa2](https://gitlab.pepita.io/getrix/mls-site/commit/b3a2aa2bb9f013623fdf94156a76b4c4eb5c555b))
* feat/php 74

See merge request getrix/mls-site!2571 ([6dc5709](https://gitlab.pepita.io/getrix/mls-site/commit/6dc570915e7b8633f4e3ae9cb44058af20cf4d6a))


### Bug Fixes

* typing issues ([8e17a0c](https://gitlab.pepita.io/getrix/mls-site/commit/8e17a0c7d29cccc93e5092b54e046560c0dd5afa))

## [5.139.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.138.0...v5.139.0) (2024-09-25)


### Features

* **immotop:** new logo ([a22f094](https://gitlab.pepita.io/getrix/mls-site/commit/a22f0947d7ab22a2216e204fa190cceb16dea6cb))
* **salesforce:** enabling live chat in prod it ([9100db5](https://gitlab.pepita.io/getrix/mls-site/commit/9100db5d15544668efe199c191afe0742922699c))
* switch to getrix api v2 endpoints ([89def24](https://gitlab.pepita.io/getrix/mls-site/commit/89def243d47ed29ab9ea4ce8ffe21d0cd31cf0f0))

## [5.138.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.137.2...v5.138.0) (2024-09-23)


### Features

* **acquisition-privates:** remove property-exist api call ([42a5819](https://gitlab.pepita.io/getrix/mls-site/commit/42a58194f4186e47d812ae5e3b43e583022c3ab2))
* deleted requirement for https connection ([ff73d8e](https://gitlab.pepita.io/getrix/mls-site/commit/ff73d8e1b3c78597e41a431b27afd7a9401ad458))
* **matches:** tests ([ddd8799](https://gitlab.pepita.io/getrix/mls-site/commit/ddd87998e976573523be57229586d5c005989ef1))
* switch to getrix api v2 endpoints ([9ada13a](https://gitlab.pepita.io/getrix/mls-site/commit/9ada13a70638ebc6e94c1d1bd17ca6c677692b7b))


### Bug Fixes

* **active-searches:** customers_agency_active_search_handled mixpanel event... ([ee79b2c](https://gitlab.pepita.io/getrix/mls-site/commit/ee79b2cc97d7bffc205fd7c2d3fa575541722b3b))
* **counters:** active search new counter + unified useListenCounter hook ([cb9e311](https://gitlab.pepita.io/getrix/mls-site/commit/cb9e3113a564175faaa3f9584dc6c7b361457bf8))
* locality can be nullable ([bd755c4](https://gitlab.pepita.io/getrix/mls-site/commit/bd755c4a2c244bbf4fbbfd47abd25b36266b6afc))
* memcached vip LU ([b56227b](https://gitlab.pepita.io/getrix/mls-site/commit/b56227bd05c3a9e4a664c15385f29a80bdd4515a))
* **property-performance:** reduce api calls of similar-properties ([66f8291](https://gitlab.pepita.io/getrix/mls-site/commit/66f8291bdb08ae3177e431d0b7db54262bc5e069))
* **settings-media:** checking feature toggle to hide some info ([f41d282](https://gitlab.pepita.io/getrix/mls-site/commit/f41d282fac74a9fc3788b5a503d0e1cfdca5a317))
* zone can be nullable ([72c78ec](https://gitlab.pepita.io/getrix/mls-site/commit/72c78ec2fde43cf99146dd4ae51997cf2a624c9f))

## [5.137.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.137.1...v5.137.2) (2024-09-16)


### Bug Fixes

* **acquisition-privates:** replaced broken checkboxes for filters ([0e0f915](https://gitlab.pepita.io/getrix/mls-site/commit/0e0f91547d956ab98506363b361d9d0279f1b0f0))
* **config:** new facebook regex into gtxConstants ([49694fb](https://gitlab.pepita.io/getrix/mls-site/commit/49694fbf0a10330245bd1d977bc0f447d56d50cc))
* **customer-care:** z-index ([76649dd](https://gitlab.pepita.io/getrix/mls-site/commit/76649dde8796e738ded90df2f00d465aecb0c4fc))
* **estimates:** handling null result for similarproperties array ([76b1d77](https://gitlab.pepita.io/getrix/mls-site/commit/76b1d7782b2242ae2e2d6078ae01da19bb2ee4da))
* **modal radio:** update modal and radio component ([0167ac9](https://gitlab.pepita.io/getrix/mls-site/commit/0167ac90bdf5887b4691433ee07fc08f6b583f26))
* **performance:** hiding improve advices in LU version ([b0da0df](https://gitlab.pepita.io/getrix/mls-site/commit/b0da0df02f775edb182bc32fddd15554a8e4ca15))

## [5.137.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.137.0...v5.137.1) (2024-09-13)


### Bug Fixes

* **gx-navigation:** cast to string customer servive phone in case of responsible is hidden ([1ff91e2](https://gitlab.pepita.io/getrix/mls-site/commit/1ff91e2bcbf25d0d8d8bd7a5efeef392316f9b90))
* **multisend:** bind contracts ddl to category ([24a9bc1](https://gitlab.pepita.io/getrix/mls-site/commit/24a9bc18463f30333c9b899b37268a404dad33b2))
* **twitter:** icon replacement ([73792ee](https://gitlab.pepita.io/getrix/mls-site/commit/73792eecd721bb7db15e125c45297c4f387be4b3))

## [5.137.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.136.1...v5.137.0) (2024-09-12)


### Features

* **sentry:** suppress some Sentry notifications ([f495b39](https://gitlab.pepita.io/getrix/mls-site/commit/f495b39caaa8f8921c72fc5be0033c59f60cafd1))


### Bug Fixes

* **gx-navigation:** fix menu rendering issue ([5196c07](https://gitlab.pepita.io/getrix/mls-site/commit/5196c0780d1b320997cf178a52639d566cefacba))
* **property-performance:** update extravisibility logic ([2ddf13e](https://gitlab.pepita.io/getrix/mls-site/commit/2ddf13ed0185525258b354887bba75b88f7b091d))

## [5.136.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.136.0...v5.136.1) (2024-09-10)

## [5.136.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.135.0...v5.136.0) (2024-09-10)


### Features

* messaging implement getThread ([7be8ecb](https://gitlab.pepita.io/getrix/mls-site/commit/7be8ecb0426206bcb34c35648c902f31a5bdd641))


### Bug Fixes

* **gx-navigation:** bad parsing of salesforce gtxConstant ([bc62bdd](https://gitlab.pepita.io/getrix/mls-site/commit/bc62bdd6f98f373a10607db0742d563e3fec23cd))

## [5.135.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.134.0...v5.135.0) (2024-09-10)


### Features

* **gx-navigation:** moving customer care ([3df100f](https://gitlab.pepita.io/getrix/mls-site/commit/3df100f3f0804f52a2d42f1e4d3722909dd9286a))
* **palette:** new colors palette ([686526e](https://gitlab.pepita.io/getrix/mls-site/commit/686526e151a99e9b41cb05fb144f89fb78f4f39c))


### Bug Fixes

* **checkbox): new prop 'indetermined'; (searches:** solved error on mobile bulk selection and sticky header ([9e043c6](https://gitlab.pepita.io/getrix/mls-site/commit/9e043c6945ebaadfc17e4007abf2e2b967ed8739))
* **color palette:** update common ([df3ccc6](https://gitlab.pepita.io/getrix/mls-site/commit/df3ccc6f3bdd29db457798b6155aa183e1c0722b))
* **email:** footer color ([5e3f1f5](https://gitlab.pepita.io/getrix/mls-site/commit/5e3f1f5beaf111285b7728227c54c8a59cc21817))
* **login:** LEGACY_MENU_ENABLED not defined in login page ([2e80341](https://gitlab.pepita.io/getrix/mls-site/commit/2e80341decfba85229bd67cc8d4ba98295c29705))
* menu counters timeout ([f42f49d](https://gitlab.pepita.io/getrix/mls-site/commit/f42f49d2165828ad17f23f85a4de005102f7f198))
* **session:** reset password of responsabile even if not exist ([96cae66](https://gitlab.pepita.io/getrix/mls-site/commit/96cae66ad70a87f49323122c8c96277ab3913cc9))

## [5.134.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.133.5...v5.134.0) (2024-09-03)


### Features

* **active-search:** action local update + mixpanel managed event ([1545729](https://gitlab.pepita.io/getrix/mls-site/commit/1545729a42536026e78e6ba83f46535a907795f9))
* **active-searches:** page load empty state logic alignment to matches page ([d2dc96f](https://gitlab.pepita.io/getrix/mls-site/commit/d2dc96f5fbb42b61b6dc382cf75600bdcefa976c))
* Add new menu item ([4050ec5](https://gitlab.pepita.io/getrix/mls-site/commit/4050ec5622ea9732e494f4aaf6acfb723792575f))
* matches and active searches no zone banner ([611a99c](https://gitlab.pepita.io/getrix/mls-site/commit/611a99ca95cfc172435def5bfbaa495f725c1330))
* **matches:** add energy and garages filters to search detail ([270903e](https://gitlab.pepita.io/getrix/mls-site/commit/270903ebb59dcc0729afaad4a8411e638ff888a5))
* **matches:** add only new checkbox in filters modal + pagination and filters contexts ([1b87f52](https://gitlab.pepita.io/getrix/mls-site/commit/1b87f527f322a3aa2fd21640e9a39f2dfd70c39a))
* **matches:** bulk success client side state update ([e3e2e3e](https://gitlab.pepita.io/getrix/mls-site/commit/e3e2e3e2e59b6ae1dea45dd5e225100e8fc72d83))
* **matches:** list toolbar filters counter ([935ab14](https://gitlab.pepita.io/getrix/mls-site/commit/935ab140ebb2528ae2bc653dc602ad337cbd5fb5))
* **session:** erase agency and agent credential when store data into the session ([5a80f3a](https://gitlab.pepita.io/getrix/mls-site/commit/5a80f3a3593ff458f83af7c92eb0f7ad2460cc02))


### Bug Fixes

* add energy and garage to active-search filters + garage trans ([b6d3ed6](https://gitlab.pepita.io/getrix/mls-site/commit/b6d3ed6a33eb9ce4108b52180b6a4667ebbb19dd))
* disable active searches and matches mocks ([46b7cbe](https://gitlab.pepita.io/getrix/mls-site/commit/46b7cbe6c3ffb2615fe342b4fda2e2fb839e6311))
* **image-editor:** z-index ([e4771ed](https://gitlab.pepita.io/getrix/mls-site/commit/e4771edd7ba7f34bf3fa040ad932004603c74403))
* **profile:** error message and snackbar on change psw error ([a06702c](https://gitlab.pepita.io/getrix/mls-site/commit/a06702c705250a774ab4c21e33e320e01fe15ed0))
* **property-performance:** showing Contacts Chart always ([d4350b0](https://gitlab.pepita.io/getrix/mls-site/commit/d4350b04e0cf154cf0040558d7cf99c7778358e7))
* **settings-users:** activate user broken page ([cb17a87](https://gitlab.pepita.io/getrix/mls-site/commit/cb17a87b2fee20fb64edb67940995ebc6d767db1))
* shows a different dialog to delete user when the logged user comes from backoffice ([2baa999](https://gitlab.pepita.io/getrix/mls-site/commit/2baa99993165978b9e2c704741906411e035bd3e))


### Refactor

* **user-view:** migration to react ([9d025d4](https://gitlab.pepita.io/getrix/mls-site/commit/9d025d44201388b2599131fe718c2b69cad7fdee))

## [5.133.5](https://gitlab.pepita.io/getrix/mls-site/compare/v5.133.4...v5.133.5) (2024-07-30)


### Bug Fixes

* **map:** changed color to new blue for polygons ([b119c43](https://gitlab.pepita.io/getrix/mls-site/commit/b119c4332fbd3940f4f61c0e0550e7cf4a45db8f))

## [5.133.4](https://gitlab.pepita.io/getrix/mls-site/compare/v5.133.3...v5.133.4) (2024-07-25)


### Bug Fixes

* **password-recovery:** remediation for user enumeration ([13b692c](https://gitlab.pepita.io/getrix/mls-site/commit/13b692cca96ae3a6f8856b1e55e53913a9d28ea1))
* **property-performance:** show correct advices ([f090f50](https://gitlab.pepita.io/getrix/mls-site/commit/f090f502314e4bbe4fab28239506b110732f635d))

## [5.133.3](https://gitlab.pepita.io/getrix/mls-site/compare/v5.133.2...v5.133.3) (2024-07-25)


### Bug Fixes

* **matches-list:** show/hide action col when needed ([5e68524](https://gitlab.pepita.io/getrix/mls-site/commit/5e685248bb8955a445435f0d3c11fc2cb152bd97))

## [5.133.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.133.1...v5.133.2) (2024-07-22)


### Bug Fixes

* **auction-catalogue:** handle missing reference on detail ([0a400d7](https://gitlab.pepita.io/getrix/mls-site/commit/0a400d72efe0d9ba92abc526444edb698a8cde9a))
* **settings-general:** populate web field only if url is not empty ([47dc10b](https://gitlab.pepita.io/getrix/mls-site/commit/47dc10bccd0f89a7f609f4a9bf802d5ac92f08f5))

## [5.133.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.133.0...v5.133.1) (2024-07-18)


### Bug Fixes

* added OriginalClientIp in login user and password requests ([5fecbe9](https://gitlab.pepita.io/getrix/mls-site/commit/5fecbe9bb0ce47d37c4edb049448d95ce162e4af))

## [5.133.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.132.2...v5.133.0) (2024-07-17)


### Features

* **gx-navigation:** add fullHash to build filename ([a0cf3f1](https://gitlab.pepita.io/getrix/mls-site/commit/a0cf3f1ac0294f7d213aef8865865865e6e8c335))
* **navigation:** scroll menu logout ([36e307f](https://gitlab.pepita.io/getrix/mls-site/commit/36e307f6e55bca3174fd04ff9e81d46fec29db67))
* **property-performance:** add label for unavailable performance ([b96094b](https://gitlab.pepita.io/getrix/mls-site/commit/b96094b5ea2d09b76f4c5e211f7a6e16eb84031a))


### Bug Fixes

* **performance:** graph spaces ([b7e686f](https://gitlab.pepita.io/getrix/mls-site/commit/b7e686f39fe9b9be2823a7aefca9566e55d662f0))
* **performance:** icona podium lista e dettaglio ([6ba4e1c](https://gitlab.pepita.io/getrix/mls-site/commit/6ba4e1c279b8f46b02947eeee3a30129c19bb1e4))
* **performance:** space cards ([127544f](https://gitlab.pepita.io/getrix/mls-site/commit/127544f81c486bd87bb983c8e5265dcad13629d4))
* **portal-properties:** cta explore statistics and performance visibility ([422c90e](https://gitlab.pepita.io/getrix/mls-site/commit/422c90e329a70ca8a118c3e377c570136796a247))
* **portal-properties:** removes some cta conditions. Changed cta tooltip text ([5aaa7ff](https://gitlab.pepita.io/getrix/mls-site/commit/5aaa7ff4782426cb5bf6c0cb8ba4c54f45ea9ab5))
* Update property stats table to display additional information ([3dc042d](https://gitlab.pepita.io/getrix/mls-site/commit/3dc042d9161c6dc32c957789d881d9aed62657f0))
* **youdomus:** remove map specific class when navigate ([6a3925e](https://gitlab.pepita.io/getrix/mls-site/commit/6a3925e81841fd0245ab2275b9f7a86a32230f2b))

## [5.132.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.132.1...v5.132.2) (2024-07-15)


### Refactor

* **property-performance:** replaced moment with date-fns ([0f993ca](https://gitlab.pepita.io/getrix/mls-site/commit/0f993ca7c8d68ed258b9741f8f1f49de03d02b9c))

## [5.132.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.132.0...v5.132.1) (2024-07-12)


### Bug Fixes

* **property-performance:** add clickable reference in stats ([73b4378](https://gitlab.pepita.io/getrix/mls-site/commit/73b437865563f2bc9233d4cb3a98a361ebfe9934))
* **property-performance:** fix visibility of button "esplora" ([859991f](https://gitlab.pepita.io/getrix/mls-site/commit/859991fa3d2903d19151ea8d3634c773ebfb6ef1))

## [5.132.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.131.2...v5.132.0) (2024-07-08)


### Features

* **youdomus:** edit iframe ([d33d30a](https://gitlab.pepita.io/getrix/mls-site/commit/d33d30a7058c1434069c801c96c5724e1730525a))


### Bug Fixes

* **match:** bulk email limit from 30 to 15 ([2f79754](https://gitlab.pepita.io/getrix/mls-site/commit/2f7975497c9ca33a84d45ae6fbb1a78d1fea202e))
* **property-performance:** show title always ([20c60a9](https://gitlab.pepita.io/getrix/mls-site/commit/20c60a908509b17e35b4f55ffdada056fe427671))
* **settings-general:** set https as default website protocol if url field is null ([f84e166](https://gitlab.pepita.io/getrix/mls-site/commit/f84e1668d4517eab246339f6b8ab4ab91fc66631))

## [5.131.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.131.1...v5.131.2) (2024-07-05)


### Bug Fixes

* **settings-general:** seperateUrlAndProtocolCheck test on input ([fc68686](https://gitlab.pepita.io/getrix/mls-site/commit/fc6868697bed1a87186a09ece460ffab2658cefa))

## [5.131.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.131.0...v5.131.1) (2024-07-05)


### Bug Fixes

* **performance:** fixed minimal 'IT' version; fixed stats table inconsistencies ([50e2a14](https://gitlab.pepita.io/getrix/mls-site/commit/50e2a1457ca14441bad5be7d4b9939872245df54))
* **property-performance:** changed column label of statistics table. Gets right rooms info ([9deae26](https://gitlab.pepita.io/getrix/mls-site/commit/9deae2612ee1c8bcb66dc78d3db6494522705396))

## [5.131.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.130.0...v5.131.0) (2024-07-03)


### Features

* Add PropertyStats component and related files ([65c7b3f](https://gitlab.pepita.io/getrix/mls-site/commit/65c7b3f0bb3e3296f1490f708b396e012fe80e14))


### Bug Fixes

* **settings-users:** right js bundle incluse ([3ab4fb4](https://gitlab.pepita.io/getrix/mls-site/commit/3ab4fb422c90fce1611dd022b116d632c67e86b0))

## [5.130.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.129.2...v5.130.0) (2024-07-03)


### Features

* aggiunte metriche per mail inviate ([c9417f1](https://gitlab.pepita.io/getrix/mls-site/commit/c9417f1f9e7e5a48d50cecd660d33dbc4321335f))
* **zone-packages:** silver package on ([a528c50](https://gitlab.pepita.io/getrix/mls-site/commit/a528c508065b881d52c4dcaac3e3e6dd4ee07bd0))


### Bug Fixes

* **agency-estimates:** reading code value on reportDailyLimit ([82841f7](https://gitlab.pepita.io/getrix/mls-site/commit/82841f736f99399a65e191a9db25917751b0aa40))
* **property-performance:** adds existance test on rankingChartTooltip points ([02d2d2e](https://gitlab.pepita.io/getrix/mls-site/commit/02d2d2e1ec13ba884dad0a81aa2a92758f7affd4))

## [5.129.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.129.1...v5.129.2) (2024-06-25)


### Bug Fixes

* gx-nav hardcoded cache bust ([39c7695](https://gitlab.pepita.io/getrix/mls-site/commit/39c7695a3f84c61fc14562e17a250d8a6e1e9879))

## [5.129.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.129.0...v5.129.1) (2024-06-25)


### Bug Fixes

* refactor revert cause types break ([6626d56](https://gitlab.pepita.io/getrix/mls-site/commit/6626d56f56b9f587bcd6c7e9c14bea976fbe495f))

## [5.129.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.128.1...v5.129.0) (2024-06-25)


### Features

* active searches + silver zone package + active search menu item and client reorder ([7d8deeb](https://gitlab.pepita.io/getrix/mls-site/commit/7d8deeb70a1cc682f91f1fdf26018811d03f6534))
* **matches:** split send matches api call in chunks ([51ff2c4](https://gitlab.pepita.io/getrix/mls-site/commit/51ff2c46b7acb3a9bcc277f9db8ebc64665c47ed))


### Bug Fixes

* **active-searches:** remove initial data pagination break + assignment refactor ([261f6ea](https://gitlab.pepita.io/getrix/mls-site/commit/261f6ea011db51e3c9d260dad79b8900ccb7f607))
* **property-performance:** property_id property on main prope main property mixpanel event ([49d655c](https://gitlab.pepita.io/getrix/mls-site/commit/49d655c4a6a4bdc2e0b94da9756298d9650ff1a6))

## [5.128.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.128.0...v5.128.1) (2024-06-21)


### Bug Fixes

* **config:** wrong cdn for the Luxembourg ([d8edf4b](https://gitlab.pepita.io/getrix/mls-site/commit/d8edf4bf09e2cea03f3fe4cad46fdad5adaf67da))
* **config:** wrong value of AGENCY_REGISTRATION_VAT_FIELD ([c06c045](https://gitlab.pepita.io/getrix/mls-site/commit/c06c04527ef33dc7c465eaf22ca723169cfa2f44))
* **deploy:** set it3 tags for es ([afb6696](https://gitlab.pepita.io/getrix/mls-site/commit/afb6696eb0432fabef30a55f34182baa4f0be160))

## [5.128.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.127.0...v5.128.0) (2024-06-19)


### Features

* **performance-match:** Ad Detail modal ([daf1675](https://gitlab.pepita.io/getrix/mls-site/commit/daf1675e556c4576bdcf19e8db7e99131977b262))


### Bug Fixes

* checked visibile match field when sending email to customer ([63ec872](https://gitlab.pepita.io/getrix/mls-site/commit/63ec872c55c1b397ec211b00c539dd30b494aa09))
* **matches:** adds property id to AdResponse response model ([e1483de](https://gitlab.pepita.io/getrix/mls-site/commit/e1483de971148a6d2808443d02808ee1c09adf2a))
* **matches:** avoid to open detail modal for deleted ads ([927a645](https://gitlab.pepita.io/getrix/mls-site/commit/927a645ab84999cd5d2b05b637d896c8d4fdcdd4))
* **property-performance:** fix list filtering ([46805b9](https://gitlab.pepita.io/getrix/mls-site/commit/46805b9eaee2f9013f463376b3584881fde06a25))
* **settings-general:** strreplace on descriptions ([508484c](https://gitlab.pepita.io/getrix/mls-site/commit/508484cf6cb1de2aa9547c58897fd8378852c852))
* **youdomus:** removes old menù sections handling ([56cb38f](https://gitlab.pepita.io/getrix/mls-site/commit/56cb38fcaf9fff2f3e72e749366d942876d2d33e))

## [5.127.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.126.0...v5.127.0) (2024-06-14)


### Features

* Add initialData set on GtxApp without redux + type fixes explicit page type declaration ([6dd2fd3](https://gitlab.pepita.io/getrix/mls-site/commit/6dd2fd32fcbe7b4f7dba7890556629a26eaf2b24))
* **agency:** new endpoint for the agency logo ([95c34c2](https://gitlab.pepita.io/getrix/mls-site/commit/95c34c2cf77f626713ad2040e0774e84eded074b))


### Bug Fixes

* makes name and surname not mandatory into loggedUserSchema ([821fb18](https://gitlab.pepita.io/getrix/mls-site/commit/821fb18aac1b5bbb06643fccaab7a6e9168b3a2e))


### Documentation

* contributing docker mode edits ([7ac6df2](https://gitlab.pepita.io/getrix/mls-site/commit/7ac6df20687650deb69ecb58b1ecd61cdfe4d8d3))
* contributing path typo ([2334259](https://gitlab.pepita.io/getrix/mls-site/commit/2334259acae931a6abf5f301c9947eb480516644))

## [5.126.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.125.1...v5.126.0) (2024-06-12)


### Features

* migrate settings users ([adb3470](https://gitlab.pepita.io/getrix/mls-site/commit/adb34706f95e896324613b387a5551b097d64688))


### Bug Fixes

* **MapFormatter:** checked if agency is in session ([2160380](https://gitlab.pepita.io/getrix/mls-site/commit/21603803e849736e3ebb8637bbea9df8bb305f19))
* **user-list:** role select mobile ([4033641](https://gitlab.pepita.io/getrix/mls-site/commit/4033641a45c3a72b6e013ea5bc50de92c2171d49))

## [5.125.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.125.0...v5.125.1) (2024-06-11)


### Bug Fixes

* typo errors ([1685e0f](https://gitlab.pepita.io/getrix/mls-site/commit/1685e0f646f8d2b44860b0f84a5b72a97f4cbbbf))

## [5.125.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.124.0...v5.125.0) (2024-06-06)


### Features

* **agency-estimate:** use v2 of the api endpoint that returns estimate details ([5112035](https://gitlab.pepita.io/getrix/mls-site/commit/51120353ca321be8384ca6a29d8b501bcd7a162d))

## [5.124.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.123.0...v5.124.0) (2024-06-06)


### Features

* **agency-active-search:** added API structures and endpoint ([e51d84f](https://gitlab.pepita.io/getrix/mls-site/commit/e51d84f1fbcad1c7ef5964ee0471c7e41c7c1ea4))
* **agency-active-search:** added endpoint for Agency Active Search stats ([c0f1370](https://gitlab.pepita.io/getrix/mls-site/commit/c0f13700267ce6c18a09a9ffb173deef260effc7))
* **agency:** call v2 api of the ecommerce-products endpoint ([e1785a1](https://gitlab.pepita.io/getrix/mls-site/commit/e1785a189658be50cc641747f748566f828a76f7))
* **messaging:** convert moment to date-fns ([b4b9968](https://gitlab.pepita.io/getrix/mls-site/commit/b4b9968ccaf02924f33c9d3e31b59bf9984e4bc5))

## [5.123.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.122.1...v5.123.0) (2024-06-04)


### Features

* **gx-navigation:** removes old navigation from IT ([a28014f](https://gitlab.pepita.io/getrix/mls-site/commit/a28014f55b1bbc1cc31ca238ec7c6d5ddfe2b10e))


### Bug Fixes

* **react-query:** bad useMutation usage ([efe37e1](https://gitlab.pepita.io/getrix/mls-site/commit/efe37e16e97a230e7ac104e660b780faf2dadfa8))

## [5.122.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.122.0...v5.122.1) (2024-06-04)

## [5.122.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.121.1...v5.122.0) (2024-05-31)


### Features

* **messages:** copy contact info to clipboard ([8b0f3ab](https://gitlab.pepita.io/getrix/mls-site/commit/8b0f3ab7beb957c1ab593dd3d1a8e5a3968ae766))
* **searches:** added Stats annotations to controllers ([d4062aa](https://gitlab.pepita.io/getrix/mls-site/commit/d4062aa5f18115c26a8a23391ada589165669097))


### Bug Fixes

* **agency-estimates:** removed dangerous useEffect which caused infinite re-renders ([f6ab29b](https://gitlab.pepita.io/getrix/mls-site/commit/f6ab29b19c392b6cfe226901df81602fb2eea147))
* **gx-navigation:**  header labels for set pprovinces pages ([68f56ae](https://gitlab.pepita.io/getrix/mls-site/commit/68f56ae22db1cfabb4ecaf44e1e2794684bc235f))


### Refactor

* **lodash:** replaced sparse usage in common files and some sections ([bdaef3c](https://gitlab.pepita.io/getrix/mls-site/commit/bdaef3ce0cdcd4b14d1d0245679f8d3f6d93f290))
* Update createQueryClient to accept partial QueryClientConfig ([24c221a](https://gitlab.pepita.io/getrix/mls-site/commit/24c221adbbf0986b0230ea37d451390d3193d529))

## [5.121.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.121.0...v5.121.1) (2024-05-29)


### Bug Fixes

* **build:** adds [@tanstack](https://gitlab.pepita.io/tanstack) to transpiled modules list ([3ea6b7d](https://gitlab.pepita.io/getrix/mls-site/commit/3ea6b7d4216cffb672e21b61de6cb56ec7f8cf29))
* pdf download ([45019a9](https://gitlab.pepita.io/getrix/mls-site/commit/45019a95b3d9a176967d5d46511fca9000280d1e))

## [5.121.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.120.1...v5.121.0) (2024-05-29)


### Features

* introduce react-query 5 ([8ace771](https://gitlab.pepita.io/getrix/mls-site/commit/8ace771b9efa8966032dcf39e0a14d6b70c1a057))
* **sales-requests:** replace moment with datefns ([4be61e9](https://gitlab.pepita.io/getrix/mls-site/commit/4be61e91ce2ba5ce40919cb9fee083c49fe7f464))


### Bug Fixes

* **build:** exludes date-fns module content because of old safari versions ([5d94103](https://gitlab.pepita.io/getrix/mls-site/commit/5d9410315c58296751157037631f85610063ddc7))

## [5.120.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.120.0...v5.120.1) (2024-05-27)


### Bug Fixes

* **sales-requests:** avoiding crash on card list when translation labels are missing ([52cde77](https://gitlab.pepita.io/getrix/mls-site/commit/52cde777c3fe95acd1d9fafc894f5bfab95024a0))
* **settings-general:** fax field name ([8794f24](https://gitlab.pepita.io/getrix/mls-site/commit/8794f24bea0057cd5a63142244feaf380ded7c7f))

## [5.120.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.119.0...v5.120.0) (2024-05-23)


### Features

* **data-provider:** added method to get api headers with author-id ([4e2bf60](https://gitlab.pepita.io/getrix/mls-site/commit/4e2bf60f05d819aeeea1b823ad2356ab75583e27))
* **matches:** added search property condition filter ([c823760](https://gitlab.pepita.io/getrix/mls-site/commit/c8237600bcb945e8683f902a4d22728fdfb421d1))


### Bug Fixes

* fixed locale from agency language ([82981eb](https://gitlab.pepita.io/getrix/mls-site/commit/82981eb67ab3511379fe580f8bf1d6fbefc6a1a8))
* **IvSchModal:** fixed check on guests, fixed initial values generation to handle some crashes ([048dd9e](https://gitlab.pepita.io/getrix/mls-site/commit/048dd9e76cca3ab8b3f739d95d783c4ba16b950d))
* **matches:** search filters popover open to bottom ([50d639b](https://gitlab.pepita.io/getrix/mls-site/commit/50d639bad84c37a474af36190679413e28caa2e8))


### Refactor

* **multisend-portals:** replaced all lodash occurencies, minor refactor for ts issues ([6815eca](https://gitlab.pepita.io/getrix/mls-site/commit/6815ecab1d711f82dc5ae6696ce6d8a095c8fed9))

## [5.119.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.118.0...v5.119.0) (2024-05-21)


### Features

* added internal endpoint to set matches to sent ([136e476](https://gitlab.pepita.io/getrix/mls-site/commit/136e476f8bcdec39a4f585da3b395290932b25d4))
* **table-data:** add style gx-design ([a990e64](https://gitlab.pepita.io/getrix/mls-site/commit/a990e6444604315d5c0a0eb6fb785ed11aedd137))
* update translations ([423b497](https://gitlab.pepita.io/getrix/mls-site/commit/423b49721f8b8974e33a3d4265a7e6a933f76fda))


### Bug Fixes

* **CustomerForm:** geofield-selects disabled when api has errors ([851b18d](https://gitlab.pepita.io/getrix/mls-site/commit/851b18d7949fa619b95d469b33e308ab3b722ea4))
* **datepicker:** handling undefined/null values to parse ([efa34f1](https://gitlab.pepita.io/getrix/mls-site/commit/efa34f1d96f9ffabeed5c4c44b24d93418104b20))
* **menu:** set highlight for zone page ([91471a4](https://gitlab.pepita.io/getrix/mls-site/commit/91471a44b595bf1d0175bb8c55213970eb175e93))
* **registration:** piva field wasn't nullable ([52445be](https://gitlab.pepita.io/getrix/mls-site/commit/52445be900cba10e05a108842d832d2c097b5928))
* **signup:** vatNumber toggle flag ([f484695](https://gitlab.pepita.io/getrix/mls-site/commit/f48469584d37abb17bc34b2c00bb75a04b4a14bf))


### Refactor

* **CustomerForm:** replaced moment with date-fns ([6ff3761](https://gitlab.pepita.io/getrix/mls-site/commit/6ff37613cb7217259b4e7e6b0c68d9742b04ae76))

## [5.118.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.117.1...v5.118.0) (2024-05-16)


### Features

* **menu:** removed zone from settings ([28a4632](https://gitlab.pepita.io/getrix/mls-site/commit/28a4632ae4cb3573ade0354dce02c1eb470d9863))


### Bug Fixes

* **matches:** getSmartCounterString handle null value ([abe3f70](https://gitlab.pepita.io/getrix/mls-site/commit/abe3f705fc661acb91086c9c84139fe72688f9d7))

## [5.117.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.117.0...v5.117.1) (2024-05-16)


### Bug Fixes

* **agency-estimate:** preventing crashes on step 4 with similar properties ([a03fb44](https://gitlab.pepita.io/getrix/mls-site/commit/a03fb4494295bfd97c2fac67a202474d4b027a4f))
* **property-performance:** improve property performance advices ([0df5a0a](https://gitlab.pepita.io/getrix/mls-site/commit/0df5a0aa96c4a04eb3a359a3e34648af9783f3a3))


### Refactor

* removing GtxSingleDatePicker / fix(IvSchModal) ([9242623](https://gitlab.pepita.io/getrix/mls-site/commit/92426233c09d86d3a1000a147aa55f4e948599cc))

## [5.117.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.116.0...v5.117.0) (2024-05-15)


### Features

* **property-performance:** update useAdvices hook to fix property totals calculation ([654a852](https://gitlab.pepita.io/getrix/mls-site/commit/654a8522b0a4bd5b166102cccccd649ca8363cde))


### Bug Fixes

* **settings-security:** improve controlle response ([8977864](https://gitlab.pepita.io/getrix/mls-site/commit/8977864171ea887f3453329fe4e3acb39549d37e))

## [5.116.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.115.1...v5.116.0) (2024-05-14)


### Features

* **property-performance:** add advices ([a14564c](https://gitlab.pepita.io/getrix/mls-site/commit/a14564c12c75a9f3624cb8be1cd78c7886f96d45))


### Bug Fixes

* aggiornati puntamenti produzione spagna ([f1db281](https://gitlab.pepita.io/getrix/mls-site/commit/f1db281ff0f8cafebd8a6f2e977a066be3491687))
* aggiornati puntamenti produzione spagna ([7ea913e](https://gitlab.pepita.io/getrix/mls-site/commit/7ea913efab6fbdc55360a15c7895d2f0687c8676))
* **dashboard:** adds condition for matches counter api ([c9fa5e0](https://gitlab.pepita.io/getrix/mls-site/commit/c9fa5e07271a23a03ef5e39d36470dde23e0af57))
* **dashboard:** matches counter without smart format ([b2dae76](https://gitlab.pepita.io/getrix/mls-site/commit/b2dae7641cfd946d657291133e1f1b6d05efd620))

## [5.115.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.115.0...v5.115.1) (2024-05-10)

## [5.115.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.114.0...v5.115.0) (2024-05-10)


### Features

* **immobiliare-list:** match column on new costructions ([93bb4c2](https://gitlab.pepita.io/getrix/mls-site/commit/93bb4c2c36a2b6ef8d2818c0f50da7802a70eb47))


### Bug Fixes

* **gx-navigation:** beamer_news check ([fd2b98a](https://gitlab.pepita.io/getrix/mls-site/commit/fd2b98a793cbaca8031d5a24a4d0d495e6ff0151))
* **profile:** replaced and fixed loader ([b888a3a](https://gitlab.pepita.io/getrix/mls-site/commit/b888a3a025f2e3d6a424dedddb891dd82cc2cbf8))
* **reseatch:** autocomplete dropdown ([69bad71](https://gitlab.pepita.io/getrix/mls-site/commit/69bad7110e06e6501ee94471fb4129e8027338ac))

## [5.114.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.113.2...v5.114.0) (2024-05-08)


### Features

* **beamer:** enabling it for ImmoTop ([27548ce](https://gitlab.pepita.io/getrix/mls-site/commit/27548ce3951fed52f9dc03fc119f45ea6c87b219))
* **immovisita-scheduled:** change from moment to date-fns ([226a993](https://gitlab.pepita.io/getrix/mls-site/commit/226a99348dd85203f088c492a4ada9eb78794120))


### Bug Fixes

* corretti puntamenti spagna produzione ([0f8c50f](https://gitlab.pepita.io/getrix/mls-site/commit/0f8c50f30991ddbc36cf298c8ab2585151b61a7d))
* **matches:** appcues windows bug ([4d24e15](https://gitlab.pepita.io/getrix/mls-site/commit/4d24e15f27c80980f7270aa062d7963922f87c10))
* **matches:** typologies from v2 to v1 ([543963e](https://gitlab.pepita.io/getrix/mls-site/commit/543963e76697659bd6b503c039d22655a0f33e55))


### Refactor

* **acquisition-privates:** replaces Dialog component with gx-design Modal ([4d6129d](https://gitlab.pepita.io/getrix/mls-site/commit/4d6129d9bf0e8460d6b6a96d6fd2270dd92d8a28))
* **customers:** replaces gtx-react Modal with gx-design Modal ([98d9e3f](https://gitlab.pepita.io/getrix/mls-site/commit/98d9e3f401f3ed12671ded92ecf4f292d1aa3871))
* **modal:** replaces remaining occurence of gtx-react Modal with gx-design Modal ([a0e68c1](https://gitlab.pepita.io/getrix/mls-site/commit/a0e68c1cba3fb342b005da67eccd468ccbd46523))
* **searches:** replaced moment with date-fns ([f79865f](https://gitlab.pepita.io/getrix/mls-site/commit/f79865f1ec0a5532240736e39392961b5171190c))

## [5.113.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.113.1...v5.113.2) (2024-05-02)


### Bug Fixes

* **portal-auctions:** pagination error on search ([f9b1570](https://gitlab.pepita.io/getrix/mls-site/commit/f9b1570ca411dfe331adbe3fbed4bed076c8766a))

## [5.113.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.113.0...v5.113.1) (2024-05-02)


### Bug Fixes

* **billboard:** photo edit ([f0c3d52](https://gitlab.pepita.io/getrix/mls-site/commit/f0c3d521526f276e46d936be81d15e00944bc89e))


### Refactor

* **portal-auctions:** replaces gtx-react Modal with gx-design Modal ([94065ad](https://gitlab.pepita.io/getrix/mls-site/commit/94065ad76edfeb86306fc03c994c0fe730e67371))
* **searches:** replaces gtx-react Modal with gx-design Modal ([37d38b2](https://gitlab.pepita.io/getrix/mls-site/commit/37d38b2d476575b422336bba7b72b0dbe80d872b))

## [5.113.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.112.1...v5.113.0) (2024-05-02)


### Features

* **dashboard:** switch from generic contacts to matches with counter ([36c0b42](https://gitlab.pepita.io/getrix/mls-site/commit/36c0b425203ff8c4c3a7baef77a6403bca5706fd))
* **match:** immobiliare.it match column on Annunci and Aste ([c4ea0b0](https://gitlab.pepita.io/getrix/mls-site/commit/c4ea0b0bb23d3419bcda6cde4c9415cfc8a8abbe))
* **mixpanel:** added configuration for the Luxembourg ([ee4cdb8](https://gitlab.pepita.io/getrix/mls-site/commit/ee4cdb87c4b93f849ce754682149131e4193446e))
* **searches:** conditional licenses select + fix preselect on one choice ([e53708e](https://gitlab.pepita.io/getrix/mls-site/commit/e53708ef2f5caf5a13c5fd1adc5704ff68b7e21d))


### Bug Fixes

* **billboard:** handle proper close of some Modals ([c995d3f](https://gitlab.pepita.io/getrix/mls-site/commit/c995d3f31df0a0f5778ec580ae2d7bbe323db981))
* **billboard:** images modal ([773752c](https://gitlab.pepita.io/getrix/mls-site/commit/773752c8bd698d0f6f533709a708019ab3abda65))
* **dashboard:** matches label typo + total counter instead of only new ones ([8fa8f55](https://gitlab.pepita.io/getrix/mls-site/commit/8fa8f55b5d754870d29a14da7c8a88782a8012cb))
* **immobiliareit-lists:** adds match and matches counters columns when paginate ([6eef3dd](https://gitlab.pepita.io/getrix/mls-site/commit/6eef3dd37aa6056b66450a54738515f51f148d0e))
* **portal-properties-auctions:** list counter, zeros if no results for matches and messages ([6c56a7b](https://gitlab.pepita.io/getrix/mls-site/commit/6c56a7b609cf6e78bdfc5f562a1f26c729fa7a97))


### Refactor

* **administration-invoices:** replaced moment with date-fns ([16a3595](https://gitlab.pepita.io/getrix/mls-site/commit/16a359545206b6845904550a09af5857e2c3a8c9))
* **gtx-app:** replaces gtx-react Notify with gx-design/snackbar into RTKNotify ([1ee4d15](https://gitlab.pepita.io/getrix/mls-site/commit/1ee4d15e31786a34013215e9a289e0e817288cb1))
* **immovisita-scheduled:** replaces gtx-react Modal with gx-design Modal ([2f05330](https://gitlab.pepita.io/getrix/mls-site/commit/2f05330e6946962e258a250ce5a9c493625ce944))
* **messagging:** replaces gtx-react Modal with gx-design Modal ([a04a483](https://gitlab.pepita.io/getrix/mls-site/commit/a04a483153dec7f4fd580f980d4c3b321c077bc5))
* **multisend:** replaces ads occurrence with properties in some endpoint s query string ([fe20f9e](https://gitlab.pepita.io/getrix/mls-site/commit/fe20f9ea0374ee8894c7ffaff40230d1adb9990a))
* **sales-requests:** replaces gtx-react Modal with gx-design Modal ([800ebcb](https://gitlab.pepita.io/getrix/mls-site/commit/800ebcbef6763ee455b612aabba25a17605c8f13))
* **settings-remote-visits:** replaces gtx-react Modal with gx-design Modal ([2ce2642](https://gitlab.pepita.io/getrix/mls-site/commit/2ce264269a62b7553cb09fcd57af98e33e451c92))
* **youdomus:** replaces gtx-react Modal with gx-design Modal ([3491b66](https://gitlab.pepita.io/getrix/mls-site/commit/3491b665e59b947d4db73c20c1d4715701c272bb))

## [5.112.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.112.0...v5.112.1) (2024-04-23)


### Refactor

* **ranking-modal:** replaces gtx react useNotifyContext with gx-design/snackbar useNotifyContext ([5758769](https://gitlab.pepita.io/getrix/mls-site/commit/57587693ba01d74f9ddfba9ceb4c27d0e641dd3c))

## [5.112.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.111.0...v5.112.0) (2024-04-23)


### Features

* update translations ([340cf71](https://gitlab.pepita.io/getrix/mls-site/commit/340cf71b42fdf2b45660e612eacb7260fdbfe737))


### Bug Fixes

* aggiornati puntamenti macchine produzione spagna ([0c1c283](https://gitlab.pepita.io/getrix/mls-site/commit/0c1c283dbf44684cea4e3bda007147a036ec3d64))
* **matches:** bulk actions tooltips ([6ed5b25](https://gitlab.pepita.io/getrix/mls-site/commit/6ed5b25fd787727b393eb3ebb19946ee92559f7c))
* **multisend:** button alignment export ([1e6817c](https://gitlab.pepita.io/getrix/mls-site/commit/1e6817ccaadab8fcf6c37444a283a1b8b1c4f41c))
* **popover:** popover and tooltip remove exit animation ([f090bbb](https://gitlab.pepita.io/getrix/mls-site/commit/f090bbb637d3c2c1f1bd5f246ff42d51a2c4c88a))
* **radio:** style import ([13a933a](https://gitlab.pepita.io/getrix/mls-site/commit/13a933a971e5661840a525af946a207197ba4437))
* **report:** footer new menu ([52ca80b](https://gitlab.pepita.io/getrix/mls-site/commit/52ca80bd37b297cec403063cb0bb79e358e6c97c))


### Refactor

* **auctions-catalogue:** replaces gtx-react Modal with gx-design/Modal ([3a8fd08](https://gitlab.pepita.io/getrix/mls-site/commit/3a8fd08a798d487cf7e1f996c128459b1281d508))
* **billboard:** replaces gtx-react Modal with gx-design Modal ([775d9e6](https://gitlab.pepita.io/getrix/mls-site/commit/775d9e6b71ee1d55f1b179a805870896e358295f))
* **portal-auctions:** replaces gtx-react Notify with gx-design/NotifyProvider ([7084d69](https://gitlab.pepita.io/getrix/mls-site/commit/7084d691b9a1ccc0c43dbdefe58707535218b4c9))
* **portal-new-constructions:** replaces gtx-react Notify with gx-design/Snackbar ([433c2a1](https://gitlab.pepita.io/getrix/mls-site/commit/433c2a1ce4909f705a0fab6743fbc59b8a9a2654))
* **portal-properties:** replaces gtx-react Notify with gx-design/Snackbar ([7bb1b17](https://gitlab.pepita.io/getrix/mls-site/commit/7bb1b177c127a6985089d9520f3d0b4398d13389))

## [5.111.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.110.0...v5.111.0) (2024-04-18)


### Features

* **customers:** created new namespace customers with api endpoint ([5427a1c](https://gitlab.pepita.io/getrix/mls-site/commit/5427a1c8762b5931658d3ef8cf3e2d95241f54a2))
* **matches:** add additional tags to mail ([fd668df](https://gitlab.pepita.io/getrix/mls-site/commit/fd668dfbd01a219f4e2861ee12646aac95087174))


### Bug Fixes

* **matches:** costomer autocomplete switch to new api ([09d9927](https://gitlab.pepita.io/getrix/mls-site/commit/09d9927c4c9b4a89d8d8848fd98c8df393a8b8c7))
* **matches:** counter update on partial action success ([51c9992](https://gitlab.pepita.io/getrix/mls-site/commit/51c999226b9b97ec532b90f53b50759a24e05c40))
* **match:** mail match tags frontend ([98b5df1](https://gitlab.pepita.io/getrix/mls-site/commit/98b5df18bc4daf32c7bb1aa58e2b5806ea983b57))
* **multiselect:** overflow scroll ([c1ccf47](https://gitlab.pepita.io/getrix/mls-site/commit/c1ccf47817813f82baf41c8ff35e5e52c92ab484))
* **performance:** action buttons ([c1ee0a7](https://gitlab.pepita.io/getrix/mls-site/commit/c1ee0a7b42116f904129806527aa6ef7c10a20d0))
* **portal-auctions:** trigger detail modal open only on properties references and image ([94147b1](https://gitlab.pepita.io/getrix/mls-site/commit/94147b1b2e27cab207f342085193377d38860fc5))
* **portal-new-constructions:** trigger detail modal open only on properties references and image ([6995424](https://gitlab.pepita.io/getrix/mls-site/commit/69954246b0627966ad8e1042445594444bb37ea2))
* **portal-properties:** trigger detail modal open only on properties references and image ([fe1f771](https://gitlab.pepita.io/getrix/mls-site/commit/fe1f771ae22e0768834e1ad98ac43dea874f9d36))

## [5.110.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.109.0...v5.110.0) (2024-04-15)


### Features

* **matches:** add typologies popover to search field ([aee24c0](https://gitlab.pepita.io/getrix/mls-site/commit/aee24c08cfe6a8edee860b3f4d82318e4eadc9d6))
* **menu:** new menu as default ([4b0ccf7](https://gitlab.pepita.io/getrix/mls-site/commit/4b0ccf75d18fe53b1003a73a1da137d311f6b518))
* **tag:** implementato tag messaggistica ([f4711ca](https://gitlab.pepita.io/getrix/mls-site/commit/f4711cac8bdc84b28d8b29eac0c7222ff4581c88))


### Bug Fixes

* **dashboard:** specific request typology trans mapping ([828d96a](https://gitlab.pepita.io/getrix/mls-site/commit/828d96af1d6ed44096f5e4de52e7dd3a575ac419))
* **menu:** notification ([904affb](https://gitlab.pepita.io/getrix/mls-site/commit/904affbdae743f7e28a115483b3d598bc1c3b454))
* **performance:** fixed scroll to charts with new hook useScrollToElement ([476e9b6](https://gitlab.pepita.io/getrix/mls-site/commit/476e9b6e79f70b45f47be93462fb52e2d51740e9))

## [5.109.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.108.1...v5.109.0) (2024-04-09)


### Features

* **agency-estimates:** replace moment with date-fns ([b6c6f1a](https://gitlab.pepita.io/getrix/mls-site/commit/b6c6f1a4c018d6131d5b57c479749c81dad1d6d5))
* chaged mailer url es-stg ([b73883f](https://gitlab.pepita.io/getrix/mls-site/commit/b73883f3bd8d13a5a47f1d84c453bf02a5b21db2))
* **matches:** removed empty state if agency has not getrixPlus ([8df9efe](https://gitlab.pepita.io/getrix/mls-site/commit/8df9efeca8aa54d05ce033f61a20682c9b723a6f))
* **menu:** enabled parsing of php constants ([7622207](https://gitlab.pepita.io/getrix/mls-site/commit/76222075599134c36bddb702c1a9af1c9760863b))
* **metrics:** metrics configuration setup ([94b898a](https://gitlab.pepita.io/getrix/mls-site/commit/94b898ae42d0e192aa61aef116bdedb1cc5cdc31))
* **registration:** adding piva/vat field ([6c916df](https://gitlab.pepita.io/getrix/mls-site/commit/6c916df678056dcae3e6ef4abb2cc911945f09f3))
* sync gx navigation agency info ([fa654a2](https://gitlab.pepita.io/getrix/mls-site/commit/fa654a2353a7a277f515f6bd6261466516f0e90a))
* update date-fns to v3 ([d485886](https://gitlab.pepita.io/getrix/mls-site/commit/d48588618bc0d88dfb583a7847e3849aa1002a67))
* update translations ([3585340](https://gitlab.pepita.io/getrix/mls-site/commit/35853409e71ad1565e2302afce8da83138395af3))


### Bug Fixes

* **acquisition-estimates:** popover use on user searches component ([ec971f8](https://gitlab.pepita.io/getrix/mls-site/commit/ec971f83dd0a72d71c60c4ff308f1a3d4f4172ee))
* **immotop:** immovisita fix provvisorio ([036c89f](https://gitlab.pepita.io/getrix/mls-site/commit/036c89f57c91f5a7fe84527833ddaa085d2000b3))
* **notify:** add style and button fix ([36f0b8e](https://gitlab.pepita.io/getrix/mls-site/commit/36f0b8e8e994bd3f8e087d630e2aa3b0764fdc20))
* **summary-list:** aggiunto stile ([43554d4](https://gitlab.pepita.io/getrix/mls-site/commit/43554d4259d2c25015d03b4b96ba78443705725e))


### Refactor

* **messaging:** replaces Notify componentwith gx-design/Snackbar ([0299ccd](https://gitlab.pepita.io/getrix/mls-site/commit/0299ccd3427f4ac04e1078f9fae02fc780df9c0e))

## [5.108.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.108.0...v5.108.1) (2024-04-03)


### Bug Fixes

* **button:** remove styl prop from buttons ([f175a96](https://gitlab.pepita.io/getrix/mls-site/commit/f175a965ba67ea671ec1078d37b8810da7f73ca2))

## [5.108.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.107.1...v5.108.0) (2024-04-03)


### Features

* **button:** component changes ([2b7b241](https://gitlab.pepita.io/getrix/mls-site/commit/2b7b241610a6005e77f659e2d05d626363231d59))
* **config:** use specific credential of Getrix api for Spain ([f0a0de1](https://gitlab.pepita.io/getrix/mls-site/commit/f0a0de15251bbe0d6b561fb5191202cf79db92b3))
* identify a user into dynatrace RUMS ([57c1b30](https://gitlab.pepita.io/getrix/mls-site/commit/57c1b303da1bc2e9ec9b23cfa36e221b6fb736de))
* **menu:** remove Settings/Contract item for the Luxembourg ([78159f4](https://gitlab.pepita.io/getrix/mls-site/commit/78159f4b41e4dcdf6c35a0b850b50c0830d44d01))
* **registration:** save 'piva' field ([1ebde5c](https://gitlab.pepita.io/getrix/mls-site/commit/1ebde5c9a5af93040b0c676c758c3ecd8402fefd))


### Bug Fixes

* **agency-estimates:** bad exposure pre-checking on step 3 causing duplicates and pois graphical bug ([e4d2b4b](https://gitlab.pepita.io/getrix/mls-site/commit/e4d2b4b602ca5ea4dd6921e4d2456e82e917052f))
* **agency-estimates:** Impossibile completare un REPORT PRO saltando dallo step 5 a uno degli step precedenti ([c8918a3](https://gitlab.pepita.io/getrix/mls-site/commit/c8918a3e8685345e44bf7c661c9580138d49ecd3))
* **button:** loader login and menu header ([b267e76](https://gitlab.pepita.io/getrix/mls-site/commit/b267e76fb2d9d09d9721923b44f77417eb5a56ed))
* **buttons:** performance e navigation ([f9e3276](https://gitlab.pepita.io/getrix/mls-site/commit/f9e32762d66c927f7666d3abc56317cf17fdaedf))
* **error:** error + modal error ([3a4aa4a](https://gitlab.pepita.io/getrix/mls-site/commit/3a4aa4a7cd7f5817217759822e593022c435ac3a))
* **property:** return 404 instead of 500 response code when request ad... ([650fe27](https://gitlab.pepita.io/getrix/mls-site/commit/650fe275cce855aaf39126d3580d00e16f0fae02))

## [5.107.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.107.0...v5.107.1) (2024-03-27)


### Bug Fixes

* **immopro:** menu color ([034717e](https://gitlab.pepita.io/getrix/mls-site/commit/034717e780084fe756ab52f0750694312a73dd5f))


### Refactor

* **matches:** removing filters force if agency has no zones ([f97bf3d](https://gitlab.pepita.io/getrix/mls-site/commit/f97bf3d1a8e279b2caa1ce2ce68daff61aa7f17b))

## [5.107.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.106.2...v5.107.0) (2024-03-27)


### Features

* **matches:** send stats count on matches bulk sent ([2bdee76](https://gitlab.pepita.io/getrix/mls-site/commit/2bdee76ccacabf3543749f178556240b4970c7f8))
* **scss:** new architecture ([8822b61](https://gitlab.pepita.io/getrix/mls-site/commit/8822b61508362b267874286ad1b1a6bd2935b9bf))


### Bug Fixes

* **customers:** missing trans module ([70d039d](https://gitlab.pepita.io/getrix/mls-site/commit/70d039defb666afbecc1abcce61154d1914cb0da))
* **multinvio:** add table css ([0dcc624](https://gitlab.pepita.io/getrix/mls-site/commit/0dcc624937a7286e080784cfeaf668fe95d2c887))
* **portal-new-construction:** promote cta now links to step 5 instead 4 ([16cdcd9](https://gitlab.pepita.io/getrix/mls-site/commit/16cdcd98bb1bb8ce0db3b7fbf7426cd8074d95ba))


### Test

* premiumSpaces and ContractVisibilitySpaces ([2fcb180](https://gitlab.pepita.io/getrix/mls-site/commit/2fcb1800383d1d4d037b4e955aa607400f33010b))


### Refactor

* **acquisition-privates:** replaces gtx-react Modal with gx-design Modal ([d72dd1b](https://gitlab.pepita.io/getrix/mls-site/commit/d72dd1bb5b1c53a02c88f99c2610c029ccf908a5))
* **administration-contract:** replaces gtx-react Modal with gx-design Modal ([ccfe447](https://gitlab.pepita.io/getrix/mls-site/commit/ccfe4470c677659e000da0cab4f367f0505ee923))
* **administration-invoicing-data:** replaces gtx-react Modal with gx-design Modal ([6599afb](https://gitlab.pepita.io/getrix/mls-site/commit/6599afb9ab97f58613a56932fc877a6d401c511d))

## [5.106.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.106.1...v5.106.2) (2024-03-20)


### Bug Fixes

* **config:** disabled sync scheduler for the Luxembourg ([6ebf1cd](https://gitlab.pepita.io/getrix/mls-site/commit/6ebf1cdd2b5b2c8f4468215a75dcf79ffc05f8eb))
* **properties-spaces:** adds categoryId to loadDashboard results ([5ec4b31](https://gitlab.pepita.io/getrix/mls-site/commit/5ec4b31241fbaa33c70a3c51c6c3e1ef8ac0be04))


### Refactor

* **acquisition-estimates:** replaces gtx-react Modal with gx-design Modal ([2e0193b](https://gitlab.pepita.io/getrix/mls-site/commit/2e0193b2f2ae25f072d6f54c97cde8c3eb671244))

## [5.106.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.106.0...v5.106.1) (2024-03-19)

## [5.106.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.105.0...v5.106.0) (2024-03-19)


### Features

* **config:** use specific credential of Getrix api for the Luxembourg ([be45453](https://gitlab.pepita.io/getrix/mls-site/commit/be45453375cf75cb71b8e745770d725c36a445a7))


### Bug Fixes

* **dashboard:** add property button link ([feb89f2](https://gitlab.pepita.io/getrix/mls-site/commit/feb89f294f692098e6b8b3fde69ed93117bc568d))
* **logout:** changed logout behaviour for immotop ([3542918](https://gitlab.pepita.io/getrix/mls-site/commit/3542918a5351c89d6e048355bc1751e8765d726c))

## [5.105.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.104.1...v5.105.0) (2024-03-18)


### Features

* **mixpanel:** disabled for the Luxembourg ([3155502](https://gitlab.pepita.io/getrix/mls-site/commit/315550230c35758701e64d83cb14bb37a753b4cf))
* refactor dashboard ([0a2641f](https://gitlab.pepita.io/getrix/mls-site/commit/0a2641f48d6c42312c0b0e2f3d105b936c37cb13))
* **settings-general:** replace moment with date-fns ([bdf8691](https://gitlab.pepita.io/getrix/mls-site/commit/bdf86919de03174e8b980b4223bed840e5c36821))


### Bug Fixes

* **customers:** list print ([2579798](https://gitlab.pepita.io/getrix/mls-site/commit/25797981f2835ffda2c495e939d9101330fe7c3d))
* **gx-table:** horizontal scrollbar optimization ([b9a99c5](https://gitlab.pepita.io/getrix/mls-site/commit/b9a99c51ca1c05a459fa5a7371c8d4754fdd9a74))
* **stats:** wrong namespace for the Luxembourg ([f2366a5](https://gitlab.pepita.io/getrix/mls-site/commit/f2366a56598306d6bada19be7e85d9d14ec87174))

## [5.104.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.104.0...v5.104.1) (2024-03-15)


### Bug Fixes

* **matches:** missing province in province only searches ([ea42ce4](https://gitlab.pepita.io/getrix/mls-site/commit/ea42ce4db43f756f4704fbbc8d29d34b8c51db4c))
* **menu:** impostazioni additional check ([a33a40c](https://gitlab.pepita.io/getrix/mls-site/commit/a33a40c1be4a88a69e2b867042250568e59d1fb6))
* **performance:** fixed bad visibility colors for performance print for immotop ([1a074af](https://gitlab.pepita.io/getrix/mls-site/commit/1a074af6275ac7943fde1896cc47e36241000b63))

## [5.104.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.103.0...v5.104.0) (2024-03-14)


### Features

* **menu:** created url resolver ([41be90c](https://gitlab.pepita.io/getrix/mls-site/commit/41be90c8b7016cb9b6b17c2b6b9d474a41df218b))
* **menu:** removed domain and unused items for the Luxembourg ([bdc7269](https://gitlab.pepita.io/getrix/mls-site/commit/bdc7269fb3f6f1f6039dde70582dac9db8a7538f))
* **portal-properties:** switch on perfomance previous stats on IT ([d285f0e](https://gitlab.pepita.io/getrix/mls-site/commit/d285f0e3d0d604992bed90e2c208ca8a645edda1))


### Bug Fixes

* **config:** wrong mailer url for the Luxembourg ([f631f1c](https://gitlab.pepita.io/getrix/mls-site/commit/f631f1cac294214d65358ffd1d9d76ef6198f12d))
* **imnovisita-scheduled:** close delete modal on success ([39794b2](https://gitlab.pepita.io/getrix/mls-site/commit/39794b2ce4380d20c3aa42ccc114108e58353649))
* **matches:** forced filter isManaged+isSent if agency has no zones ([334ed1d](https://gitlab.pepita.io/getrix/mls-site/commit/334ed1df8dc0fc44879131619d488dfbb7a08f69))
* **matches:** mapped missing geography fields in Search property ([f50a404](https://gitlab.pepita.io/getrix/mls-site/commit/f50a40448d28eab039398d9fe7a7d65d01675fad))
* **matches:** radius should not be divided by 1000 ([4b66f11](https://gitlab.pepita.io/getrix/mls-site/commit/4b66f1110fbb4e5920dcfd64fe7dea5d48df8e46))
* **menu:** old menu fixed bug ([f3e7d7b](https://gitlab.pepita.io/getrix/mls-site/commit/f3e7d7b68cdd422d5d740b544c155cf7e9df7a5d))
* **portal-properties:** does not show 0 if has no variation ([1c2eb2f](https://gitlab.pepita.io/getrix/mls-site/commit/1c2eb2f3540b27b148f53d13f01e6d32e40ab4d2))

## [5.103.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.102.0...v5.103.0) (2024-03-12)


### Features

* **agent:** added media server url specific for the agents ([c79e57e](https://gitlab.pepita.io/getrix/mls-site/commit/c79e57e48dfc1ec875e582073c9a6ded1d0ee6de))
* **gx-navigation:** fix indentation ([6a8759f](https://gitlab.pepita.io/getrix/mls-site/commit/6a8759f3687038096f3d9023d6e3d21e7c1752aa))


### Bug Fixes

* **agency-estimates:** migration of Notify componente to gx-design/snackbar ([552c0a1](https://gitlab.pepita.io/getrix/mls-site/commit/552c0a1b3c19c52c685ac8283b6fe051b3be2ad0))
* **matches:** showing empty state if agency has not getrixPlus ([4d5ceee](https://gitlab.pepita.io/getrix/mls-site/commit/4d5ceeeaac2cff73737c78430d015bbb61a6da1b))
* **menu:** removed empty space close handler ([09195b6](https://gitlab.pepita.io/getrix/mls-site/commit/09195b65b6f11978df12cc910cd4319144f13a3a))
* **performance:** fixed bad condition for variation helper ([70e8273](https://gitlab.pepita.io/getrix/mls-site/commit/70e8273e261962ef512ad479dba9a6f0f2965480))


### Refactor

* **customer:** replaces gtx-react Notify with gx-design/Snackbar ([ed1e588](https://gitlab.pepita.io/getrix/mls-site/commit/ed1e588555fdf04ce2362f408ed31e5ac7f60857))
* **immovisita-scheduled:** replaces Notify with gx-design/Snackbar ([1b6fa58](https://gitlab.pepita.io/getrix/mls-site/commit/1b6fa58d601c559ef4fa88a4d6e40dfb8aff34f7))
* **sales-requests:** replaces Notify with gx-design/Snackbar ([629456f](https://gitlab.pepita.io/getrix/mls-site/commit/629456fa69d8590e320ab8e769bd0e7bc12a0d8d))

## [5.102.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.101.1...v5.102.0) (2024-03-11)


### Features

* comparing performance over previous week on properties-list and performance pages ([5521b8e](https://gitlab.pepita.io/getrix/mls-site/commit/5521b8eeb921a868245c4361324194885b0f0c8b))
* **matches:**  empty state if agency dont have getrix ([e66e30a](https://gitlab.pepita.io/getrix/mls-site/commit/e66e30a6de95c8e716f8e65e50f5ccc9876ad847))
* **matches:** map placeholder if no city or points ([6bb9c69](https://gitlab.pepita.io/getrix/mls-site/commit/6bb9c690f781e14717d599e6cb4efc7dfb7362f8))
* **media:** use cdn instead of mediaserver for the Luxembourg ([01bf0c7](https://gitlab.pepita.io/getrix/mls-site/commit/01bf0c7a4f0e6d54bcb0d72a66d16ff9b0288e72))
* **performance:** enabling previous stats via env ([73cb4f2](https://gitlab.pepita.io/getrix/mls-site/commit/73cb4f23db538e3e219fee7659e2f862f7e44ea5))


### Bug Fixes

* **billboard:** from Notify to gx-design/Snackbar ([12b5759](https://gitlab.pepita.io/getrix/mls-site/commit/12b57596d8295477bd6919693fe4436c4bdc2e5b))
* **billboard:** modal edit text ([6761c8f](https://gitlab.pepita.io/getrix/mls-site/commit/6761c8f0a5d6f9e7cf7e118d7f20a7683f67fea2))
* infinite loading using msw in develop ([29e8b1d](https://gitlab.pepita.io/getrix/mls-site/commit/29e8b1dc005cee590640ca1653389d11b4d8f064))
* **matches:** mapped missing fields on Search response ([1dab6c2](https://gitlab.pepita.io/getrix/mls-site/commit/1dab6c231490a8c71366a1f69e7a2b19dc641098))

## [5.101.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.101.0...v5.101.1) (2024-03-06)


### Bug Fixes

* **matches:** search for clients with match doesn't work correctly ([654b4e8](https://gitlab.pepita.io/getrix/mls-site/commit/654b4e839ef72547f4bef5c50bd13f1b6eac6459))
* **properties-list:** first print attempt was empty ([4efcaeb](https://gitlab.pepita.io/getrix/mls-site/commit/4efcaeb1bb645ec52893b73bf00173d5531306dc))

## [5.101.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.100.0...v5.101.0) (2024-03-05)


### Features

* **matches:** search map with popover in list items ([21a39fc](https://gitlab.pepita.io/getrix/mls-site/commit/21a39fc68fcb92bdc3c9fd94db52a337b346dd81))
* **property:** added endpoint to bulk get ads stats ([af3fe6d](https://gitlab.pepita.io/getrix/mls-site/commit/af3fe6df9ee04336694154a527e874f9abcb6175))
* **z-index:** tokens ([13a1250](https://gitlab.pepita.io/getrix/mls-site/commit/13a125006f78fe3dae0e7ee177abe577dfeb5e26))


### Bug Fixes

* 'smart phone' service name for immotop ([d4fea6a](https://gitlab.pepita.io/getrix/mls-site/commit/d4fea6a7ece030808df274ad90a3c747ff786a37))
* **check:** gdpr ([5a113f0](https://gitlab.pepita.io/getrix/mls-site/commit/5a113f0e5cc650cab0f8bc1723f4510fab9edfab))
* **docker:** wrong configuration ([88ff7fc](https://gitlab.pepita.io/getrix/mls-site/commit/88ff7fc17a75672dfa505ae168c4c9b202f738d1))
* **gx-navigation:** hiding NotificationButton when BEAMER_NEWS is disabled ([014d014](https://gitlab.pepita.io/getrix/mls-site/commit/014d01446c1b9097bd21a1273031910693440a64))
* **immotop:** landing fatturazione ([1ce5fd4](https://gitlab.pepita.io/getrix/mls-site/commit/1ce5fd43f4ad6faf1e5aba4a07254a55f73c086b))
* **list:** use fetch to control response error code ([aa28013](https://gitlab.pepita.io/getrix/mls-site/commit/aa2801384084409fd87507d473e7d8f26ce3c1d8))
* **matches:** added remapping for missing fields price and surface ([acc82de](https://gitlab.pepita.io/getrix/mls-site/commit/acc82deb9ab9598926b3464667df2af19c5e6408))
* **navigation:** fixes ([26032e2](https://gitlab.pepita.io/getrix/mls-site/commit/26032e2815dd5e246358a2b1fdbc1005ced0bd08))
* **searches:** searches card checkbox spacing ([393b316](https://gitlab.pepita.io/getrix/mls-site/commit/393b316abf25d98754b15b942fa5dc0d40f15d5b))

## [5.100.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.99.0...v5.100.0) (2024-02-22)


### Features

* **matches:** added customers_match_area_access mixpanel event on page load ([d4646ca](https://gitlab.pepita.io/getrix/mls-site/commit/d4646cafa2fc8638b202d9f797ce90a90071fb34))


### Bug Fixes

* **acquistion:** help mobile ([f1d6fb0](https://gitlab.pepita.io/getrix/mls-site/commit/f1d6fb075f6486c7136bda3b866ee51dd48e00d5))
* **agent:** agents list new endpoint with new filter ([04f807c](https://gitlab.pepita.io/getrix/mls-site/commit/04f807c1e7b83b16a5d50ec3247fa085daa661ea))
* **counters:** fix counter on failed fetch ([ecc0a11](https://gitlab.pepita.io/getrix/mls-site/commit/ecc0a11217d7450d572114d662b1b6e84866be75))
* **menu:** old menu wrong class on body ([0356280](https://gitlab.pepita.io/getrix/mls-site/commit/0356280a0ecfba9bbd0770d9aa5fd748c142f612))
* **zones:** gradient on read more ([422ceb9](https://gitlab.pepita.io/getrix/mls-site/commit/422ceb97e9c99c8c3d553fc7844d7d71fa3f9833))

## [5.99.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.98.5...v5.99.0) (2024-02-20)


### Features

* **gx-navigation:** update to react-18 ([75f82a9](https://gitlab.pepita.io/getrix/mls-site/commit/75f82a9178c7e56f7c4fed19baae1a70aa0957f8))


### Bug Fixes

* **acquisition-privates:** shows Pager also when total pages are juste one ([a37e65d](https://gitlab.pepita.io/getrix/mls-site/commit/a37e65d486b8de2f55be8e07c329d82928e69339))
* **immotop:** dati fatturazione ([045a08d](https://gitlab.pepita.io/getrix/mls-site/commit/045a08d3b3705a9885d0bfdfa9e0b27595b7e3c8))
* **matches:** invalid array length after filtering ([e9795c4](https://gitlab.pepita.io/getrix/mls-site/commit/e9795c4579f7cf554b5ce108b6a3de3c0223719e))

## [5.98.5](https://gitlab.pepita.io/getrix/mls-site/compare/v5.98.4...v5.98.5) (2024-02-19)


### Bug Fixes

* error when trim null ([04b30ff](https://gitlab.pepita.io/getrix/mls-site/commit/04b30ff9666c269b31a50228c1f8e34b228a35ec))

## [5.98.4](https://gitlab.pepita.io/getrix/mls-site/compare/v5.98.3...v5.98.4) (2024-02-19)


### Bug Fixes

* **matches:** remove useless comma when empty address in ad info ([f45d959](https://gitlab.pepita.io/getrix/mls-site/commit/f45d959adab5fe6e09c745f7c47fc41b1d12956b))

## [5.98.3](https://gitlab.pepita.io/getrix/mls-site/compare/v5.98.2...v5.98.3) (2024-02-19)


### Bug Fixes

* fixed customer mobile phone ([73b5d5a](https://gitlab.pepita.io/getrix/mls-site/commit/73b5d5a4f499a82fc517f02bb00a56e2ff5e4b03))

## [5.98.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.98.1...v5.98.2) (2024-02-16)


### Bug Fixes

* **matches:** changed ad image size in match email ([92a3e3e](https://gitlab.pepita.io/getrix/mls-site/commit/92a3e3e6ed13624aefff8de9ccb7beb67ebaf9be))
* **matches:** wrong ad image url in match email ([72fa6f4](https://gitlab.pepita.io/getrix/mls-site/commit/72fa6f4edfb8683d0dc5d0fd8216cf2831079564))

## [5.98.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.98.0...v5.98.1) (2024-02-16)


### Bug Fixes

* **babelfish-loader:** forcing sync of ready event dispath ([c182c7a](https://gitlab.pepita.io/getrix/mls-site/commit/c182c7af74fd3f3545c2f4be4047e0f6ec499389))
* **customer:** test pagination lenght before render Pager component ([19d2476](https://gitlab.pepita.io/getrix/mls-site/commit/19d24769257a6c65c464e13b1cb65a9c32b61508))
* **matches:** mayches incremental typecheck inclusion and fixes ([a612d6e](https://gitlab.pepita.io/getrix/mls-site/commit/a612d6e5b0ee53f0e52f86e47887a1b9baf1269e))
* **matches:** removed zone api call fail fallback to no zones ([216dd13](https://gitlab.pepita.io/getrix/mls-site/commit/216dd13475a783a31306ac8832265a7e5f6ed068))
* **matches:** wrong ad url in match email ([23694b5](https://gitlab.pepita.io/getrix/mls-site/commit/23694b55d327a7f9efd874d4dca24450914c182d))
* **menu:** aste inserimento menu highlight ([6f2aa8e](https://gitlab.pepita.io/getrix/mls-site/commit/6f2aa8e3ceb46890b67a8a5d4f988221fe8451c1))
* **zones:** read more ([7e1fa34](https://gitlab.pepita.io/getrix/mls-site/commit/7e1fa3428a3bce85d3b4154e72f10ba0615162fd))

## [5.98.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.97.3...v5.98.0) (2024-02-14)


### Features

* **ad:** refactor of ad url as a parameter ([639aa80](https://gitlab.pepita.io/getrix/mls-site/commit/639aa80d7d874c05b8956d608015392f32503596))


### Bug Fixes

* **menu:** match counter not checking Getrix version ([4c50ba9](https://gitlab.pepita.io/getrix/mls-site/commit/4c50ba9f6c0ed2dd64f62ae4efcfb747d45acfbb))
* **module:** matches and zone authorizer ovverride constructor missing parent calls ([83e8ff8](https://gitlab.pepita.io/getrix/mls-site/commit/83e8ff800142b2b2cd862caafedc80986afca72f))

## [5.97.3](https://gitlab.pepita.io/getrix/mls-site/compare/v5.97.2...v5.97.3) (2024-02-14)


### Bug Fixes

* **gx-navigation:** agenda href ([02e4cc1](https://gitlab.pepita.io/getrix/mls-site/commit/02e4cc14a932f777be930ba52850303444829e9a))
* **landing:** missing js module imports for youdomus ([cf78e8a](https://gitlab.pepita.io/getrix/mls-site/commit/cf78e8aa6221b16a0d55df90001762e73bdc66f0))

## [5.97.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.97.1...v5.97.2) (2024-02-13)


### Bug Fixes

* **agency-estimates:** fix activation copy ([24c36cb](https://gitlab.pepita.io/getrix/mls-site/commit/24c36cbb8e146ae17081860c33bbd2b320b21e3a))
* corrected vip for api ([603e970](https://gitlab.pepita.io/getrix/mls-site/commit/603e9706f21360ba159d3ae6deddd67d8abfaddd))
* corrected vip for api ([6937391](https://gitlab.pepita.io/getrix/mls-site/commit/69373913f5df05c071a3ae60c195109bdaf7edc4))
* **gx-navigation:** appointment route ([b9e0945](https://gitlab.pepita.io/getrix/mls-site/commit/b9e0945629d3566ff0f3654a6d37940cf464ab9d))
* **menu:** fallback on failed new menu loading ([12ba753](https://gitlab.pepita.io/getrix/mls-site/commit/12ba75355e3ffa49426a52db89068d484f158c83))

## [5.97.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.97.0...v5.97.1) (2024-02-12)

## [5.97.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.96.0...v5.97.0) (2024-02-12)


### Features

* **evaluation-zones:** implemeted api filter onlyGetrix ([913797d](https://gitlab.pepita.io/getrix/mls-site/commit/913797dffad0e90724e21b24287022a6ab5ea643))
* **matches:** updated search name construction logic to handle the case when typology is null ([6b6fe47](https://gitlab.pepita.io/getrix/mls-site/commit/6b6fe472f4020322e25075e0a1068f23d6cc978b))
* **stats:** updated namespace for the Luxembourg ([4f08a5c](https://gitlab.pepita.io/getrix/mls-site/commit/4f08a5c5d62987edf3f8ebbb78abef6224e518ab))
* translations update ([e69b3ec](https://gitlab.pepita.io/getrix/mls-site/commit/e69b3ecb2cf4f639daf46e474776af1d0f9ba7a0))


### Bug Fixes

* **dashboard:** removed dashboard generic contacts if match enabled ([1945bce](https://gitlab.pepita.io/getrix/mls-site/commit/1945bcef243bf07f0b68581e52ffbee9b251932e))
* **match-email:** mail email agency address fix ([6ad38eb](https://gitlab.pepita.io/getrix/mls-site/commit/6ad38eb27147e399c19e6f7754076e64a617a664))
* **matches:** search by property ref on empty code ([ab3e148](https://gitlab.pepita.io/getrix/mls-site/commit/ab3e148385b8229d8f3d7b9e2334ee44a566808c))
* **zones:** moved from env to parameters zone packages ([664b4f5](https://gitlab.pepita.io/getrix/mls-site/commit/664b4f59c362702ea3aaa8b3ae0e31157d824c6a))
* **zones:** ui fixes ([d80b38b](https://gitlab.pepita.io/getrix/mls-site/commit/d80b38b20a8b3cb00d6b870f97e24d583011a7c6))

## [5.96.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.95.0...v5.96.0) (2024-02-05)


### Features

* **fullstory:** importing script from common when new env variable is 1 ([4705520](https://gitlab.pepita.io/getrix/mls-site/commit/470552079ee8c91e4fc0a9058ae0ec37849372fe))


### Bug Fixes

* **checkbox:** profilo ([e8f0aae](https://gitlab.pepita.io/getrix/mls-site/commit/e8f0aae884b2ff46769defde6e1160f55cc65f86))
* **customer-service:** email modal didn't work with some services ([d5f503e](https://gitlab.pepita.io/getrix/mls-site/commit/d5f503e7b38c83f96895de5ca282894f606b55bb))
* **customer-service:** email of ecommerce products had wrong placeholder ([c523748](https://gitlab.pepita.io/getrix/mls-site/commit/c523748b4f4bb3cf260c9a8c9e04554a38b8955e))
* **performance:** bad data in tooltip, bad picker position ([1602322](https://gitlab.pepita.io/getrix/mls-site/commit/160232289e3ace3522b3ee746659c57a09c184b7))
* **zones:** query optimization ([05b83d4](https://gitlab.pepita.io/getrix/mls-site/commit/05b83d4862c50a4cdb1c935e0136d7cef681fe59))

## [5.95.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.94.0...v5.95.0) (2024-02-01)


### Features

* added vitest in ci ([37e107a](https://gitlab.pepita.io/getrix/mls-site/commit/37e107a4b8f3195f011b8fb845e5f9a3d070c238))


### Bug Fixes

* **billboard:** align text icons ([01a1a2a](https://gitlab.pepita.io/getrix/mls-site/commit/01a1a2adb0f92f28c84dc06935d2785750bdaaf8))
* disabled match counter ([74555a4](https://gitlab.pepita.io/getrix/mls-site/commit/74555a4ed1e9db7bdb326c1c9713f329d9faf2a6))
* **immotop:** profile url changed in menu profile dropdown ([20e2668](https://gitlab.pepita.io/getrix/mls-site/commit/20e266847efdf0dda08c73da6d12af2f8cbcfaa7))
* **matches:** missing cta link ([6406c26](https://gitlab.pepita.io/getrix/mls-site/commit/6406c26c473114ad7151eb6098a07797acf70912))

## [5.94.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.93.0...v5.94.0) (2024-02-01)


### Features

* added query string for redirect to specific page on single sign on ([313dae9](https://gitlab.pepita.io/getrix/mls-site/commit/313dae9d63d1907936f0f453d343b07e6c4ec575))
* **matches:** disabling mocks ([99a1468](https://gitlab.pepita.io/getrix/mls-site/commit/99a146812b0544eb5b8994726e4a6262ee7bdcb2))
* **matches:** disabling mocks ([dcf1188](https://gitlab.pepita.io/getrix/mls-site/commit/dcf1188dd3a3eb399c22cfd0c4c9c884df97a005))
* **matches:** empty state if no zones ([b5d358c](https://gitlab.pepita.io/getrix/mls-site/commit/b5d358c437ac9e858223e2bd94ae87da50543e80))
* **matches:** empty state labels + icon + styles ([6ba0564](https://gitlab.pepita.io/getrix/mls-site/commit/6ba05646c5cf44a9e8c77f9813d5ad6ae34de4c3))
* **matches:** endpoint to send bulk emails ([738d0b4](https://gitlab.pepita.io/getrix/mls-site/commit/738d0b498138989cc7c9e3c7512cc32f35f7e3c2))
* **menu:** new service item for the Luxembourg ([826e046](https://gitlab.pepita.io/getrix/mls-site/commit/826e04674daf69bc5fabcff8b7a0a29def941c4c))
* update fe testing doc ([696d813](https://gitlab.pepita.io/getrix/mls-site/commit/696d8136e4c9355a21594fcd0cd1865d7ad83e66))


### Bug Fixes

* **billboard:** print ([6fcef5d](https://gitlab.pepita.io/getrix/mls-site/commit/6fcef5d3ff377fb4ea86a5f3e98eb8ee21ab174c))
* **build:** adds hash to css assets ([5cf7621](https://gitlab.pepita.io/getrix/mls-site/commit/5cf7621375ec4c669f4330bda86f1a457f73664c))
* **checkbox:** billboard register ([ad8eb2f](https://gitlab.pepita.io/getrix/mls-site/commit/ad8eb2fe8dac1b6f837d4a7e6082b5032c37c81a))
* **matches:** fixed some issues about match email ([a67c5ad](https://gitlab.pepita.io/getrix/mls-site/commit/a67c5ad478290808079c70e7cd0765be1c0b188f))
* **matches:** matches icon sparkle ([5e4221e](https://gitlab.pepita.io/getrix/mls-site/commit/5e4221ef259aa42858e58d100c3d1c53e5dea525))
* **matches:** show emptyState only if noZones AND no matches ([e4c4ecd](https://gitlab.pepita.io/getrix/mls-site/commit/e4c4ecdaed305abeb53acde8c56d34dfb444d32d))
* **performance:** fixed missing reset after quick filter is activated ([e9b5c01](https://gitlab.pepita.io/getrix/mls-site/commit/e9b5c010759640acefe9174f295344ac9f1dc2c4))
* **performance:** horizontal scroll ([c213072](https://gitlab.pepita.io/getrix/mls-site/commit/c2130724acdd8bb5a89cc42347cea0e8f340012d))
* **report:** checkbox ([6641cd4](https://gitlab.pepita.io/getrix/mls-site/commit/6641cd47f5f47819acaecc40e7caff2002d65115))


### Refactor

* **GtxApp:** optional initFunc ([82bef9c](https://gitlab.pepita.io/getrix/mls-site/commit/82bef9c1faaba208471102e0e82ceedfe6de15cf))
* replace use of formatter and api client helper with data provider... ([e265360](https://gitlab.pepita.io/getrix/mls-site/commit/e2653606afa19a2b96893caa69ba23a5c5609a07))

## [5.93.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.92.1...v5.93.0) (2024-01-25)


### Features

* add gitlab reporter ([1923c5c](https://gitlab.pepita.io/getrix/mls-site/commit/1923c5c111ee03153bb1a7065ecdac12bf7f92c1))
* **agency:** updated dataprovider to get evaluation zones of an agency ([810a118](https://gitlab.pepita.io/getrix/mls-site/commit/810a1189ce259c0c28a10cc685f4cb9270550281))


### Bug Fixes

* **zones-manager:** added type to api call + minor type fixes + error check ([916ff6f](https://gitlab.pepita.io/getrix/mls-site/commit/916ff6fca0db7d22895019fd8a3ac132878c7716))

## [5.92.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.92.0...v5.92.1) (2024-01-24)


### Bug Fixes

* **performance:** events chart ([7cf1546](https://gitlab.pepita.io/getrix/mls-site/commit/7cf15463b1dfc32b0c7c625597f55e2600d26f18))

## [5.92.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.91.2...v5.92.0) (2024-01-24)


### Features

* **zones:** new zones manager ([4533f1b](https://gitlab.pepita.io/getrix/mls-site/commit/4533f1b4e0edb53a649cc8b3592490dc90fc573c))


### Bug Fixes

* **portal-properties:** update country filter options on filter status change ([1a434ec](https://gitlab.pepita.io/getrix/mls-site/commit/1a434ece7d8694dbc64371e06726894ae4b4065b))
* **settings-media:** avoid agency data nesting ([a1de328](https://gitlab.pepita.io/getrix/mls-site/commit/a1de32881b52ccf28051fd93352c9921260d2322))

## [5.91.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.91.1...v5.91.2) (2024-01-23)


### Bug Fixes

* nullable SearchResponse getter methods ([dd66303](https://gitlab.pepita.io/getrix/mls-site/commit/dd6630318ba34471949facb0138eed030660ff20))

## [5.91.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.91.0...v5.91.1) (2024-01-23)


### Bug Fixes

* wrong name MENU_COUNTERS_POLLING_INTERVAL ([9d2eb5a](https://gitlab.pepita.io/getrix/mls-site/commit/9d2eb5a603decd18fc2aa68210f46b61c29d4d9d))

## [5.91.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.90.0...v5.91.0) (2024-01-23)


### Features

* active search and match ([cbdd270](https://gitlab.pepita.io/getrix/mls-site/commit/cbdd2706862991553f28652e6c8fce6a75b80163))
* **performace:** table card ([dd6d880](https://gitlab.pepita.io/getrix/mls-site/commit/dd6d880416cf62d6392c2522001f120a532688b7))
* **performance:** error page + immotop adjustements ([d34fe43](https://gitlab.pepita.io/getrix/mls-site/commit/d34fe4371a345776232997ceab68adb3b3b2be01))


### Bug Fixes

* CardList broken on missing config check ([7ce92c7](https://gitlab.pepita.io/getrix/mls-site/commit/7ce92c78a61bef307c4e55d42af2aceb756750e2))
* **gx-table:** double sticky ehader ([aae42c9](https://gitlab.pepita.io/getrix/mls-site/commit/aae42c992387c097064098fbc6d72f8e53825906))
* **matches:** fixed some email issues ([bf4997f](https://gitlab.pepita.io/getrix/mls-site/commit/bf4997f5121e039041934e25f659b5df4977f1f6))
* **matches:** property cell ref label print ([cdf9658](https://gitlab.pepita.io/getrix/mls-site/commit/cdf9658d5569c547d2f92127a2aa6e90bade47af))
* **performance:** detail bug FF ([7796276](https://gitlab.pepita.io/getrix/mls-site/commit/7796276ce74cbfe45045bbdf14f0a9bb4e4c3187))
* updated url reference for api ([4bd1ff9](https://gitlab.pepita.io/getrix/mls-site/commit/4bd1ff99bf0b22bd7273d0df36d7c29e933d8098))

## [5.90.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.89.1...v5.90.0) (2024-01-18)


### Features

* **print:** modifica bottone e pdf ([a692971](https://gitlab.pepita.io/getrix/mls-site/commit/a692971a26672f0c6249e2cb405f6fb9a0f3434c))


### Bug Fixes

* **property-performance:** download button changed into primary ([4434906](https://gitlab.pepita.io/getrix/mls-site/commit/4434906f892e180e06470198a6af5c3a1f97eb38))

## [5.89.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.89.0...v5.89.1) (2024-01-17)


### Bug Fixes

* **property-performance:** changed similar ads title ([8e78a8e](https://gitlab.pepita.io/getrix/mls-site/commit/8e78a8e565df3b2470d2e76adb863a1be6810bf2))
* **property-performance:** changed similar ads title on print version ([890e3b5](https://gitlab.pepita.io/getrix/mls-site/commit/890e3b566c6fe4b0169d42bc7355f2a9f3e16ece))

## [5.89.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.88.0...v5.89.0) (2024-01-17)


### Features

* **immotop:** favicon ([f7751e7](https://gitlab.pepita.io/getrix/mls-site/commit/f7751e7cb58ad6843eeacd5f0bd92f176792e6be))


### Bug Fixes

* **property-performance:** math.ceil on price min/max quick filter ([fcbd17e](https://gitlab.pepita.io/getrix/mls-site/commit/fcbd17ed7685e44e1e688fa07ece5e185916f0da))

## [5.88.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.87.0...v5.88.0) (2024-01-17)


### Features

* **performance:** card performance ([bb26607](https://gitlab.pepita.io/getrix/mls-site/commit/bb266072a07720b392ce61c7cf518fd583ea0f85))


### Bug Fixes

* **agency:** fixed wrong route name in multiagency redirect ([6d65da1](https://gitlab.pepita.io/getrix/mls-site/commit/6d65da181522f1c2ecc59f187d52da9f659a9d5e))
* **property-performance:** adds quick filter for similar ads ([41a9ff6](https://gitlab.pepita.io/getrix/mls-site/commit/41a9ff6e4d92c0ba3c67dce3a25db5d93054d8d2))
* **property:** handle Ad Statues in Property publication geography ([d9663ac](https://gitlab.pepita.io/getrix/mls-site/commit/d9663ac00f2c3962e13cb0156773d1004a67bcab))

## [5.87.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.86.2...v5.87.0) (2024-01-09)


### Features

* **sf-web-to-case:** staging and prod switch on ([f3eab5c](https://gitlab.pepita.io/getrix/mls-site/commit/f3eab5cc70770f058688cc82aa420277e61fc846))
* stripped assets/1 found into js files ([2f0569b](https://gitlab.pepita.io/getrix/mls-site/commit/2f0569bede1e0c9ee6fe1d55e2d927905502cc7d))
* stripped assets/1 from urls ([911c63a](https://gitlab.pepita.io/getrix/mls-site/commit/911c63a1f28e7af993cb60f065a7d62b0b1e0c8e))

## [5.86.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.86.1...v5.86.2) (2024-01-08)


### Bug Fixes

* **holidays-logo:** holidays logo fix ([7480533](https://gitlab.pepita.io/getrix/mls-site/commit/74805335cdd7202d77cd88603e8c821f9b48a0d2))
* **menu:** removed acquisition item for Luxembourg ([f49060d](https://gitlab.pepita.io/getrix/mls-site/commit/f49060d9067f9faf292db41f55c1f5dfa8603097))
* **property-performance:** adds promote listing mixpanel event on main property button ([60585bd](https://gitlab.pepita.io/getrix/mls-site/commit/60585bd4bb36932d62e2a3890cad1e02beb2e14d))
* remove duplicate property ([8892543](https://gitlab.pepita.io/getrix/mls-site/commit/8892543952b88dc2e2617936fb6bc173c0eaa4c3))

## [5.86.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.86.0...v5.86.1) (2023-12-21)


### Bug Fixes

* changed email regex allowing capital letter ([25ca284](https://gitlab.pepita.io/getrix/mls-site/commit/25ca284c257a4d0e1962d18de0488a0a18e13906))

## [5.86.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.85.1...v5.86.0) (2023-12-21)


### Features

* **routes:** redirect dashboard route for Luxembourg ([a1097d9](https://gitlab.pepita.io/getrix/mls-site/commit/a1097d908912c50c89d9f5c68398d63e53cd905e))


### Bug Fixes

* **auth-register:** adds regexp for email, changed input placeholders ([e00e844](https://gitlab.pepita.io/getrix/mls-site/commit/e00e844220f7102d475f3a2f15ed7c9373f798ea))
* **mortgage-advice:** adds noValidate to the form to avoid html validation ([4779a73](https://gitlab.pepita.io/getrix/mls-site/commit/4779a73a33a484b3ce0b5275a141c81454a60816))
* **sales-requests:** introduce new trans with placeholder on modal title ([bca4d8c](https://gitlab.pepita.io/getrix/mls-site/commit/bca4d8c1397cb726b4311376a054dde4c7406b9f))

## [5.85.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.85.0...v5.85.1) (2023-12-20)


### Bug Fixes

* **registration:** restored the old agency registration endpoint ([7d86004](https://gitlab.pepita.io/getrix/mls-site/commit/7d86004a477bc16cf679b7e45e35dcae4727d353))

## [5.85.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.84.1...v5.85.0) (2023-12-19)


### Features

* add frontend testing docs ([0bb4127](https://gitlab.pepita.io/getrix/mls-site/commit/0bb4127701cf53c67abd6eecbedf810b3cae2383))
* add support for custom worker in ([fe89a75](https://gitlab.pepita.io/getrix/mls-site/commit/fe89a75b24c2a80d0d600a470cc5e68248a182e9))
* **ci:** added deploy lu ([a64e22b](https://gitlab.pepita.io/getrix/mls-site/commit/a64e22b0d5d9c0602c2c60b28a32e26ae7c53bd4))
* **contattaci:** aggiunge form offline di salesforce su it ([4011fa2](https://gitlab.pepita.io/getrix/mls-site/commit/4011fa2b2bc14839dad38ba781296587a5c17310))
* **menu:** updated items for Luxembourg ([4a89147](https://gitlab.pepita.io/getrix/mls-site/commit/4a89147c90111d12573c39bb1968317860c3a3a1))
* **registration:** created new agency registration endpoint ([401d218](https://gitlab.pepita.io/getrix/mls-site/commit/401d2182249a22907cad2f800830a64c64027bb5))


### Bug Fixes

* **auth-registration:** add nation to autocomplete option ([189332f](https://gitlab.pepita.io/getrix/mls-site/commit/189332fc944512db3f1f635f315e9a748c007912))
* **gx-dropdown:** switch to useLayoutEffect ([7fafc66](https://gitlab.pepita.io/getrix/mls-site/commit/7fafc664e1ab02bfd0b2076832f161792b960819))
* **immotop:** fixed various parameters ([e52b7f0](https://gitlab.pepita.io/getrix/mls-site/commit/e52b7f0c6b9dac200b4b0ebd9ad50f83488a04fe))
* **immotop:** prepared menu items for Luxembourg ([0e6d2ca](https://gitlab.pepita.io/getrix/mls-site/commit/0e6d2ca53deb984759cd65c6acd22b4d6e5dba73))
* **menu:** messaging module menu item ([bee781a](https://gitlab.pepita.io/getrix/mls-site/commit/bee781ac59e2dd32325c174077481505a5ba1d25))
* **property:** missing translate ([39482cc](https://gitlab.pepita.io/getrix/mls-site/commit/39482cc5283431478943d3da5cfb4ae444419b44))
* **signup:** email content placeholder key ([da0b755](https://gitlab.pepita.io/getrix/mls-site/commit/da0b755c940be7fc0ddffe8e10a7fd329183e85d))

## [5.84.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.84.0...v5.84.1) (2023-12-11)

## [5.84.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.83.0...v5.84.0) (2023-12-07)


### Features

* **christmas:** logos ([e2dc6ec](https://gitlab.pepita.io/getrix/mls-site/commit/e2dc6ecf2de24b599bb5315b81860d5646710e7e))
* **performance:** added clicks and views to property stats endpoint response ([37ea977](https://gitlab.pepita.io/getrix/mls-site/commit/37ea9778103d86139219b40f64e8fa105b1c4df1))
* upgrade to react router dom 6 ([f63b190](https://gitlab.pepita.io/getrix/mls-site/commit/f63b1902f72ba33769342f5f88508688e45abad2))


### Bug Fixes

* **modal:** sostituito componente ([3747415](https://gitlab.pepita.io/getrix/mls-site/commit/3747415effae619114a58a65a17c2ff3aca36ec5))
* **performance:** sidemenu ([5768d6c](https://gitlab.pepita.io/getrix/mls-site/commit/5768d6c0a4f125051e13ad306f0eee9d969841c6))
* **property-performance:** changed label on similar ads list ([4634d5a](https://gitlab.pepita.io/getrix/mls-site/commit/4634d5a23c60e1144da35c229ad68ef1d9b4e4eb))

## [5.83.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.82.0...v5.83.0) (2023-12-05)


### Features

* GxTable abstraction and component startup api shrink ([6dccb60](https://gitlab.pepita.io/getrix/mls-site/commit/6dccb60ef63537f97fa86136d3ec2f3aac57f0ae))


### Bug Fixes

* **common:** use IntlDateFormatter to localize datetimes ([c8b21c0](https://gitlab.pepita.io/getrix/mls-site/commit/c8b21c0e9fba35ec3b2b92240e7d32b6b1fcb33f))
* **portal-auction-nc:** test keys existance ([d11a87d](https://gitlab.pepita.io/getrix/mls-site/commit/d11a87da7e86d0bda614d5770d68f4f57d647d36))
* **portal-auctions-nc:** avoids agent filter autoselect ([6997760](https://gitlab.pepita.io/getrix/mls-site/commit/69977602d01a38fe2cf38c062dbe82e9387fedad))

## [5.82.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.81.0...v5.82.0) (2023-12-04)


### Features

* **data-provider:** refactor of api base url ([c3c00fb](https://gitlab.pepita.io/getrix/mls-site/commit/c3c00fb94287f2852aa7fe746f99f4c21383dedd))
* **geography:** added new endpoint to get suggestions ([4087402](https://gitlab.pepita.io/getrix/mls-site/commit/408740236f726c9b7c4601710c65a023fdf71efa))
* **immotop:** customers service email and phone ([8b304ec](https://gitlab.pepita.io/getrix/mls-site/commit/8b304ec8ae0049364c7e77f09f8df5ff846764ce))
* **searches:** refactored Mixpanel to use common GTX Provider ([c157e95](https://gitlab.pepita.io/getrix/mls-site/commit/c157e95f3e5acdf808bfa96dc4fe7eda6cefa4ab))

## [5.81.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.80.0...v5.81.0) (2023-11-30)


### Features

* **performance:** test explore cta ([bf3cf51](https://gitlab.pepita.io/getrix/mls-site/commit/bf3cf51a2d63457b7c4c46ce507047a03069cbdc))

## [5.80.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.79.0...v5.80.0) (2023-11-29)


### Features

* **immotop:** modifica header email logo ([1485397](https://gitlab.pepita.io/getrix/mls-site/commit/148539711c86e63e3955595605cd4ee160daec5b))

## [5.79.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.78.1...v5.79.0) (2023-11-29)


### Features

* migrate mortgage advice ([eb93964](https://gitlab.pepita.io/getrix/mls-site/commit/eb939647b8d11bc39eb9fb59710887b1d57fe417))


### Bug Fixes

* **scheduled-visits:** delete modal ([17ac71d](https://gitlab.pepita.io/getrix/mls-site/commit/17ac71d9bd436df414e35c2c90b82a0f87ec0dd2))

## [5.78.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.78.0...v5.78.1) (2023-11-28)


### Bug Fixes

* **menu:** fixed mechanism ([33c4827](https://gitlab.pepita.io/getrix/mls-site/commit/33c4827c4c25ebc773357f318ed0d445e2ad9b93))

## [5.78.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.77.1...v5.78.0) (2023-11-28)


### Features

* **avatar:** new component ([8db7924](https://gitlab.pepita.io/getrix/mls-site/commit/8db79245b34b139cd796ea5375c2f4cac4990495))
* **youdomus:** removed agencies filter ([ed01d42](https://gitlab.pepita.io/getrix/mls-site/commit/ed01d42c8f2d5bcdd440dc16f239061cb9bc9a4d))


### Bug Fixes

* **menu:** ui fixes ([235ecc2](https://gitlab.pepita.io/getrix/mls-site/commit/235ecc2320cd11ced1a9cd590ae68532cecdd54d))
* **snackbar:** profile page ([78ba698](https://gitlab.pepita.io/getrix/mls-site/commit/78ba698c9577a8ee780664849efeaff174aab4d8))
* **translations:** updated indomio-translations/cms ([c058332](https://gitlab.pepita.io/getrix/mls-site/commit/c058332efed9d01f2bda35fffb95fd5e79606930))

## [5.77.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.77.0...v5.77.1) (2023-11-20)


### Bug Fixes

* **badge:** new version without throw ([4460310](https://gitlab.pepita.io/getrix/mls-site/commit/4460310f32baa3d5050996c59994827603656ec6))

## [5.77.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.76.0...v5.77.0) (2023-11-20)


### Features

* **badge:** replace Tag with new Badge ([4eae233](https://gitlab.pepita.io/getrix/mls-site/commit/4eae233bcf9d4c0d4b69c1a820aee3b6b27eb40f))


### Bug Fixes

* **acquisition:** badge margin ([55f92a8](https://gitlab.pepita.io/getrix/mls-site/commit/55f92a89daa318b699d5ed1f004b97922eff17ed))
* **beta-badge:** brand style ([ecb25d5](https://gitlab.pepita.io/getrix/mls-site/commit/ecb25d597a7fb68d74ba9eb70641bfded97d37f2))
* **menu:** wrong searches menu for 'immobiliare pro' ([22e19c9](https://gitlab.pepita.io/getrix/mls-site/commit/22e19c94841a23a9eb696f29ddf0e426abc88b61))
* **performance:** RankingChart bad useffect return; fixed some ts issues ([53e8023](https://gitlab.pepita.io/getrix/mls-site/commit/53e8023830d4316ff8df5b4e135edf28d297f897))

## [5.76.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.75.0...v5.76.0) (2023-11-09)


### Features

* add integration tests ([4f49971](https://gitlab.pepita.io/getrix/mls-site/commit/4f499713f1a649328bbba17152ce5e3a56ae9e62))


### Bug Fixes

* **agency-estimates:** test elevator initial value when add a new report ([afca876](https://gitlab.pepita.io/getrix/mls-site/commit/afca876fe1694b6b035ea640ded41c2866ec6bc2))

## [5.75.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.74.0...v5.75.0) (2023-11-09)


### Features

* **config:** added new parameter to change body css class suffix ([ccbdfb4](https://gitlab.pepita.io/getrix/mls-site/commit/ccbdfb4af362e6daf2e0759c32f8ae9d9f2b5c31))


### Bug Fixes

* **customer-modal:** invalidates edit client form ([4ecf8d8](https://gitlab.pepita.io/getrix/mls-site/commit/4ecf8d8e8769173ad4ea78a7c2f9ce84bf540805))

## [5.74.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.73.5...v5.74.0) (2023-11-06)


### Features

* **portal-properties:** adds mixpanel event to similars filters ([5acbe80](https://gitlab.pepita.io/getrix/mls-site/commit/5acbe80af4382e8a97dc76e8986bcb4f2e90b787))
* **property:** added Property Condition to AdResponse ([fab78e9](https://gitlab.pepita.io/getrix/mls-site/commit/fab78e906cc63eed0f656fe87ed87175f6179a32))


### Bug Fixes

* **multisend:** empty state ([5c991b8](https://gitlab.pepita.io/getrix/mls-site/commit/5c991b8efd9fbf000e411eccb6e524d5d5f9b3d4))
* **notify:** addes error type for backward compatibility ([848f76a](https://gitlab.pepita.io/getrix/mls-site/commit/848f76aebb1cec8782a3006bcdffe5c99fe547e7))
* **portal-details:** test address existence ([690ad22](https://gitlab.pepita.io/getrix/mls-site/commit/690ad2249e384ba6934aba9fa7b85c7201f7315e))
* **portal-properties:** hide explore button if proprerty has no performance position ([c398cec](https://gitlab.pepita.io/getrix/mls-site/commit/c398cec9268fe449110601d286cdb2693c7298f8))
* **portal-properties:** only ads with premium visibility are allowed to show performances ([7566ca1](https://gitlab.pepita.io/getrix/mls-site/commit/7566ca179587ac0fac86e771d7ffdb7cb143da99))

## [5.73.5](https://gitlab.pepita.io/getrix/mls-site/compare/v5.73.4...v5.73.5) (2023-10-31)

## [5.73.4](https://gitlab.pepita.io/getrix/mls-site/compare/v5.73.3...v5.73.4) (2023-10-30)


### Bug Fixes

* **agency-report:** missing queryClientProvider wrap ([20f35b2](https://gitlab.pepita.io/getrix/mls-site/commit/20f35b2bfe31685a353ed27f23e9e1743cc666f8))
* **portal-properties:** show zone into performance descriptions only if city has macroozones ([fd3f0c8](https://gitlab.pepita.io/getrix/mls-site/commit/fd3f0c81c50826545aeb21fc061cb57710098fd1))
* revert modification in CI ([8138fc5](https://gitlab.pepita.io/getrix/mls-site/commit/8138fc55e5a3690d98cee25d3065a9c38d6f0881))
* temporary fix for pipeline ([f1ebbf0](https://gitlab.pepita.io/getrix/mls-site/commit/f1ebbf0d8a7e0f54decb51b56576360a5c08fbca))

## [5.73.3](https://gitlab.pepita.io/getrix/mls-site/compare/v5.73.2...v5.73.3) (2023-10-27)

## [5.73.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.73.1...v5.73.2) (2023-10-27)

## [5.73.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.73.0...v5.73.1) (2023-10-27)

## [5.73.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.72.1...v5.73.0) (2023-10-27)


### Features

* **cities:** added coords to city response ([53c24cf](https://gitlab.pepita.io/getrix/mls-site/commit/53c24cfd29c21a94cf18faca77fae8946e232272))
* **immotop:** sostituzione loghi immotop ([39bafa9](https://gitlab.pepita.io/getrix/mls-site/commit/39bafa9a0cf80f578e06d759c843d65328c38f6a))
* **menu:** switch between menu version could be controlled by a parameter ([6def1ec](https://gitlab.pepita.io/getrix/mls-site/commit/6def1ec20387107680dc86bda2d0ffb3d6e1f299))


### Bug Fixes

* **agency-estimante:** map with not geocoded address ([a863396](https://gitlab.pepita.io/getrix/mls-site/commit/a863396a24a298be2ef9950e50e38795fa01a7d7))
* **agency-estimates:** ws call to check if coords are into the municipally area when reverse geocoding ([ae566de](https://gitlab.pepita.io/getrix/mls-site/commit/ae566debac90eb35ca4d44a74263b94eaeb12aa2))
* **error-screen:** customers and gtxapp error screen ([a194578](https://gitlab.pepita.io/getrix/mls-site/commit/a1945789a2373f3d23761e088f92f2b900e9da70))
* **header:** fixed bad condition to render menu switch ([d837cf3](https://gitlab.pepita.io/getrix/mls-site/commit/d837cf3dbc25c34d56d497cbdcbeeea5e38f0e44))
* **languages:** fixed lu parameters and locale with partial code ([7f93eab](https://gitlab.pepita.io/getrix/mls-site/commit/7f93eabad762e3d0debb87d1ebd35d98a204fc4c))
* **languages:** fixed some Luxembourg issues ([f7768b8](https://gitlab.pepita.io/getrix/mls-site/commit/f7768b88dca8e196032cdc23d85deae0feb67a52))
* **menu:** customers old list menu and header ([0c343a8](https://gitlab.pepita.io/getrix/mls-site/commit/0c343a81309e804bdbc24166785ecf3540b2b7d2))
* **portal-properties:** restores statistics on mobile view ([bdcfc2a](https://gitlab.pepita.io/getrix/mls-site/commit/bdcfc2aadd9ec5797c34ec0fa75727f86ecf4627))
* **property-detail:** surface formatting ([23927d3](https://gitlab.pepita.io/getrix/mls-site/commit/23927d3cacfde8cea6cec9a819e6f420446ddb14))
* **property-performance:** add not available label when ad with no performance ([171690e](https://gitlab.pepita.io/getrix/mls-site/commit/171690e8bc416c14fddd241264ab15635fcd99de))
* **property-performance:** search id on small devices lists ([9988fe8](https://gitlab.pepita.io/getrix/mls-site/commit/9988fe825b46ba39f20afe8b2c671cbc8ef8345a))
* **z-index:** dropdown header and snackbar z-index fixes ([4a440fc](https://gitlab.pepita.io/getrix/mls-site/commit/4a440fc3f4b8bad9b02d9ffd22b44259bc4ba42c))

## [5.72.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.72.0...v5.72.1) (2023-10-19)


### Bug Fixes

* **menu:** menu virtual tour label ([0d8185f](https://gitlab.pepita.io/getrix/mls-site/commit/0d8185fea5c36a89178934ee845140e82ab7e5f6))
* **property-performance:** avoid to display performance quick action if bulk performance breaks ([0daf25a](https://gitlab.pepita.io/getrix/mls-site/commit/0daf25ac1a2241728be385fbbd821582c07d412d))
* some issues relates to Luxembourg ([eb9173f](https://gitlab.pepita.io/getrix/mls-site/commit/eb9173f355129a832a96d8f0865efba4fd050476))

## [5.72.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.71.0...v5.72.0) (2023-10-18)


### Features

* **ad-performance:** add position to similar property ([90998e4](https://gitlab.pepita.io/getrix/mls-site/commit/90998e4a0d2f51ba7f330ffc2892f106400e6806))
* add browserlists elements and linter ([9b6b473](https://gitlab.pepita.io/getrix/mls-site/commit/9b6b4739653451f5773637e0ac1a28695d539204))
* filters for similar ads in performance page ([d475c4b](https://gitlab.pepita.io/getrix/mls-site/commit/d475c4ba0c66980d7fc4c1c0137d4545de3ae404))
* **gx-navigation:** add support to hide old/new menu switch ([cb3732e](https://gitlab.pepita.io/getrix/mls-site/commit/cb3732e563ab38f6f92d92414b9a6e17d22efea3))
* **property:** new filters for API endpoint Similar Ads ([14fadf7](https://gitlab.pepita.io/getrix/mls-site/commit/14fadf7924472f88568d745299d3a42c3b10a0f4))


### Bug Fixes

* **agency-estimates:** address modal alert div size ([a63938f](https://gitlab.pepita.io/getrix/mls-site/commit/a63938fbd9e7e8bb3f33288a8678cd2c0494da4c))
* **agency-estimates:** geocoder, removes comma before address number ([64ac703](https://gitlab.pepita.io/getrix/mls-site/commit/64ac703182f6e3a9db2aa78e921d88dee790cc0f))
* **config:** added missing feature toggle ([7afde4f](https://gitlab.pepita.io/getrix/mls-site/commit/7afde4f23ec8ca2e3ea95dd589753c4cbfeabd18))
* **env:** updated immotop api base url ([ba3d8a0](https://gitlab.pepita.io/getrix/mls-site/commit/ba3d8a09abe9e08e34c370ac87f20a16245bd010))
* **lists:** scroll top when paginate ([d493e31](https://gitlab.pepita.io/getrix/mls-site/commit/d493e31b422da4752ed383de9e887519cdb37cad))
* **menu:** authorizer didn't works correctly and removed unused code ([1ffb978](https://gitlab.pepita.io/getrix/mls-site/commit/1ffb9788dad3f2848cc498087651602cd6981c2c))
* **property-performance:** adds rooms info to similar properties ([36a6f96](https://gitlab.pepita.io/getrix/mls-site/commit/36a6f960faeae2981e75ec3d9d2a34ae6af34eef))
* **property-performance:** remove beta tag if the logged agency has performance extension ([2cf7673](https://gitlab.pepita.io/getrix/mls-site/commit/2cf7673035d9b06ac3c74f83015376a7546ee40a))
* **property-performances:** ui and copy fixes ([142feef](https://gitlab.pepita.io/getrix/mls-site/commit/142feefdbe6e56d4964713b0cf8054f16311de89))
* **property:** restored Logger on Data Provider ([52838a2](https://gitlab.pepita.io/getrix/mls-site/commit/52838a2e9851349ce378d483d3366a30dcaaaf50))

## [5.71.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.70.1...v5.71.0) (2023-10-09)


### Features

* **property-performance:** moved existing mixpanel events to new UI elements,... ([943810f](https://gitlab.pepita.io/getrix/mls-site/commit/943810f0dd944c855d3a021cdf47a86c0081c4ce))


### Bug Fixes

* **multi-agency:** allows agencies with no address infos ([60014b7](https://gitlab.pepita.io/getrix/mls-site/commit/60014b7b9c62fb597fa973246969580ef7e4bf15))
* removed LU from languages keys constant ([75e7b8c](https://gitlab.pepita.io/getrix/mls-site/commit/75e7b8c552a6809698049c88d8b56d4b4e4b5929))

## [5.70.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.70.0...v5.70.1) (2023-10-06)


### Bug Fixes

* check multiAgency data exist in SupportController ([981c5d6](https://gitlab.pepita.io/getrix/mls-site/commit/981c5d687f0663caf2815af5ab0cdf72d1a9ffbd))

## [5.70.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.69.0...v5.70.0) (2023-10-06)


### Features

* **agency:** added support for new Multi Agency feature ([62f3775](https://gitlab.pepita.io/getrix/mls-site/commit/62f3775cb41120931e15c61958a7eedf3575a0d1))
* **agency:** multi agency improvements ([9414557](https://gitlab.pepita.io/getrix/mls-site/commit/94145579ac7e26920c8335a01c795d737f7b469a))


### Bug Fixes

* **multi-agency:** add missing translations, update @getrix/common ([10ad3bc](https://gitlab.pepita.io/getrix/mls-site/commit/10ad3bc655b3f6f307c1e4107e1425fba17cf93a))
* **multisend:** force page to first when filtering and results are changed ([1fc86c1](https://gitlab.pepita.io/getrix/mls-site/commit/1fc86c10b95ad69ae3f9739a4ad8fa3d4eba8006))
* **searches:** fixed pager to be rendered with bad params after... ([a3db3dc](https://gitlab.pepita.io/getrix/mls-site/commit/a3db3dce395c52d604808f0e27a0585e9c95ff9b))
* **translations:** update indomio-translations/cms for multi agency ([8614f93](https://gitlab.pepita.io/getrix/mls-site/commit/8614f937e31d8410175d20f8bcf578599118b4ea))

## [5.69.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.68.4...v5.69.0) (2023-09-28)


### Features

* **lang:** added config for immotop.lu ([d8ceac8](https://gitlab.pepita.io/getrix/mls-site/commit/d8ceac8d3a27a2bd4031c1f0c169027875094c17))


### Bug Fixes

* **invoicing-data:** reverting last changes for countries type ([1a9dae7](https://gitlab.pepita.io/getrix/mls-site/commit/1a9dae7828835a124910b7751a54f3d761672c9f))
* **invoicing-data:** yup validation schema fixed ([c6f3f4e](https://gitlab.pepita.io/getrix/mls-site/commit/c6f3f4e8dac2354d7376eff794a3f51768081a8f))

## [5.68.4](https://gitlab.pepita.io/getrix/mls-site/compare/v5.68.3...v5.68.4) (2023-09-27)


### Bug Fixes

* **estimates:** fixed broken pdf download ([3a49f07](https://gitlab.pepita.io/getrix/mls-site/commit/3a49f07b1ea6e71be9875ed9e5ffabb3d1a6dd31))
* **multisend:** avoid page breaks if any property does not have publicationStatus ([d5994b3](https://gitlab.pepita.io/getrix/mls-site/commit/d5994b391c170f0a6805e8ba661548a2f31eb004))

## [5.68.3](https://gitlab.pepita.io/getrix/mls-site/compare/v5.68.2...v5.68.3) (2023-09-26)


### Bug Fixes

* lookupEndpoint property name revert ([0a5397b](https://gitlab.pepita.io/getrix/mls-site/commit/0a5397b9946a62f89e567393295e37601468e8f2))
* **portal-properties:** missing React import for mobile view ([80c886b](https://gitlab.pepita.io/getrix/mls-site/commit/80c886b151ea3550edf94a93bec50fab46857f8d))

## [5.68.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.68.1...v5.68.2) (2023-09-26)


### Bug Fixes

* **estimates:** otherwise bad type into validationSchema ([36ecb9c](https://gitlab.pepita.io/getrix/mls-site/commit/36ecb9c3e47e0d77b7bbd76f411670c33c6084e8))

## [5.68.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.68.0...v5.68.1) (2023-09-25)


### Bug Fixes

* **property-performance-similar-ads:** handles prices on request ([0879439](https://gitlab.pepita.io/getrix/mls-site/commit/0879439a67a363dbacbdf8941fa5d6b05e4c8b43))

## [5.68.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.67.0...v5.68.0) (2023-09-25)


### Features

* added licenses lookup ([2f4d7fa](https://gitlab.pepita.io/getrix/mls-site/commit/2f4d7fa821bd2975f2700d1ac3091930e4d953be))
* **performances:** new printable version for performance detail ([4c374db](https://gitlab.pepita.io/getrix/mls-site/commit/4c374dbfefc3d482a0624966e8386a5ca3cd06fb))


### Bug Fixes

* **acquistion-private:** note ([49e2b99](https://gitlab.pepita.io/getrix/mls-site/commit/49e2b998c89b9d754741b8b14fae1962429395ed))
* immovisita header title ([ace7f58](https://gitlab.pepita.io/getrix/mls-site/commit/ace7f58d4f91f40900c03120dd8f0227eb3593c7))
* **performance:** fixed button on auctions and new constructions ([216455c](https://gitlab.pepita.io/getrix/mls-site/commit/216455c54b57080c599aabca55a4ac2495485e9c))
* **remote-visits:** re-enables mixpanel track event ([f65861a](https://gitlab.pepita.io/getrix/mls-site/commit/f65861a675bd5a98aad3e27a59c2b337dd405080))

## [5.67.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.66.1...v5.67.0) (2023-09-20)


### Features

* **agency:** return HTTP_FORBIDDEN if agency flag noAdCopy is set ([e8f47c4](https://gitlab.pepita.io/getrix/mls-site/commit/e8f47c48def02c87b1ffefe56ad164968051f9f9))
* tanstack/react-query v4 ([7a6b82e](https://gitlab.pepita.io/getrix/mls-site/commit/7a6b82e66a02b36fade14fa8afe92881288f5ad7))


### Bug Fixes

* **active-search-creation:** fixed typologies lookup ([6801d94](https://gitlab.pepita.io/getrix/mls-site/commit/6801d9407f046bbe67de6530cb031713ecea1899))
* **indomio:** menu logic ([3266f61](https://gitlab.pepita.io/getrix/mls-site/commit/3266f61b3825e0b104d847ee6273e140d55051d2))
* preventDefault of undefined ([e18cf96](https://gitlab.pepita.io/getrix/mls-site/commit/e18cf96a5fb7c7e355d8429188258705fa40d830))
* removed undefined notifyError ([9a1ccc0](https://gitlab.pepita.io/getrix/mls-site/commit/9a1ccc08ce7e5112248ad9fa5c8eff6f8fe462e8))
* **spain:** header routes ([c1c3038](https://gitlab.pepita.io/getrix/mls-site/commit/c1c30384aac93d4d5042d817cb4378ad8d1ed0a9))

## [5.66.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.66.0...v5.66.1) (2023-09-18)


### Bug Fixes

* **invoicing-data:** fixed bad formatting after form submission for share capital field ([c86ea05](https://gitlab.pepita.io/getrix/mls-site/commit/c86ea0574501d4c0b73c8f15dc51ce8318addff2))

## [5.66.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.65.1...v5.66.0) (2023-09-18)


### Features

* **portal-properties:** list and performance detail optimizations ([3740f4d](https://gitlab.pepita.io/getrix/mls-site/commit/3740f4d8019df5c43ddd09259d3fe352bda6e819))


### Bug Fixes

* **acquisitions-privates:** restores typology field on filters ([ea4ffa7](https://gitlab.pepita.io/getrix/mls-site/commit/ea4ffa7e4bf3bb5dde7111b96ce5f28eacc62c2f))
* **ad_portal:** ad portal before string ([326924d](https://gitlab.pepita.io/getrix/mls-site/commit/326924ddc4c74b0e637229c4b9dba70831593f74))
* **administration-contract:** reverting setFeaturesConfig reducer, fixed type ([798495e](https://gitlab.pepita.io/getrix/mls-site/commit/798495eb3e445a4ea007554d1e7f984b2170d785))
* aggiunto parametro mancante ([bb0cc8a](https://gitlab.pepita.io/getrix/mls-site/commit/bb0cc8a412e5845043c5ea3634c3d1c9396aff1d))
* corretto puntamento api dev es ([6a2195c](https://gitlab.pepita.io/getrix/mls-site/commit/6a2195ced7e4b71c5a5b220f412f224deaeb85d7))
* **menu:** add auction catalogue detail menu ([941fca7](https://gitlab.pepita.io/getrix/mls-site/commit/941fca7719b6fdac0fdd6fe52ec22ffc9d740d9f))
* **menu:** remove first level link ([a94902b](https://gitlab.pepita.io/getrix/mls-site/commit/a94902bb3ff2d6430e2efec67f16072d0ea34b7e))
* **messaging:** sentry typeError setting status on undefined ([5894174](https://gitlab.pepita.io/getrix/mls-site/commit/5894174cbec751b128b0c6ba7e7caf73d3bebfaf))
* scheduled visit modal blank ([80a9439](https://gitlab.pepita.io/getrix/mls-site/commit/80a9439703b10059ae9968325e612251127bf2bd))
* **ts-ignore:** enable ts ignore in gx-navigation ([d20c6bf](https://gitlab.pepita.io/getrix/mls-site/commit/d20c6bf05ad6acae4eb61f5f1c970ffb4ffaa93d))

## [5.65.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.65.0...v5.65.1) (2023-09-13)


### Bug Fixes

* featureConfig missing in slice reducer ([93b7722](https://gitlab.pepita.io/getrix/mls-site/commit/93b7722bb13448c0fae45148919fca54555e8da6))
* **youdomus:** menu ([0eb2801](https://gitlab.pepita.io/getrix/mls-site/commit/0eb28010799af1a2fbe00db2b7bb79393f4d32b3))

## [5.65.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.64.0...v5.65.0) (2023-09-12)


### Features

* useIsNewMenuActive ([54799bd](https://gitlab.pepita.io/getrix/mls-site/commit/54799bd4f37f3661a2a04a35bfdbb92b38fad85c))

## [5.64.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.63.3...v5.64.0) (2023-09-11)


### Features

* **snackbar:** new ui and update common ([94de4aa](https://gitlab.pepita.io/getrix/mls-site/commit/94de4aa760671d5cc5553195f60f78586935c174))
* **youdomus:** enabled auctions ([6082418](https://gitlab.pepita.io/getrix/mls-site/commit/6082418df6abf20b89d5bc6d526f5783f66ebb5f))
* **youdomus:** enabled new agency ([6158b27](https://gitlab.pepita.io/getrix/mls-site/commit/6158b2743c5af8105f446214bc2469e4458bfab1))


### Bug Fixes

* replaced old "/suggest" city autocomplete api ([53ffb9f](https://gitlab.pepita.io/getrix/mls-site/commit/53ffb9f84f85c762b3d1c04593e26ea6937d59a7))
* **youdomus:** mobile header function removed ([bc7d20d](https://gitlab.pepita.io/getrix/mls-site/commit/bc7d20dae6edb0b964c85e003e7450ee54383c15))

## [5.63.3](https://gitlab.pepita.io/getrix/mls-site/compare/v5.63.2...v5.63.3) (2023-09-06)

## [5.63.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.63.1...v5.63.2) (2023-09-06)


### Bug Fixes

* **build:** add app-shell to all js webpack entries ([9117aa2](https://gitlab.pepita.io/getrix/mls-site/commit/9117aa2f67f2651c5371e504e57aba5219863ebf))

## [5.63.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.63.0...v5.63.1) (2023-09-06)


### Bug Fixes

* **build:** missing deps on set-password module ([5eea708](https://gitlab.pepita.io/getrix/mls-site/commit/5eea708ca83e1a55364cfc17fc9cde9dcc0b1548))
* **portal-properties:** improve quality modal ([ecb0a0c](https://gitlab.pepita.io/getrix/mls-site/commit/ecb0a0c3fa93b00a0e4857911832876bc3aa91b8))
* types fix in admin-invoice portalOrdering searches settings-media ([3fc0e8f](https://gitlab.pepita.io/getrix/mls-site/commit/3fc0e8f06bd1cd79a66a6a45710f5cd53024f701))
* **youdomus:** menu routes ([8c06114](https://gitlab.pepita.io/getrix/mls-site/commit/8c06114e785e213f71e00617c5b4f5594a45383e))
* **youdomus:** menu voice hidden ([68f9176](https://gitlab.pepita.io/getrix/mls-site/commit/68f9176392d5ca304ee5b6785936958a9b0ca92f))

## [5.63.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.62.0...v5.63.0) (2023-09-05)


### Features

* **mixpanel:** added channel to log events tracker informations ([adc95fd](https://gitlab.pepita.io/getrix/mls-site/commit/adc95fde4eb22e44f4c1fbc01ac66c9c978020b8))
* re-introduce linter ([760dc16](https://gitlab.pepita.io/getrix/mls-site/commit/760dc16b5ed3e2f3692aa7cb34fd89875a61db49))


### Bug Fixes

* **bulk-performances:** dataMapper adIds and controller response ([6979541](https://gitlab.pepita.io/getrix/mls-site/commit/6979541b240910c370d2bf4cb3fe64c877483859))

## [5.62.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.61.1...v5.62.0) (2023-09-05)


### Features

* added bulk endpoint for retrieve ads performance ([236d565](https://gitlab.pepita.io/getrix/mls-site/commit/236d565b42317b0ccfc77c70798768eafab6abbb))
* **ci:** added cache to gitlab-ci build job ([800975a](https://gitlab.pepita.io/getrix/mls-site/commit/800975adc89f0ef97566e28b107bbd5fc63d5519))
* getrix common link/unlink scripts ([1791f73](https://gitlab.pepita.io/getrix/mls-site/commit/1791f731a8cbf477e97d300fc933c355df818389))
* **gx-design:** update icon and alert ([5688dfd](https://gitlab.pepita.io/getrix/mls-site/commit/5688dfdfc8ed34a05218f3cd3a161e02e8be6480))
* **gx-navigation:** add wildcards for query params ([48c17e1](https://gitlab.pepita.io/getrix/mls-site/commit/48c17e12847df0fa3a537c74e15efcbb442d0771))
* **gx-navigation:** youdomus routes handling ([511776e](https://gitlab.pepita.io/getrix/mls-site/commit/511776e789ee29212a45f8d0e9f70e2fbb292c7e))
* webpack 5 upgrade + code splitting + selective build scripts + sentry ci ([205ae82](https://gitlab.pepita.io/getrix/mls-site/commit/205ae8296d20db1cb55195bc331c7c56407bbeff))
* **youdomus:** added endpoint to check if ad can use service ([4303c3a](https://gitlab.pepita.io/getrix/mls-site/commit/4303c3a45c9dfc5d31a1facf476373e846a9d63a))
* **youdomus:** obtain cadastral data button ([3c35d55](https://gitlab.pepita.io/getrix/mls-site/commit/3c35d55a4fc22cebb9f6cae73b6bbfcd36ac6c36))


### Bug Fixes

* adds module dependencies on build config, adds missing route on gx-navigation ([26def44](https://gitlab.pepita.io/getrix/mls-site/commit/26def44e2d4569e374ac320c305c89b6bf4edca5))
* **agency-estimates:** allow to trigger search just from the second char ([dc146f6](https://gitlab.pepita.io/getrix/mls-site/commit/dc146f6500c26efc632b3958145e898338651364))
* aggiornati hostname ([e61f769](https://gitlab.pepita.io/getrix/mls-site/commit/e61f7696fda84593eb953fcb8fb51990e4d7d093))
* **alert:** fix vari alert ([b91223e](https://gitlab.pepita.io/getrix/mls-site/commit/b91223e72a6639ce085a05de5feefe329b9a2088))
* ansible installation of sentry-cli ([8f06ab8](https://gitlab.pepita.io/getrix/mls-site/commit/8f06ab865e1e931ccf8710ca3c957bbf9d52e2fb))
* ansible sentry and update ansible-cli to version 2.10 ([19a05ff](https://gitlab.pepita.io/getrix/mls-site/commit/19a05ff57133eb041a26219d171e82c5383731c8))
* **build:** renames photoplan and virtual-tour chunks ([6d017b3](https://gitlab.pepita.io/getrix/mls-site/commit/6d017b3e6e839eb36d790c7673dbc030ffeda442))
* gitlab-ci build cache ([b42d820](https://gitlab.pepita.io/getrix/mls-site/commit/b42d8208c928179f6af7bcd5cbcd0aa336a75253))
* **menu:** menu active submenu mechanism ([7e4e4a5](https://gitlab.pepita.io/getrix/mls-site/commit/7e4e4a56493aed84afb6211f1c96221c7e423cf3))
* **menu:** menu scroll block logic ([b134081](https://gitlab.pepita.io/getrix/mls-site/commit/b13408106eeb5b978ef18e100b77211256dd6f05))
* **mixpanel:** changed mixpanel track endpoint ([f694f3b](https://gitlab.pepita.io/getrix/mls-site/commit/f694f3b5296895f9b06be421f39199b3d3df98e5))
* **mixpanel:** menu_version had a wrong value ([0f3b7a2](https://gitlab.pepita.io/getrix/mls-site/commit/0f3b7a240dbdfcd07feffdc70b58398a80af1a75))
* **modifica-utenti:** datipicker css build asset ([6fe3412](https://gitlab.pepita.io/getrix/mls-site/commit/6fe34123845515df901c02da4aec97f5b468b0ff))
* **multisend:** changed ListGlobally authentication endpoint ([034f5aa](https://gitlab.pepita.io/getrix/mls-site/commit/034f5aa3a35ae31e6b8a89dd6481a7a2e48ca837))
* pages > messaging > types fix and icons ([1b4ea89](https://gitlab.pepita.io/getrix/mls-site/commit/1b4ea89cff3c603e7d19b99def8c522e4cdbfa15))
* **pagination-bar:** updates @gx-design/pagination-bar ([035da22](https://gitlab.pepita.io/getrix/mls-site/commit/035da2226ae3fc49640d3734548287f4c46438d9))
* **property:** ads starts should use only status 1 (STATUS_IMMOBILIARE_ACTIVE) ([3946dfb](https://gitlab.pepita.io/getrix/mls-site/commit/3946dfbd45ee0bf060c79a7dabfceefbbfef31ef))
* **property:** limit shared pagination model admitted values ([6d81b1c](https://gitlab.pepita.io/getrix/mls-site/commit/6d81b1c8c73515f73e07de5b13442c293f3b5c12))
* **property:** total value in Performance response ([acd93bd](https://gitlab.pepita.io/getrix/mls-site/commit/acd93bd0a401c6bba809d1dd434cb823040d76fd))
* **report:** icone prezzo medio ([1ac82c5](https://gitlab.pepita.io/getrix/mls-site/commit/1ac82c5d2a50beab2e4647bd5e443a72d2973f45))
* **report:** menu add url ([121b100](https://gitlab.pepita.io/getrix/mls-site/commit/121b10004b45198e3b8b2740f7348457dbd72fad))
* **sales-req:** tag style update ([b3b8506](https://gitlab.pepita.io/getrix/mls-site/commit/b3b8506176bab3d8d1a1d485bbda3b042bc486fb))
* sentry enabled flag always filled ([106fe00](https://gitlab.pepita.io/getrix/mls-site/commit/106fe00770b3d082c36e1436575cdfd01df014aa))
* sentry version in webpack build ([8117aad](https://gitlab.pepita.io/getrix/mls-site/commit/8117aad6cd3732299f885f5b86308074b2601c52))
* sentryEnabled in twig extension ([740fe98](https://gitlab.pepita.io/getrix/mls-site/commit/740fe98f28a111c4abaf97a5785b5e8bee53be54))
* **settings-headquarters:** new cities api ([bd4e2ff](https://gitlab.pepita.io/getrix/mls-site/commit/bd4e2ff1f9e0c3403c48d3c20abb069d92698c7d))
* **webpack:** missing workers copy ([f1ae484](https://gitlab.pepita.io/getrix/mls-site/commit/f1ae484c01830b6c1edf024cac2ce5d6790ea72c))


### CI/CD

* temporary removes sentry jobs ([c1c26fa](https://gitlab.pepita.io/getrix/mls-site/commit/c1c26fa1726618e469c495f964f6994b19fae29c))

## [5.61.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.61.0...v5.61.1) (2023-08-01)


### Bug Fixes

* **multisend:** allow to publish on immobiliare.it even if premium spaces are over ([ffc77e1](https://gitlab.pepita.io/getrix/mls-site/commit/ffc77e17570869678421516d5ee5164c478943a7))
* **user-modal:** removed double init user modal + alert styling ([371306e](https://gitlab.pepita.io/getrix/mls-site/commit/371306e5c08289759060d7b6be0b9127a19503f6))

## [5.61.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.60.2...v5.61.0) (2023-07-31)


### Features

* **gx-nav:** setFixed cross tab sync ([6547c05](https://gitlab.pepita.io/getrix/mls-site/commit/6547c05f63729a8cbd4f9877e91d74021820cbd9))


### Bug Fixes

* **register:** rem typo ([c620bbf](https://gitlab.pepita.io/getrix/mls-site/commit/c620bbfa9ebe0a4c4e6b2f06b899082b3d022bdf))

## [5.60.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.60.1...v5.60.2) (2023-07-31)


### Bug Fixes

* **language:** switch on language selection on prod env ([47fdd09](https://gitlab.pepita.io/getrix/mls-site/commit/47fdd09cfec88f712437b9f085449cd2ca098436))
* **youdomus:** youdomus extension check ([81a3adf](https://gitlab.pepita.io/getrix/mls-site/commit/81a3adf7c8439f8de0f6c7892d17667b5f66b72d))

## [5.60.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.60.0...v5.60.1) (2023-07-28)


### Bug Fixes

* **gx-navigation:** class check to classList ([d48d87a](https://gitlab.pepita.io/getrix/mls-site/commit/d48d87a5c301cad490dd7b2158da075a27840ee0))
* **mixpanel:** menu_version had a wrong value ([aa5d419](https://gitlab.pepita.io/getrix/mls-site/commit/aa5d419fe95d96adf8b82dfdb3a7221889259878))
* **multisend:** fallback ad image and portal image url on other portals modal ([1076699](https://gitlab.pepita.io/getrix/mls-site/commit/1076699f85c456ff58f6a8966b290b6de328a822))
* **portfolio:** alert modal ([c7359d2](https://gitlab.pepita.io/getrix/mls-site/commit/c7359d2a414b7f1e581b60bbcfafa9f9a4fcd1dd))
* **property:** removed default sorting value in shared model ([bcb2976](https://gitlab.pepita.io/getrix/mls-site/commit/bcb2976a0723cff3b130bb60b7fd8a9865bda31c))
* **report:** ask help position ([24ccb3b](https://gitlab.pepita.io/getrix/mls-site/commit/24ccb3b8620e09245406d270e08035860cb88b2c))
* **toolbar:** results align ([8f6955f](https://gitlab.pepita.io/getrix/mls-site/commit/8f6955f1b09f485e6b3e4246e0b5fe9e80a9fbd1))

## [5.60.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.59.0...v5.60.0) (2023-07-27)


### Features

* added user menu hash to rest/profile/me response ([3ecdd13](https://gitlab.pepita.io/getrix/mls-site/commit/3ecdd13822115cf85dcb637b018a6daa297c295a))
* **date:** added new DateFormatter to use standard timezone and format ([c51c945](https://gitlab.pepita.io/getrix/mls-site/commit/c51c945103028a0fa93949f9f33ac92229f92a98))
* **gx-navigation:** gx navigation menu integration ([e83653f](https://gitlab.pepita.io/getrix/mls-site/commit/e83653f61b1cea0935de808ee197cab8e13c7d3e))
* **menu:** created endpoint to get menu structure ([d56650a](https://gitlab.pepita.io/getrix/mls-site/commit/d56650ae50db1c96aac653fa0e7662b61a58bd65))
* **menu:** enable menu prod ([98cce94](https://gitlab.pepita.io/getrix/mls-site/commit/98cce941603a186e204ea1aa750a7985d56e1728))
* **mixpanel:** added 'menu_version' property to all events except 'menu_switch_menu' ([484a144](https://gitlab.pepita.io/getrix/mls-site/commit/484a144ee02b259e41ff10da330b2ec8ddbde6c0))
* **mixpanel:** refactoring of PortalNewConstructions and PortalAuctions with MixpanelProvider ([6491274](https://gitlab.pepita.io/getrix/mls-site/commit/6491274b3dfe0883e342a9551107f75f4b3271b6))
* **multisend:** refactor "annunci"and "nuove costruzioni" ([60f8d70](https://gitlab.pepita.io/getrix/mls-site/commit/60f8d7023fb311029caede6c3415825b070852a4))
* **property:** added new endpoint to get Similar Properties passing an adId ([2f63521](https://gitlab.pepita.io/getrix/mls-site/commit/2f63521784925ed8159ca54fe486e309854021a5))


### Bug Fixes

* **acquisition:** filter button mobile ([3dfe784](https://gitlab.pepita.io/getrix/mls-site/commit/3dfe7848c4cc9515d6677a903254e8c9c03ed5e6))
* **acquisition:** trans mapping acquisition section ([6e59e81](https://gitlab.pepita.io/getrix/mls-site/commit/6e59e81ded434ee7a8c11d51ec5832a7c1383fa2))
* **acquisizione-valuta:** adds array keys exists ([81ebed0](https://gitlab.pepita.io/getrix/mls-site/commit/81ebed0063ee35a137f6a7c68e917cb78f7638b5))
* **ag-estimates:** added missing dropdown position to full header ([1ff1003](https://gitlab.pepita.io/getrix/mls-site/commit/1ff1003412297d0afcc66d0de1d640a6b46ecc04))
* disable menu switch when GX_NAVIGATION_MENU_ENABLED is 0 ([2977eec](https://gitlab.pepita.io/getrix/mls-site/commit/2977eec26fd1aabd2f9adaccfc4ea758ede67ffe))
* display messages after switch menu old/new ([4ff50d0](https://gitlab.pepita.io/getrix/mls-site/commit/4ff50d0f297df651b01eebd69cba4f3139ba7bd3))
* **estimates:** feedbacks int value to string ([bc3012e](https://gitlab.pepita.io/getrix/mls-site/commit/bc3012e5edfa9d081175d242c7873cba75fb6afa))
* **gx-navigation:** fixed some issues ([94ffb0a](https://gitlab.pepita.io/getrix/mls-site/commit/94ffb0a06b81037232be987f422235609c17a91f))
* **gx-navigation:** fixed some issues ([871318f](https://gitlab.pepita.io/getrix/mls-site/commit/871318fade3eb371a17ea87d545975174faf7ce4))
* **gx-navigation:** fixed typo error ([da5d4b7](https://gitlab.pepita.io/getrix/mls-site/commit/da5d4b7ca031af3608ffc076c9c0a88ab03b11bc))
* **menu:** add missing acquisition routes ([c528ed7](https://gitlab.pepita.io/getrix/mls-site/commit/c528ed72cdf33f3d051575bda6338ec4f1d948dc))
* **menù:** adds estimate detail route ([d67e953](https://gitlab.pepita.io/getrix/mls-site/commit/d67e9535d5a96cc911641eb77dfc53d3d2d7ef13))
* **menu:** fixed some issues ([f3ffe76](https://gitlab.pepita.io/getrix/mls-site/commit/f3ffe7687dfc16ad25c3b7b0af01f9f647a72659))
* **menù:** removes mounts of menu components ([1e194ba](https://gitlab.pepita.io/getrix/mls-site/commit/1e194ba7e0ae123bb3e203777737a07a1ae9ce3a))
* **menù:** sold and rented label key ([5b73ef8](https://gitlab.pepita.io/getrix/mls-site/commit/5b73ef8cb4d51cdc46f5338194f9557f20fe288b))
* **menù:** typo on auction list endpoint ([5e9afde](https://gitlab.pepita.io/getrix/mls-site/commit/5e9afde35acc7342fda047612a504a7674346230))
* **menu:** various fixes ([9c344ea](https://gitlab.pepita.io/getrix/mls-site/commit/9c344ea278216e2a6991b8b79a1c39ec75fab59e))
* **menu:** yaml fixes ([6e50db5](https://gitlab.pepita.io/getrix/mls-site/commit/6e50db5cca43504fce88ef5343361a0bd9ab2210))
* **menù:** zone edit route ([4e99342](https://gitlab.pepita.io/getrix/mls-site/commit/4e99342fe93213141b951fcc2fc8a864ab9714fc))
* **messaging:** header margin with new menu ([2b7e6dd](https://gitlab.pepita.io/getrix/mls-site/commit/2b7e6dd826465164955f894ac7a49e4b67736969))
* **messaging:** preview ([3b382ba](https://gitlab.pepita.io/getrix/mls-site/commit/3b382bada606a11b45474e43dad2b6be72511521))
* **multisend:** adds module check on property action ([0e3681d](https://gitlab.pepita.io/getrix/mls-site/commit/0e3681d4e324aa0fe113dc7d738f4e755273a297))
* **multisend:** adds module check on property action ([df83f45](https://gitlab.pepita.io/getrix/mls-site/commit/df83f4515139f5aa94d394bd840ee4b79cfbb12f))
* **multisend:** empty portal cell handling ([d35cc47](https://gitlab.pepita.io/getrix/mls-site/commit/d35cc477f5d49ff10ae50f2e76b3ef00b02ac068))
* **multisend:** portal icon url on new constructions list ([0ca0144](https://gitlab.pepita.io/getrix/mls-site/commit/0ca0144b5fc37d88f2957ec55805ffbf3777a3e7))
* **multisend:** properties handle errors, modal update, property image ([d001091](https://gitlab.pepita.io/getrix/mls-site/commit/d001091bed897e95f26adc2b30eee75ad0974a83))
* **multisend:** removes module check on multisend-portals controller ([40444b3](https://gitlab.pepita.io/getrix/mls-site/commit/40444b366659daf69a1477c754c9aa6d2c305fc1))
* **multisend:** restores right module on actions ([8422f4b](https://gitlab.pepita.io/getrix/mls-site/commit/8422f4b614bcc7f438b0bf160ec0abe92ad91d87))
* **multisend:** use right shared pagination model for API requests ([8268747](https://gitlab.pepita.io/getrix/mls-site/commit/8268747253307d5a1e52b9e4048f8a94e00dc380))
* **portal-properties:** few similar ads popover content based on async call ([868b4aa](https://gitlab.pepita.io/getrix/mls-site/commit/868b4aa9ba884c71420b27ff19be3631e1753434))
* **searches:** missing CATEGORIA_RESIDENZIALE in gtxConstants needed in filters ([a836558](https://gitlab.pepita.io/getrix/mls-site/commit/a836558d90cde23237351088d3b3187344816d9e))
* **valutazioni:** terraceBalcony value formatter ([29b79bb](https://gitlab.pepita.io/getrix/mls-site/commit/29b79bb77436db09bef172c2f282aa33cb4a091b))
* **youdomus:** adds new app params and changed pro param ([55e4d36](https://gitlab.pepita.io/getrix/mls-site/commit/55e4d361b5c067c04c57d83fc446771d678ace5a))


### Refactor

* new input with datepicker ([2b2ea7f](https://gitlab.pepita.io/getrix/mls-site/commit/2b2ea7f277ef441441e1d6a7637b99b4a63d92b7))

## [5.59.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.58.2...v5.59.0) (2023-07-18)


### Features

* **mixpanel:** refactoring with Context and added events for Reportistica ([a0b6ce0](https://gitlab.pepita.io/getrix/mls-site/commit/a0b6ce0093118fbaf495c1793bdf557a245efc1e))
* **private:** throw exception if query param City is not set in filters ([80d3d2e](https://gitlab.pepita.io/getrix/mls-site/commit/80d3d2e06291ead3efbed2c2c19e3d62104f20dd))
* **token:** icon-size ([2f04ecb](https://gitlab.pepita.io/getrix/mls-site/commit/2f04ecb2b31b241175df8016b25399de0aee1eb2))


### Bug Fixes

* **acquisition:** set default city if both province and city are empty ([72adb42](https://gitlab.pepita.io/getrix/mls-site/commit/72adb42f056096e170cbcbe80fd4e1982557f3b7))
* **acquisition:** toolbar ([b384931](https://gitlab.pepita.io/getrix/mls-site/commit/b3849313cac9b78337842d23f8cd29fb9a3dff9a))
* cambiati puntmaneti memcache di sviluppo ([a244747](https://gitlab.pepita.io/getrix/mls-site/commit/a244747ca5bbb282b04bc87f6f3a1ea89a5895d3))
* **customer-add:** missing translations handling ([76e8889](https://gitlab.pepita.io/getrix/mls-site/commit/76e8889b58520fd98720f24c74dcca6189a0ddb1))
* **messaging:** missing translations ([52e2763](https://gitlab.pepita.io/getrix/mls-site/commit/52e2763b043f7cc2a1701155aa56276eb7c2bb9f))
* **portal-properties:** missing filters translations ([51f44b4](https://gitlab.pepita.io/getrix/mls-site/commit/51f44b421a0006d13ce85c8bb9072e4d0669dcac))
* **searches-active:** missing translations ([c2d42ff](https://gitlab.pepita.io/getrix/mls-site/commit/c2d42ff4e66c708a21fb3f97dbb83ec709009e29))

## [5.58.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.58.1...v5.58.2) (2023-07-11)


### Bug Fixes

* **youdomus:** app parameter ([6fe14d1](https://gitlab.pepita.io/getrix/mls-site/commit/6fe14d1bab4a207ce9bbde7b57b345a0ea35c22b))

## [5.58.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.58.0...v5.58.1) (2023-07-11)


### Bug Fixes

* app.gx and logo error on gestionale-es ([ed859eb](https://gitlab.pepita.io/getrix/mls-site/commit/ed859ebb60fdaeaa05fa404db5f49c2d9fef65f2))
* corrected stop action for review_app ([b93a652](https://gitlab.pepita.io/getrix/mls-site/commit/b93a652237f3566a5ebab885b29546d03945b3cc))
* ordering mapping multisend ([f890ede](https://gitlab.pepita.io/getrix/mls-site/commit/f890ede4cddeb7fe98068bbbb1557e01636bf6bb))
* sorting mapping multisend ([d620ee7](https://gitlab.pepita.io/getrix/mls-site/commit/d620ee7d6fcba262b7bd40a51bf3ae205fb3463a))

## [5.58.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.57.0...v5.58.0) (2023-07-10)


### Features

* added fake health check for docker ([866b0e3](https://gitlab.pepita.io/getrix/mls-site/commit/866b0e3a8837089fd1227374aaf4aa6fee755376))
* **ci:** add default settings to all jobs of ci ([192421f](https://gitlab.pepita.io/getrix/mls-site/commit/192421fe30909163507ffaedc13fd4f648769812))


### Bug Fixes

* **dashboard:** missing translations ([2f96673](https://gitlab.pepita.io/getrix/mls-site/commit/2f96673c74e5b3b939e6a99f65b31bb2070a0993))
* **landing:** adds app subtitle ([b1d1654](https://gitlab.pepita.io/getrix/mls-site/commit/b1d16545a7a62eed3aea6c8b7dbef3c32a7ed2bc))
* **reset-password:** added missing start profiler, no more crash on reset password action ([6fecd1e](https://gitlab.pepita.io/getrix/mls-site/commit/6fecd1eed485d76d266e44842ee1f00a399382cc))

## [5.57.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.56.0...v5.57.0) (2023-07-06)


### Features

* aggiornato repo traduzioni cms e sostituito repo contents con cms-contents ([7f09a36](https://gitlab.pepita.io/getrix/mls-site/commit/7f09a3681e4b7cd2274070ff9db46f6354968bad))


### Bug Fixes

* makes app products names dynamic ([fa11838](https://gitlab.pepita.io/getrix/mls-site/commit/fa118384929d53c3debe499b51c2fea39700b0dc))
* visibility key mapping ([6165b81](https://gitlab.pepita.io/getrix/mls-site/commit/6165b811364213df7bab530da45adfffd1ca07d3))

## [5.56.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.55.0...v5.56.0) (2023-07-05)


### Features

* remove useless steps for reviewapp docker image startup ([bfaaf7f](https://gitlab.pepita.io/getrix/mls-site/commit/bfaaf7f9e695bd83103bcbeafce1ec489b09d83c))


### Bug Fixes

* no extra parameters on functions ([e9dac52](https://gitlab.pepita.io/getrix/mls-site/commit/e9dac523ea84e76162a5ac7b6335802f461f2281))
* **portal-properties:** no more property preview/print crash when property has no address ([a02110b](https://gitlab.pepita.io/getrix/mls-site/commit/a02110b2d3ae90160e7c9351ec57a80c824d186f))

## [5.55.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.54.3...v5.55.0) (2023-07-04)


### Features

* **mixpanel:** intercept and alter Appcues events ([47eda56](https://gitlab.pepita.io/getrix/mls-site/commit/47eda56bd1a901edf9f88e4f625ede122f78c939))


### Bug Fixes

* **property-performance:** performance filter visibile also to backoffice access ([190a384](https://gitlab.pepita.io/getrix/mls-site/commit/190a3849c86d15cfe7a0870d10eb9f0a389bd843))

## [5.54.3](https://gitlab.pepita.io/getrix/mls-site/compare/v5.54.2...v5.54.3) (2023-06-28)


### Bug Fixes

* **property-performance:** opens stats and performance routes to backoffice access ([6fa2d67](https://gitlab.pepita.io/getrix/mls-site/commit/6fa2d67bec77f547cbb71c821825254ec6fbdf92))

## [5.54.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.54.1...v5.54.2) (2023-06-27)


### Bug Fixes

* **agency-estimates:** missing constants ([52ebe84](https://gitlab.pepita.io/getrix/mls-site/commit/52ebe84a86a9a7c4ebb7b454e9e25db72779d512))

## [5.54.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.54.0...v5.54.1) (2023-06-27)


### Bug Fixes

* **gx-navigation:** header buttons ([dde84ab](https://gitlab.pepita.io/getrix/mls-site/commit/dde84ab8e040f481626f257180fea20657e13e2f))

## [5.54.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.53.1...v5.54.0) (2023-06-27)


### Features

* disabled onboarding on all envs ([02d5226](https://gitlab.pepita.io/getrix/mls-site/commit/02d522633f613643dad5699dbb11d756f4a67c52))
* **immobili:** add gx-navigation header configuration ([d43c2e8](https://gitlab.pepita.io/getrix/mls-site/commit/d43c2e824d67b298af00d1c4b0b3816b465f9872))
* **language-selection:** selezione lingua nella sezione impostazioni generale ([c169c00](https://gitlab.pepita.io/getrix/mls-site/commit/c169c004654f190a5fbaba02d57014e4d06e5d5b))
* **messaging:** add gx-navigation header ([ff30292](https://gitlab.pepita.io/getrix/mls-site/commit/ff30292acfcab5e9a006dd5377aa10a2782be35f))
* **multisend:** add gx-navigation header ([978c2a4](https://gitlab.pepita.io/getrix/mls-site/commit/978c2a4ec9b15eb345974779f2d8cf8ab4a600aa))
* **portal-properties:** add gx-navigation header configurations ([9d72684](https://gitlab.pepita.io/getrix/mls-site/commit/9d726843975de1a62cc4964ed4ce58e6e2130184))
* **property-performance:** adds raise and view performance mixpanel event tracking ([73fcf37](https://gitlab.pepita.io/getrix/mls-site/commit/73fcf374d082724646a35e47d5f9931309c435b7))
* **property-performance:** list status and performance detail  with few similar properties. Deletes property status info from description ([0903622](https://gitlab.pepita.io/getrix/mls-site/commit/0903622f5d9a61bd3fc35bc1c78774d4545c2dc4))
* **property-performance:** makes feature available only to agencies with active extension ([4276245](https://gitlab.pepita.io/getrix/mls-site/commit/427624583352c2b6c47122545493c2c8e16d9bd9))
* **property:** added performance invalidity reason ([b29bb4b](https://gitlab.pepita.io/getrix/mls-site/commit/b29bb4ba3a724e514eabd4a5e3181dad2596c075))
* **searches:** add gx-navigation configuration ([069c8cf](https://gitlab.pepita.io/getrix/mls-site/commit/069c8cf61b5942f3fb83063d40383a3f5bc22b2e))


### Bug Fixes

* **calendar:** color hover ([347b6df](https://gitlab.pepita.io/getrix/mls-site/commit/347b6dfa3e0d0a3328a694dd0bd17739f12ad038))
* count on null ([b5abcbb](https://gitlab.pepita.io/getrix/mls-site/commit/b5abcbb01eb711a2d0bdb7cebf2dd03351fbe60c))
* from trans(label.ad_portal) to portal name coming from parameters ([510ee0c](https://gitlab.pepita.io/getrix/mls-site/commit/510ee0c221a5e84e49afcb38540fbe4a1dd3bc28))
* missing translations ([22f24e4](https://gitlab.pepita.io/getrix/mls-site/commit/22f24e414fdc6bbc59b729a258850b9c7a97c1de))
* **performance:** head-section ([81914cb](https://gitlab.pepita.io/getrix/mls-site/commit/81914cb2f354b462a365ed011d57aaf045cf2d52))
* **property-performance:** adds other tipologies to redidential labels group function ([6c7371a](https://gitlab.pepita.io/getrix/mls-site/commit/6c7371a63f37894aaff0738d94e7d81d2a287d75))
* **property-performance:** detail pdf print name ([4b4f88d](https://gitlab.pepita.io/getrix/mls-site/commit/4b4f88d42e533f7da2b85da9a122bcc2eb8f1d8c))
* **property-performance:** label beta visibile only from backoffice access ([b592467](https://gitlab.pepita.io/getrix/mls-site/commit/b5924671cfe70495eecad0de015c1b180272d621))
* **property-performance:** maps to houses or apartaments some specific typologies ([cdccafa](https://gitlab.pepita.io/getrix/mls-site/commit/cdccafa53587299871106012cf246ed13632347a))
* **property-performance:** removes some tooltips. Appcues will handle them ([406336e](https://gitlab.pepita.io/getrix/mls-site/commit/406336e5f34c92e49a0b9a1d0863d480822600bc))
* translation language on not logged pages ([c5fd32b](https://gitlab.pepita.io/getrix/mls-site/commit/c5fd32b3c31dfad1340019d7c3c960605f7f189c))

## [5.53.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.53.0...v5.53.1) (2023-06-20)


### Bug Fixes

* **settings-headquartes:** marker size ([42088d7](https://gitlab.pepita.io/getrix/mls-site/commit/42088d765ece1aaed1a49f4bfa80cf31809d7a81))

## [5.53.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.52.1...v5.53.0) (2023-06-19)


### Features

* **customers:** add gx-navigation header ([5dc8d48](https://gitlab.pepita.io/getrix/mls-site/commit/5dc8d482bc0ceb3387d230b93f57e71ce8f17c99))
* **dashboard:** add gx-navigation header configuration ([9fc104a](https://gitlab.pepita.io/getrix/mls-site/commit/9fc104a44e0f3cc2ad357edd99d846a3b27e29ac))


### Bug Fixes

* creation of reviewapp url with prefix ([137a067](https://gitlab.pepita.io/getrix/mls-site/commit/137a067ecb7fb7c5a83108ccfa7dde297b0f7adb))
* **customers:** dropdown requests list action to new searches page ([ff42d7f](https://gitlab.pepita.io/getrix/mls-site/commit/ff42d7f70b8b642b9bd97d256732b1c5ba2cff95))
* **icon:** old close icon replace ([c93e150](https://gitlab.pepita.io/getrix/mls-site/commit/c93e1500074e106d30ee8361f82a331c33291a6e))
* **messaging:** paperclip icon not showing because of wrong name ([5c0cc43](https://gitlab.pepita.io/getrix/mls-site/commit/5c0cc4300db1b487433c4b123c457f5c605a3390))
* **messaging:** phone reply css ([f39f6c0](https://gitlab.pepita.io/getrix/mls-site/commit/f39f6c0f0f5dccd0bedcca4fb203ab7b7ee91918))
* **performance:** fix legend ([aae99d6](https://gitlab.pepita.io/getrix/mls-site/commit/aae99d6f73ffea14518dd3fada91274cafd2b657))
* **performance:** modifiche grafiche modale e dettaglio ([3c3d7e3](https://gitlab.pepita.io/getrix/mls-site/commit/3c3d7e394936c67704fad66a7306b78589d43d2d))
* **property-lists:** removes statistics column ([c91166b](https://gitlab.pepita.io/getrix/mls-site/commit/c91166bd7fd77abbbd0355f9297185b6353eb827))
* **property-performance:** general performances empty state ([68724e0](https://gitlab.pepita.io/getrix/mls-site/commit/68724e0730ee0d8ebbdf5ba98b188b94a94628d5))
* reviewapp trigger in gitlab-ci ([49765c3](https://gitlab.pepita.io/getrix/mls-site/commit/49765c3ae9623e0136404ad37e92cb24ef5511fb))
* **settings-general:** updated gx-design/icon and changed icon name ([6cce052](https://gitlab.pepita.io/getrix/mls-site/commit/6cce052fe896d2e6cf87ede175c1882cc6ecd579))

## [5.52.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.52.0...v5.52.1) (2023-05-26)


### Bug Fixes

* **auction-catalogue:** remove useless Notify component that was generating troubles ([642fd91](https://gitlab.pepita.io/getrix/mls-site/commit/642fd91358c0087034b288ade5e594aa406de096))

## [5.52.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.51.1...v5.52.0) (2023-05-25)


### Features

* added test for .env files ([a1f0590](https://gitlab.pepita.io/getrix/mls-site/commit/a1f0590529286853b454753208ed7c7c1d916cd3))
* **searches:** added filter to modal ui ([570256a](https://gitlab.pepita.io/getrix/mls-site/commit/570256a8003288077859f3689d40ef03fcd585a6))
* **token:** breakpoints ([0bf74aa](https://gitlab.pepita.io/getrix/mls-site/commit/0bf74aa8a80077c808b6fb799162cf77b479c784))


### Bug Fixes

* added missing env variable ([3946c16](https://gitlab.pepita.io/getrix/mls-site/commit/3946c16dbae4949d2b385ec4dcae0d2dce09e3e3))
* **envs:** updated .env with all keys and dropped the useless ones ([87b7ffa](https://gitlab.pepita.io/getrix/mls-site/commit/87b7ffa540fd303f57881b0cd442702e4ffc5a0b))
* GxTable sorting ([2403d23](https://gitlab.pepita.io/getrix/mls-site/commit/2403d23a78abf5c6b319c95697cec9f1a14e117d))
* **icon:** exclamation-mark ([3980855](https://gitlab.pepita.io/getrix/mls-site/commit/39808555ef91fc6d663d1b232a3c6392438615ba))
* **multisend:** add translation key ([80740c7](https://gitlab.pepita.io/getrix/mls-site/commit/80740c743e76b7441b43caf98a68acdacc02a59a))
* **performance:** icons and modal ([f475e34](https://gitlab.pepita.io/getrix/mls-site/commit/f475e34b153fa521a2d23fc4d8958e4504eb000c))
* **property-performance:** adds existance test on performance object ([42560d0](https://gitlab.pepita.io/getrix/mls-site/commit/42560d0219da57adf5ab99c703f5503d7f8e2e26))
* **property-performance:** adds ux improvements to modal and performance page ([65536fd](https://gitlab.pepita.io/getrix/mls-site/commit/65536fdefed8dea80ca2fbf05d8097cdf78dd0f4))
* **property-performance:** changed tag performance tooltip ([c92c71f](https://gitlab.pepita.io/getrix/mls-site/commit/c92c71f8f0b55fa038af7b850443ce9184d89551))
* **property-performance:** typo icon performance high ([307a353](https://gitlab.pepita.io/getrix/mls-site/commit/307a353c0bde828991638794eda16a96e5c7b167))
* **searches:** removed ids filter + added propertyId ([1588457](https://gitlab.pepita.io/getrix/mls-site/commit/1588457a1ce462eb406c0d1b747f9506bb13fea1))


### Refactor

* **acquisition-privates:** sostituisce Notify con gx-design/snackbar ([9086bfe](https://gitlab.pepita.io/getrix/mls-site/commit/9086bfe4da6c842ac4e1318e823e177da66f3e8e))
* **auctions-catalogue:** sostituisce componente Notify con gx-design/snackbar ([928782e](https://gitlab.pepita.io/getrix/mls-site/commit/928782e1b9dd3800d314e061d24e3a877f9873e0))

## [5.51.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.51.0...v5.51.1) (2023-05-15)


### Bug Fixes

* **property-performance:** fix minori e refactoring ([77bd000](https://gitlab.pepita.io/getrix/mls-site/commit/77bd000a841f8725faad20c15ed6a10d34f006d8))
* **settings-general:** refuso placeholder campo cellulare ([90da44d](https://gitlab.pepita.io/getrix/mls-site/commit/90da44d42dd025ed229def78f54c3870e1b8d881))

## [5.51.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.50.1...v5.51.0) (2023-05-15)


### Features

* **ci:** simplified env managing for gitlab ci ([837fec1](https://gitlab.pepita.io/getrix/mls-site/commit/837fec19087c6349eefa2aea89fc7c301947328c))
* **ci:** switched to internal image ([18b53af](https://gitlab.pepita.io/getrix/mls-site/commit/18b53af3730c6b39fda121984d14e1e09a5b1d7c))
* **header:** notification button ([25fc661](https://gitlab.pepita.io/getrix/mls-site/commit/25fc661f0be612e5b38eb1d245d3ceab13b5635b))
* **header:** notification button ([4d39a60](https://gitlab.pepita.io/getrix/mls-site/commit/4d39a600132109dcf8db5d9a157d5cb90c18b51f))
* **header:** notification button ([d38e1e3](https://gitlab.pepita.io/getrix/mls-site/commit/d38e1e3598556fd02c3f7652519b73b7831fe8a8))
* **header:** notification button ([fb01e4c](https://gitlab.pepita.io/getrix/mls-site/commit/fb01e4c613a8648dfea4feb1cb143fd5ede36d3d))
* **icons:** new sprite ([9442537](https://gitlab.pepita.io/getrix/mls-site/commit/9442537510e286b248220f1a6141263d7087746d))
* improved .env files ([0bc4eb3](https://gitlab.pepita.io/getrix/mls-site/commit/0bc4eb3e55cffed744540e905073059299b2dd6a))
* updated .env files to be standard compliant ([b5e4c4a](https://gitlab.pepita.io/getrix/mls-site/commit/b5e4c4abb8259f06d60cf17333cd42e6900d8485))


### Bug Fixes

* **icons:** new icons fixes ([50de512](https://gitlab.pepita.io/getrix/mls-site/commit/50de51248db1117eedcd57982670a67043c9919a))
* **performance:** detail and list ([1d2116c](https://gitlab.pepita.io/getrix/mls-site/commit/1d2116c2dceab44a61cfbdad3bb181171723a82a))
* **portal-performance:** aggiunge il numero locali alla descrizione dell immobile ([e8550a7](https://gitlab.pepita.io/getrix/mls-site/commit/e8550a77fcb843ed68a2299f82f65f5fc4669ada))
* **property-performance:** icons names. Update translations package ([a86ac6f](https://gitlab.pepita.io/getrix/mls-site/commit/a86ac6f5999a701e8bb45f5569c54aca1306204b))
* **property-performance:** lettura fascia corretta per visualizzazioni in lista ([665b942](https://gitlab.pepita.io/getrix/mls-site/commit/665b942c310c154798591ad4c2cccb4877ee6b24))
* **property-performance:** performance annuncio ([8be4cf1](https://gitlab.pepita.io/getrix/mls-site/commit/8be4cf1b40b741d5957c871d1633483e7a8856cf))
* updated .env path for deploy procedure ([dddf7f3](https://gitlab.pepita.io/getrix/mls-site/commit/dddf7f302a24fc784af57e2e0e17428565a65c95))

## [5.50.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.50.0...v5.50.1) (2023-05-08)


### Bug Fixes

* **acquisition-privates:** missing portalName to property-exist endpoint ([0f67e3e](https://gitlab.pepita.io/getrix/mls-site/commit/0f67e3eaaa834f690e0bc3368c5a259d1428c6ad))

## [5.50.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.49.1...v5.50.0) (2023-05-03)


### Features

* **appcues:** appcues integration ([ef2810f](https://gitlab.pepita.io/getrix/mls-site/commit/ef2810f552bbc39a3d7b84f48ff1f06f61d7e749))
* getrix-common upgrade ([117c902](https://gitlab.pepita.io/getrix/mls-site/commit/117c902e079c8ec51bfac5d61d12a4e3a636bccf))
* gtxApp optional init func ([275350b](https://gitlab.pepita.io/getrix/mls-site/commit/275350bdaebe8df51b85a185e6bc271c759c5b39))
* **messaging:** getrix common update - parent tab counter update on detail access ([ad7eb12](https://gitlab.pepita.io/getrix/mls-site/commit/ad7eb1298b07e9741d6137ce572833d53cb2f8b5))
* **portal-property-list:** aggiunge colonna prestazioni ed rimuove posizione e statistiche ([882b7c6](https://gitlab.pepita.io/getrix/mls-site/commit/882b7c68d084dc2add3d5bd5901c4f4744f803f3))
* **properties-list:** sky visibility ([de4c4e9](https://gitlab.pepita.io/getrix/mls-site/commit/de4c4e90ed2a47311644305c88d6a7a72d971823))


### Bug Fixes

* **acquisition-privates:** dynamic metric events name based on portalName ([cd17b04](https://gitlab.pepita.io/getrix/mls-site/commit/cd17b040ecf8eb75d6969d1354b4d33ffd747065))
* **app-shell:** beamer and appcues integration conditional ([9397776](https://gitlab.pepita.io/getrix/mls-site/commit/93977764e68bf898651a7196c646f8c4d357e946))
* **clienti:** sbianca item localstorage al mount del form per evitare note duplicate ([7c7ec16](https://gitlab.pepita.io/getrix/mls-site/commit/7c7ec16641c32a9ae3d5c5fd29444a33cfde1d43))
* corretto dominio interno gestionale trunk staging ([4a87cdf](https://gitlab.pepita.io/getrix/mls-site/commit/4a87cdfe8f4c488645f857c4eed49b8852eafcaa))
* **iv-sch:** reset errors and modalView on modal isOpen change ([4840f65](https://gitlab.pepita.io/getrix/mls-site/commit/4840f6530d8a8a16df6fc991d521344127e42832))
* **lookup:** fixed wrong builder in Lookup Data Provider for Internationa Phone Prefix ([a9c957c](https://gitlab.pepita.io/getrix/mls-site/commit/a9c957cc5f056de862843dddb23e4079b440e198))
* **messaging:** remove wrong mixpanel event ([facdcef](https://gitlab.pepita.io/getrix/mls-site/commit/facdcef487813494a645c14f15cf264058c373fa))
* modificato dominio event-server per migrazione a it3 ([657c8f1](https://gitlab.pepita.io/getrix/mls-site/commit/657c8f1b77f109f7b6a075dd94b28f3c3f51607f))
* **multisend-active-portals:** riabilita accesso portali con sso (listGlobally) ([47edb9a](https://gitlab.pepita.io/getrix/mls-site/commit/47edb9a8b04d819d7466a4cef080fb2138a6ba78))
* **multisend:** add condition for relationship code ([11639d4](https://gitlab.pepita.io/getrix/mls-site/commit/11639d45fc41f4ab49a65f26a1e56c37c60f8d9d))
* **multisend:** add logic for project visibility and fix label ([ab0722c](https://gitlab.pepita.io/getrix/mls-site/commit/ab0722ca3ce59311ec2aa3680d468c560f0b63b8))
* **multisend:** portali attivi, in caso di portale con sso blocca apertura modale se auth va a buon fine ([42c1d6a](https://gitlab.pepita.io/getrix/mls-site/commit/42c1d6aa1dd72e30bedd0bce74bd7d1e9a9dd105))
* **portal-properties:** aggiuge tag beta alla colonna prestazioni, visibilità righe su card mobile ([f3a5b65](https://gitlab.pepita.io/getrix/mls-site/commit/f3a5b6512faded9cad8ba6a8d4de52818a7c90b2))
* **portal-properties:** non mostra colonna prestazioni se env PROPERTY_LIST_PERFORMANCE è spenta ([1d27116](https://gitlab.pepita.io/getrix/mls-site/commit/1d27116b9fa9f07f5c8ae378a2d2a393ade91e9e))
* **properties-list:** sky visibility tag logic ([e084f47](https://gitlab.pepita.io/getrix/mls-site/commit/e084f47960bce9b5bbf64bcfbe6d0b0095caeb18))
* updated gitlab url protocol with basic authentication ([7115085](https://gitlab.pepita.io/getrix/mls-site/commit/71150858c77ece9c666e9065708e858eb401d37e))
* **use-media:** us emedia match hook older browser compatibility ([5f8ffaa](https://gitlab.pepita.io/getrix/mls-site/commit/5f8ffaad6cd49207e82a8493de43499106a2e6eb))

## [5.49.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.49.0...v5.49.1) (2023-04-13)


### Bug Fixes

* **multisend:** add correct logo ([5c7cee8](https://gitlab.pepita.io/getrix/mls-site/commit/5c7cee8f6493af178f303f52e31954b3d06426a0))

## [5.49.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.48.2...v5.49.0) (2023-04-13)


### Features

* add check to avoid old satis url ([71a1e08](https://gitlab.pepita.io/getrix/mls-site/commit/71a1e08a60c1c9d1ea2d879d833a9941f74a10f9))
* **messaging:** new table ([e0554d6](https://gitlab.pepita.io/getrix/mls-site/commit/e0554d672aff0be24c7f0a089722aa5e991f7bd6))
* **multisend:** refactor gestione esportazioni & esportazioni attive ([bb33708](https://gitlab.pepita.io/getrix/mls-site/commit/bb3370811b23729eccb10184550fc667d0911001))
* **signup:** city autocomplete refactoring ([1205777](https://gitlab.pepita.io/getrix/mls-site/commit/12057779789a2942cb20e7b14131166c64d690e8))
* **virtual-tour:** added create virtualtour endpoint ([6d8da7c](https://gitlab.pepita.io/getrix/mls-site/commit/6d8da7c4e253c1b30f8c9f5ea2c15512f9359239))
* **virtual-tour:** added delete image endpoint ([78530ec](https://gitlab.pepita.io/getrix/mls-site/commit/78530ec71adfa050f0836a11eae252f4a2ff60ba))
* **virtual-tour:** added delete multimedia-floor endpoint ([9f4bb3e](https://gitlab.pepita.io/getrix/mls-site/commit/9f4bb3eb5ab92293f100488c310c7621f6028981))
* **virtual-tour:** added endpoints to set and update virtualtour multimedia floor ([127e9d4](https://gitlab.pepita.io/getrix/mls-site/commit/127e9d45d160a3fa36487820330948d4b52af1ec))
* **virtual-tour:** added set image endpoint ([d6ee65e](https://gitlab.pepita.io/getrix/mls-site/commit/d6ee65e97cec166fb11645f5c6ee5358a29478ee))
* **virtual-tour:** added update image endpoint ([03d214a](https://gitlab.pepita.io/getrix/mls-site/commit/03d214a3ee3d28d21f5e7a7da9c975bc3196b633))
* wip settings media migration ([742cc43](https://gitlab.pepita.io/getrix/mls-site/commit/742cc430b11a003ffb75bc4967990d2944f5dfd7))


### Bug Fixes

* **ci:** reviewapp .env ([5464779](https://gitlab.pepita.io/getrix/mls-site/commit/546477973cec14afe19f0b0503f7c5e2a132dc53))
* **ci:** reviewapp .env ([8eeae74](https://gitlab.pepita.io/getrix/mls-site/commit/8eeae74452c634dd3957e78b11791477677603b0))
* **ci:** reviewapp wrong env ([b34e7a5](https://gitlab.pepita.io/getrix/mls-site/commit/b34e7a55c2c8df1a9ef2441ce119c085578dd1e4))
* **invoices:** added default filter Complete=1 in Invoice List ([184be39](https://gitlab.pepita.io/getrix/mls-site/commit/184be3930ce580570144630a7828ecb7ac288fad))
* **lista-annunci-immobiliare:** href modifica annunci su vista mobile ([60b2ee6](https://gitlab.pepita.io/getrix/mls-site/commit/60b2ee6054c06b243dcf851ec3b9b4a67624fcad))
* **messaging:** new table various fixes ([3a167af](https://gitlab.pepita.io/getrix/mls-site/commit/3a167af38a5c0c5d7588f28d397a73a880bea33a))
* **multisend:** chiavi traduzioni ([8881522](https://gitlab.pepita.io/getrix/mls-site/commit/88815228acc8535331391dcca885df00403b3dc1))
* **property:** restored List listing ads filters request resolver ([086feac](https://gitlab.pepita.io/getrix/mls-site/commit/086feac9b34421d2955a9fb2e313e0d8320ae09a))
* **report:** lin-j ([f09d23a](https://gitlab.pepita.io/getrix/mls-site/commit/f09d23ae949e69acb55049c927cb31542e8caa04))

## [5.48.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.48.1...v5.48.2) (2023-04-06)


### Bug Fixes

* **ci:** node assets generation ([fd42eeb](https://gitlab.pepita.io/getrix/mls-site/commit/fd42eeb2c22342073786421a00f7458c2fe3935a))
* **multisend:** accesso info availableSpaces ([64b4ac3](https://gitlab.pepita.io/getrix/mls-site/commit/64b4ac37d4b96a20b9bda3f624b62c7531e7c929))
* **multisend:** portalName placeholder nella notify di unpublish all ([56ed9f9](https://gitlab.pepita.io/getrix/mls-site/commit/56ed9f9b32e159774ca794ec89dcaabe8391d42b))

## [5.48.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.48.0...v5.48.1) (2023-04-06)


### Bug Fixes

* **messaging:** preview string ([b42c944](https://gitlab.pepita.io/getrix/mls-site/commit/b42c944bd6d2820212c75af6afd87dd0050ffc9c))
* **messaging:** wrong new messages badge position on phoneReply box presence ([660757d](https://gitlab.pepita.io/getrix/mls-site/commit/660757d25922cb1cdd5cff5b4b7cdc46aafbab5b))

## [5.48.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.47.3...v5.48.0) (2023-04-06)


### Features

* **messaging:** added reply by phone feature ([de5e765](https://gitlab.pepita.io/getrix/mls-site/commit/de5e765aa6675c4663ba81f9a44c0b42804ea28b))
* **messaging:** set fake readStatus and updateDate if thread was answered by phone ([265a352](https://gitlab.pepita.io/getrix/mls-site/commit/265a35273bbf0f6f853126f575deaedc81cc33a0))
* **messaging:** set fake readStatus if thread was answered by phone ([9dc8b5e](https://gitlab.pepita.io/getrix/mls-site/commit/9dc8b5e34afca130a1842c7640ceeb42ebd78451))
* speed up gitlab-ci ([58a85f3](https://gitlab.pepita.io/getrix/mls-site/commit/58a85f3a6f2d3407637d5694fabedb0ee5204036))
* update translations ([9007582](https://gitlab.pepita.io/getrix/mls-site/commit/9007582ed166c4591c03ae61dd8cbfdcaf1a3173))


### Bug Fixes

* **billboard:** text line height ([783666d](https://gitlab.pepita.io/getrix/mls-site/commit/783666d721b15344a41c5962379ed2c4b15d72d4))
* **ci:** wrong environment on reviewapp ([ae7085f](https://gitlab.pepita.io/getrix/mls-site/commit/ae7085ffcb766e1d9c43e2297a813f4ed4894d54))
* **messaging:** fix ordering + status update on phone reply ([c3c73f1](https://gitlab.pepita.io/getrix/mls-site/commit/c3c73f1f23f8f8b296de7e84f9240ecd4691325b))
* **messaging:** getHasRepliedByPhone + correct icon on phone reply after visitRequest ([f3439e0](https://gitlab.pepita.io/getrix/mls-site/commit/f3439e0a8b5a83a521a4977bc25255d1fecd8a81))
* **messaging:** missing readStatus list synch on detail postThreadMessageSuccess ([10ade12](https://gitlab.pepita.io/getrix/mls-site/commit/10ade129a10d950c42e3d6d70731421a737684a4))
* **messaging:** phone reply action did not hide after message sent ([1d30c50](https://gitlab.pepita.io/getrix/mls-site/commit/1d30c500de1762a9485ca38f9d8881f3da0a7186))
* **messaging:** phone reply cta weight ([d6c7830](https://gitlab.pepita.io/getrix/mls-site/commit/d6c783056be4252c4d8ee66434a0a83fa1dd2231))
* **messaging:** phone reply do not change thread updateDate ([c50855d](https://gitlab.pepita.io/getrix/mls-site/commit/c50855d8602b7785b060cd4a805f450c68e3a835))
* **messaging:** removed client side list sort ([f779abe](https://gitlab.pepita.io/getrix/mls-site/commit/f779abe6596f25591260c437495a90b586cf6fbf))
* **messaging:** scroll down after phone reply set success ([7538ae9](https://gitlab.pepita.io/getrix/mls-site/commit/7538ae9ce8f70ffa95de70d39b4eee191a926346))
* **mixpanel:** now read data from body content ([2b4244a](https://gitlab.pepita.io/getrix/mls-site/commit/2b4244ad44df24c59a7a3854cdb3b7ad40cbc634))
* **php:** cs fixer ([56578f2](https://gitlab.pepita.io/getrix/mls-site/commit/56578f2f57b0a36a1151d2c34c467e08be2f75ae))

## [5.47.3](https://gitlab.pepita.io/getrix/mls-site/compare/v5.47.2...v5.47.3) (2023-04-04)

## [5.47.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.47.1...v5.47.2) (2023-04-04)


### Bug Fixes

* **acquisition-privates:** aggiunge traduzioni proveniente dal php mancanti ([513c226](https://gitlab.pepita.io/getrix/mls-site/commit/513c2263512e5abd738ea038c9290f457e76ed0a))

## [5.47.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.47.0...v5.47.1) (2023-04-03)

## [5.47.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.46.0...v5.47.0) (2023-03-31)


### Features

* onboarding webpack 5 ([143cad2](https://gitlab.pepita.io/getrix/mls-site/commit/143cad2e1305421a6e19005e9a34bba790e89db4))
* **onboarding:** shared webpack config ([4b84fa8](https://gitlab.pepita.io/getrix/mls-site/commit/4b84fa89d3be2183d1ab8efe9416a6a562924367))


### Bug Fixes

* **ci:** rebase modifications ([1b5fa67](https://gitlab.pepita.io/getrix/mls-site/commit/1b5fa67ba3d177cfb2c9a040b8b488b66632a1eb))
* **onboarding:** license removal webpack plugin deprecated api update ([f8d7907](https://gitlab.pepita.io/getrix/mls-site/commit/f8d7907589879afad70cfc86d6408f32abcd658a))

## [5.46.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.45.0...v5.46.0) (2023-03-31)


### Features

* **ci:** used pepita release ([21fbc17](https://gitlab.pepita.io/getrix/mls-site/commit/21fbc171623d9d73d9a42d3f9df55514ba35941d))


### Bug Fixes

* **ci:** migrate to dind approach in CI ([eaa0c81](https://gitlab.pepita.io/getrix/mls-site/commit/eaa0c81bab48f09c7b27d950440bd3af8e991c62))
* **ci:** revert --depth 1 parameter on git clone ([e1ae475](https://gitlab.pepita.io/getrix/mls-site/commit/e1ae475e14eebc8798249c99045db93393507fe9))
* **ci:** specified job dependencies for tagging ([5e0dd4c](https://gitlab.pepita.io/getrix/mls-site/commit/5e0dd4cbdd169c8b7c8c8b3cab95178f6550bfeb))
* **ci:** wrong time creation of gitlab_variables file ([04198d8](https://gitlab.pepita.io/getrix/mls-site/commit/04198d8f4983e9b76425d0ded8cf007f7a7cf0b2))
* pepita release mattermost message ([56d9038](https://gitlab.pepita.io/getrix/mls-site/commit/56d903814a4d123c26fa94244e559bd9b3e4b927))
* pepita release mattermost message ([d9c500b](https://gitlab.pepita.io/getrix/mls-site/commit/d9c500bcf105a7d8ae59424648ecc6dfa2a632b5))
* wrong artifact download url ([fb46869](https://gitlab.pepita.io/getrix/mls-site/commit/fb4686966235153e952a419139473ade3dfee99c))
* wrong use of single quote ([583ed7d](https://gitlab.pepita.io/getrix/mls-site/commit/583ed7d55fdabd570e005c2bf4f40a1877b8541e))

## [5.40.3](https://gitlab.pepita.io/getrix/mls-site/compare/v5.40.2...v5.40.3) (2023-02-16)


### Bug Fixes

* trusted inbox and parametric-stats endpoint ([1a05add](https://gitlab.pepita.io/getrix/mls-site/commit/1a05addd792313c82c3b14e13159427d332a8de2))



# [5.34.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.33.7...v5.34.0) (2022-12-02)


### Bug Fixes

* **microbundle:** microbundle dependency ([9c8602d](https://gitlab.pepita.io/getrix/mls-site/commit/9c8602d8805d9b88e99b6f4dfd8dff6a3d52370e))
* **onboarding:** onboarding bundler ([c98d613](https://gitlab.pepita.io/getrix/mls-site/commit/c98d613188a8ca21ed5d87fdcf5a0181490bfbd6))
* **reportistica:** link promuovi aste ([4b35321](https://gitlab.pepita.io/getrix/mls-site/commit/4b353217b543da96cc6f058a92629e9fe0f68163))
* **reportistica:** link promuovi aste ([531edc4](https://gitlab.pepita.io/getrix/mls-site/commit/531edc4085aab1c4f5a3d0a12f79dae18496946f))


### Features

* added .phprc to define environment ([c9d4740](https://gitlab.pepita.io/getrix/mls-site/commit/c9d474085109fbb01d0feaba59f4242449162e5d))
* **event:** Event tracker refactoring. ([a0eec07](https://gitlab.pepita.io/getrix/mls-site/commit/a0eec07aaa573e1eb9e8d0d12cda47f3e51127a3))
* **onboarding:** onboarding app ([f44f337](https://gitlab.pepita.io/getrix/mls-site/commit/f44f337c6b279edd207e93c839edc2a6871be48b))



## [5.33.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.33.0...v5.33.1) (2022-11-17)


### Bug Fixes

* **reportistica:** aggiunge controllo esistenza gruppo_BO ([25eb120](https://gitlab.pepita.io/getrix/mls-site/commit/25eb120af384ef3ac9d2f041f74ceb475ec4288d))
* **reportistica:** codice annuncio errore bulk, prezzo su richiesta, link lista dashboard, format superficie, data creazione -> modifica ([416690f](https://gitlab.pepita.io/getrix/mls-site/commit/416690fb2a566a1dce7c225b3bce7ce8a22f0ea4))
* **reportistica:** match campo corretto per blocco integrazioni aste ([7e70a7b](https://gitlab.pepita.io/getrix/mls-site/commit/7e70a7bb7d94a5c3a91e3342843097ee3cabbea3))
* **reportistica:** table head ([9a95bdb](https://gitlab.pepita.io/getrix/mls-site/commit/9a95bdbbea0f97611a46bfa6bee79806a66ca23d))



# [5.33.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.32.1...v5.33.0) (2022-11-16)


### Bug Fixes

* auctions spaces ([91f950b](https://gitlab.pepita.io/getrix/mls-site/commit/91f950ba495ee1c50a082d4fda1109c3eaf049c1))
* datamapper macrozone controllo esistenza proprietà city ([d0c2aa4](https://gitlab.pepita.io/getrix/mls-site/commit/d0c2aa4f102d5a12324c931df2372a2c0ae7848e))
* missing use class ([a5dc20a](https://gitlab.pepita.io/getrix/mls-site/commit/a5dc20a76dc317e37a9a2b5938c220f055823017))
* performance profiler wrapper in dataproviders ([b0a8821](https://gitlab.pepita.io/getrix/mls-site/commit/b0a8821c388c992bc61b898d9dc5d7de4880f05b))
* php fixer ([c88f150](https://gitlab.pepita.io/getrix/mls-site/commit/c88f1505f67f65223399b3b7da8aa744c736e2b9))
* portal properties spaces count ([789e349](https://gitlab.pepita.io/getrix/mls-site/commit/789e349f9ac97a64e90b5cf9e32d1200e12bfabf))
* **reportistica:** aggiorna pagination-bar, fix dettaglio senza tipologiaV2 ([16abde9](https://gitlab.pepita.io/getrix/mls-site/commit/16abde9400f7f7175f5be1014c00b2b4f34c81fb))
* **reportistica:** aggiornamento counter premium, stato header actions, fix e miglioramenti vari ([161d0ae](https://gitlab.pepita.io/getrix/mls-site/commit/161d0ae4c2c07856846e38969bf27820513f3518))
* **reportistica:** aggiornamento gx-design alert e gestione dismiss alert ([411763d](https://gitlab.pepita.io/getrix/mls-site/commit/411763d1a3fbd257fbf7e48f9d8ab8cda91dadf6))
* **reportistica:** aggiunge gestione modulo nuove costruzioni e landing page, piccoli fix ([c59a5de](https://gitlab.pepita.io/getrix/mls-site/commit/c59a5dec489d7adcc3534dbfffd161db3c4f967c))
* **reportistica:** aggiunge gx-design/PaginationBar, reset ActionBar al cambio tab ([178fe38](https://gitlab.pepita.io/getrix/mls-site/commit/178fe386f9dd1c50cdebfa4fce214d10dcffa080))
* **reportistica:** fix vari ([d9318e5](https://gitlab.pepita.io/getrix/mls-site/commit/d9318e5b1ddb517761e15ca144a0cd5b7ba3e5cf))
* **reportistica:** gli spazi premium delle aste non devono essere esclusivi per le aste ([d729e05](https://gitlab.pepita.io/getrix/mls-site/commit/d729e057b8f6860d5e7a984184e6daf266dc958b))
* **reportistica:** informazioni modale ranking duplicate ([be6f27a](https://gitlab.pepita.io/getrix/mls-site/commit/be6f27a8e4dc44952a8a67495653c7d88035e014))
* **reportistica:** label errore liste, path anteprima annuncio ([2d0fb41](https://gitlab.pepita.io/getrix/mls-site/commit/2d0fb414f74e4060caf20492644df3c370deda8b))
* **reportistica:** nome endpoint stampa ([a551b8b](https://gitlab.pepita.io/getrix/mls-site/commit/a551b8bf798d32f70d4ed08325f1a7a2e948b2a4))
* **reportistica:** ottimizzazione endpoint, loader spento ad errore api qualità ([70864e3](https://gitlab.pepita.io/getrix/mls-site/commit/70864e33c640198956a1d7665e1c13b7769cbd16))
* **reportistica:** patch conteggi premium, rotta cartellone, refuso stampa dettaglio immobile ([36f2a78](https://gitlab.pepita.io/getrix/mls-site/commit/36f2a78bf220235857849c0ce2027bc1528c8808))
* **reportistica:** patch modale qualità da backoffice ([e2fc899](https://gitlab.pepita.io/getrix/mls-site/commit/e2fc89915906487423789fa9353c9b73c870f561))
* **reportistica:** refuso modale qualità ([260ff7f](https://gitlab.pepita.io/getrix/mls-site/commit/260ff7f06ac40b2897f7a900880c592c37cedb7b))
* **reportistica:** spunta verde qualità da lista, ricalcolo spazi premium dopo operazioni ([0320d77](https://gitlab.pepita.io/getrix/mls-site/commit/0320d773ac19cd817001ea6378807239786ce0dc))
* **reportistica:** table nuove costruzioni ([d5ebf59](https://gitlab.pepita.io/getrix/mls-site/commit/d5ebf59dac0da2b993ec55c7a41cbaf78b8bbda5))
* rimuove console.log e debugger ([06a0110](https://gitlab.pepita.io/getrix/mls-site/commit/06a0110cd068fabae4b289b5894a4535f2330dc8))
* rimuove console.log e debugger ([94b22aa](https://gitlab.pepita.io/getrix/mls-site/commit/94b22aaeeb607e5513d08d340456af3734c825cc))
* **tos:** css ([62df7da](https://gitlab.pepita.io/getrix/mls-site/commit/62df7da996d6cb3f9385cdae8a7ae18d4d6c880f))


### Features

* added redux toolkit dep ([dd4b199](https://gitlab.pepita.io/getrix/mls-site/commit/dd4b1990f7ea808b9f0f3c6a97a1fb40d676c5df))
* **gtx-react:** added NotifyProvider component ([ecc7ae0](https://gitlab.pepita.io/getrix/mls-site/commit/ecc7ae0338d0b642414c03734d4087131a64048f))
* **gtx-react:** added parseIntWithPoints util ([07432cf](https://gitlab.pepita.io/getrix/mls-site/commit/07432cfc884d4e28e9ef6a35e40ba943d6d39328))
* **gtx-react:** added useIsInViewPort hook ([24ba3ea](https://gitlab.pepita.io/getrix/mls-site/commit/24ba3ea71c6633d1f27e570ac5b02335b00c03ba))
* **gtx-react:** added useLookUpData hook ([c528d5f](https://gitlab.pepita.io/getrix/mls-site/commit/c528d5f0b25b10e0f21dc4e7015e1955e6185eef))
* **gtx-react:** apiCallOnVisible provider and common component ([f627b64](https://gitlab.pepita.io/getrix/mls-site/commit/f627b64320938bb6d0fa9bb257230ed02d6f822f))
* **gtx-react:** GtxApp + RTK components ([02fba4c](https://gitlab.pepita.io/getrix/mls-site/commit/02fba4c684af6c6b63df297241114507195100c1))
* **reportistica:** lista immobili su immobiliare.it ([2c3155b](https://gitlab.pepita.io/getrix/mls-site/commit/2c3155b7569f4c89a830506e93cdfd188fe063bb))



# [5.31.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.30.3...v5.31.0) (2022-09-22)


### Bug Fixes

* fixed immobiliare base url ([a8247ac](https://gitlab.pepita.io/getrix/mls-site/commit/a8247acc0b4f155e511ffc4b4f987013342fd8d2))
* **sentry:** cambiata dsn sentry sy documentazione ([92239e0](https://gitlab.pepita.io/getrix/mls-site/commit/92239e0285f785aa56ba96f7ac6c0526904d8e23))


### Features

* **property:** added ad sharing token endpoints ([0ba233f](https://gitlab.pepita.io/getrix/mls-site/commit/0ba233ff5a456957a4ade1dba3d0ae6df1d2d10c))
* refactoring some lookup properties ([6be8a02](https://gitlab.pepita.io/getrix/mls-site/commit/6be8a02c9d3cffa4186f92c045cee2da8e14c344))
* **settings-general:** added pdf privacy file download ([a95e7b6](https://gitlab.pepita.io/getrix/mls-site/commit/a95e7b6cdb2fb40c3f5bc7ad86798ed16ced0c45))



## [5.30.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.30.1...v5.30.2) (2022-09-16)


### Bug Fixes

* **messaging:** bulk bar getBoundingRect on undefined ([07a753c](https://gitlab.pepita.io/getrix/mls-site/commit/07a753cd6ea1744872a4a3df25ee48b7d1b6d76a))
* **messaging:** switch from GxBadge to gx-design/badge on list rows ([451660f](https://gitlab.pepita.io/getrix/mls-site/commit/451660f5027c782a12ffcb1d810d777f88f53790))
* **messaging:** unwanted window close call ([8c2f6fa](https://gitlab.pepita.io/getrix/mls-site/commit/8c2f6fa0b91aa8ab9043a348ac32064c883be4d9))
* **security:** responde code fix ([38f43eb](https://gitlab.pepita.io/getrix/mls-site/commit/38f43ebdefa144078ef0942b2ae19a1124de8dc0))
* **security:** responde code fix ([bb08022](https://gitlab.pepita.io/getrix/mls-site/commit/bb0802270f547e96495bfc67fb2fee85c9238b5d))
* **security:** securty table ui fix ([d21ae8e](https://gitlab.pepita.io/getrix/mls-site/commit/d21ae8eb4702cff13dec671dc4f6531931a5399b))



## [5.28.3](https://gitlab.pepita.io/getrix/mls-site/compare/v5.28.2...v5.28.3) (2022-07-27)



## [5.28.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.28.1...v5.28.2) (2022-07-27)



## [5.28.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.28.0...v5.28.1) (2022-07-27)


### Bug Fixes

* **messaging:** se l immobile è un progetto viene mostrata la label corrispondente ([cc2d8a6](https://gitlab.pepita.io/getrix/mls-site/commit/cc2d8a6a03d8dc70b6e4dd25491be6a6ca13845c))



# [5.28.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.27.4...v5.28.0) (2022-07-27)


### Bug Fixes

* **messaging:** aggiunge eventuale riferimento immobile nel footer del messaggio ([540bcc7](https://gitlab.pepita.io/getrix/mls-site/commit/540bcc7a6492d04679f659a8a5b9378633a9cd60))
* wrong sentry dsn ([e028816](https://gitlab.pepita.io/getrix/mls-site/commit/e028816a1af4ff1025cad2ae6136e29998855e96))


### Features

* hidden projects from immovisita ads list ([5b2e913](https://gitlab.pepita.io/getrix/mls-site/commit/5b2e9134b7d590908cc3a6e233c333b33964ad79))
* js pepita sentry dns ([3b329b6](https://gitlab.pepita.io/getrix/mls-site/commit/3b329b6f9fe124bc2312abd0ee9838091f77ed3f))
* **new-constructions:** modifiche per nuova gestione nuove costruzioni ([974e5a1](https://gitlab.pepita.io/getrix/mls-site/commit/974e5a1ef3c90a27b53b290df4d0d2db963a17e4))



## [5.27.4](https://gitlab.pepita.io/getrix/mls-site/compare/v5.27.3...v5.27.4) (2022-07-19)


### Bug Fixes

* **immovisita:** modal ul ([a770b55](https://gitlab.pepita.io/getrix/mls-site/commit/a770b556c05ca5c5141f72aea12814c3a9e83526))



## [5.27.3](https://gitlab.pepita.io/getrix/mls-site/compare/v5.27.2...v5.27.3) (2022-07-18)


### Bug Fixes

* **agency-estimate:** risolve problema replace url al caricamento del primo step in inserimento ([9532c94](https://gitlab.pepita.io/getrix/mls-site/commit/9532c94adbd8903be25f9215101fd75a11582720))
* **agency-estimates:** rimuove doppia richiesta a /users sul primo step ([dc3b5ae](https://gitlab.pepita.io/getrix/mls-site/commit/dc3b5ae8288253cc4a4ea288448dd4195bcdc33f))
* **xhr:** sostituisce utilizzi libreria @immobiliare-labs/fetch in favore di @pepita/http ([5d5fc18](https://gitlab.pepita.io/getrix/mls-site/commit/5d5fc186f565b23595bd0e41aab7793ba6ed1716))
* **youdomus:** iframe catasto ([e937bb6](https://gitlab.pepita.io/getrix/mls-site/commit/e937bb61f785b7dc0a13b102aac5ae20134ab7d3))



## [5.27.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.27.1...v5.27.2) (2022-07-15)


### Bug Fixes

* **checkbox:** checkbox event removed ([878d98e](https://gitlab.pepita.io/getrix/mls-site/commit/878d98edadd5b5732bf5965bdfcd8c6ad5f21ba2))



# [5.26.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.25.0...v5.26.0) (2022-06-30)


### Bug Fixes

* **aste:** gallery catalogo aste ([a6973e2](https://gitlab.pepita.io/getrix/mls-site/commit/a6973e203e0d465bab618e10e9f90612d2a1dd28))
* **catalogo-aste:** risolve notifca errore su importazione ([32ef2ef](https://gitlab.pepita.io/getrix/mls-site/commit/32ef2ef9a6b4798f4bfb6a664a4788d2f6758487))
* **pager:** pager component changes ([366bbc5](https://gitlab.pepita.io/getrix/mls-site/commit/366bbc521aa5f9de7be7dbe3f5f2b0350316cbd6))
* **settings:** agenzie di zona: modifica testi e aggiunge modale alla sezione immagini e video > immagine ([edee89a](https://gitlab.pepita.io/getrix/mls-site/commit/edee89a9d20152fc84ae286fa43b47fa0cd2f09c))



# [5.25.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.24.1...v5.25.0) (2022-06-28)


### Bug Fixes

* rename pacchetti getrix-common getrix-bundles in @getrix/common|bundles ([8750b2d](https://gitlab.pepita.io/getrix/mls-site/commit/8750b2d2089f2d26451d97ec42597c4fbec99b90))



# [5.23.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.22.2...v5.23.0) (2022-06-13)


### Bug Fixes

* **acquisizione-privati:** evita trigger richiesta city-zone al caricamento della pagina ([6f2697a](https://gitlab.pepita.io/getrix/mls-site/commit/6f2697a522b423c497e214f0ac38f74dd08271c3))
* **tabs:** tabs modified span ([ea896fd](https://gitlab.pepita.io/getrix/mls-site/commit/ea896fd7a4e5e43aab67a456748cc65aae01eecc))
* **youdomus:** nascosto ask help ([1508f2b](https://gitlab.pepita.io/getrix/mls-site/commit/1508f2bec0695454e8c9fd1ad8619fe7ac01824a))


### Features

* **youdomus:** aggiunge sezione Catasto su mappa ([c0f3fd1](https://gitlab.pepita.io/getrix/mls-site/commit/c0f3fd1081fa9fbce09d03f1cbe5da786da8af97))



## [5.22.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.22.0...v5.22.1) (2022-05-27)


### Bug Fixes

* **messaging:** scroll bug ([7bcaf70](https://gitlab.pepita.io/getrix/mls-site/commit/7bcaf7023821688812e330b466f85150219991fd))
* **ui:** gx-tag + border ([b8f526f](https://gitlab.pepita.io/getrix/mls-site/commit/b8f526f5dfd6b67f4b94d0740c259fa0213b3a47))



# [5.22.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.21.2...v5.22.0) (2022-05-27)


### Bug Fixes

* corregge gestione risposta PUT setVisited su messaggistica. Alcuni fix su catalogo aste ([58d6479](https://gitlab.pepita.io/getrix/mls-site/commit/58d6479bcc6743a40214e753b1acf8ab8f619a7c))
* **immovisita_settings:** modal image ([016204b](https://gitlab.pepita.io/getrix/mls-site/commit/016204b353219827c003176a5a4e5fd8b3f26135))
* **lists:** card list ([791c10e](https://gitlab.pepita.io/getrix/mls-site/commit/791c10e172ed0ef3f9ec0f597b1b12b63c677607))
* **login:** login title spacing ([62d7bb0](https://gitlab.pepita.io/getrix/mls-site/commit/62d7bb0623403a9194651a3dc23ad0a114aee303))
* **misc:** multisend sorting fix + other ([bdb5a5f](https://gitlab.pepita.io/getrix/mls-site/commit/bdb5a5fbd61aecca11ffad22238def3aa7ef4d4c))
* **mls:** fix vari ([ff6151e](https://gitlab.pepita.io/getrix/mls-site/commit/ff6151e20a9738132cb58cc54a727023c4f5ae10))
* **mls:** removed unused css ([4cc14b7](https://gitlab.pepita.io/getrix/mls-site/commit/4cc14b752f4c0226e963c7e292b7075806110657))
* **multisend-settings:** multisend settings customvalidate ([6ff8823](https://gitlab.pepita.io/getrix/mls-site/commit/6ff8823b878cb9cb862d7533b95248ecacc9cbce))
* **report:** report add notes margin ([af340fe](https://gitlab.pepita.io/getrix/mls-site/commit/af340fe2ce2b7e66cd380c98d1f2db1e69ca4c4a))
* **schedule-modal:** schedule modal button type ([80f143f](https://gitlab.pepita.io/getrix/mls-site/commit/80f143f3dfa76dd22105a2b2d6de731bbbe3869c))
* **trans:** add title translation ([b5e4baa](https://gitlab.pepita.io/getrix/mls-site/commit/b5e4baa2ddc7dcdb88684fbc0bd6afa0777d449d))
* **typo:** add dot at the end of the sentence ([fa800b6](https://gitlab.pepita.io/getrix/mls-site/commit/fa800b6d04b57312b1f735b54877b333f57e14d8))


### Features

* removed filter for getting invoices without an attachment. ([469ede8](https://gitlab.pepita.io/getrix/mls-site/commit/469ede85e8c87cf184807505371f375a52f58d6b))



## [5.20.6](https://gitlab.pepita.io/getrix/mls-site/compare/v5.20.5...v5.20.6) (2022-05-05)


### Bug Fixes

* **customer:** edit modal geography select behaviour ([23f6ecc](https://gitlab.pepita.io/getrix/mls-site/commit/23f6ecc8f96e0c87581cf41566a4e16e4c65bfa2))
* **sales-req:** truncate long mail + desired price bold ([e24b426](https://gitlab.pepita.io/getrix/mls-site/commit/e24b426e80746ca0867c506f10dae923ccad34e2))


### Features

* **sales-req:** added garage bool to details page ([786d461](https://gitlab.pepita.io/getrix/mls-site/commit/786d461737e6b728abd6df73659264d7a7f9ecef))
* **sales-req:** added info vendita column ([0353e0b](https://gitlab.pepita.io/getrix/mls-site/commit/0353e0b77b602ec92c964466890ee91539b9e17b))
* updated translations ([ed303de](https://gitlab.pepita.io/getrix/mls-site/commit/ed303dee4cb1791d4f5ae4c55612218740cf28e8))



<a name="5.19.6"></a>
## [5.19.6](https://gitlab.pepita.io/getrix/mls-site/compare/v5.19.5...v5.19.6) (2022-03-28)



<a name="5.19.4"></a>
## [5.19.4](https://gitlab.pepita.io/getrix/mls-site/compare/v5.19.3...v5.19.4) (2022-03-21)


### Bug Fixes

* **messaging:** removed filters console log ([4ecf278](https://gitlab.pepita.io/getrix/mls-site/commit/4ecf278))
* aggiunta traduzione per stringa nella mail post registrazione ([ba4205a](https://gitlab.pepita.io/getrix/mls-site/commit/ba4205a))



<a name="5.19.2"></a>
## [5.19.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.19.1...v5.19.2) (2022-03-02)


### Bug Fixes

* **messaging:** fixed a bug where unread messages in mobile were not displayed ([c519b74](https://gitlab.pepita.io/getrix/mls-site/commit/c519b74))
* **messaging:** revisione testi modale info alert. Corretto recupero tipologia immobile ([3a5f823](https://gitlab.pepita.io/getrix/mls-site/commit/3a5f823))
* **react-components:** modifica keyDown handler del componente RangeInput di formik ([3ca4eeb](https://gitlab.pepita.io/getrix/mls-site/commit/3ca4eeb))
* **scheduled-visits:** handling null fields toString ([20e9fc5](https://gitlab.pepita.io/getrix/mls-site/commit/20e9fc5))


### Features

* **messaging:** aggiunge info alert e relativa modale sul listing ([28d7dcc](https://gitlab.pepita.io/getrix/mls-site/commit/28d7dcc))



<a name="5.18.1"></a>
## [5.18.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.18.0...v5.18.1) (2022-02-15)


### Features

* modificati puntamenti per utilizzo nuove macchine trunk ([0c11b9c](https://gitlab.pepita.io/getrix/mls-site/commit/0c11b9c))
* scroll top message ([d03405f](https://gitlab.pepita.io/getrix/mls-site/commit/d03405f))



<a name="5.17.0"></a>
# [5.17.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.16.1...v5.17.0) (2022-01-26)


### Bug Fixes

* gestione richieste xhr con sessione mancante ([fc3a2ab](https://gitlab.pepita.io/getrix/mls-site/commit/fc3a2ab))


### Features

* **youdomus:** modale benvenuto, logica versione estensione ([2b392fa](https://gitlab.pepita.io/getrix/mls-site/commit/2b392fa))



<a name="5.15.2"></a>
## [5.15.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.15.1...v5.15.2) (2022-01-21)



<a name="5.15.1"></a>
## [5.15.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.15.0...v5.15.1) (2022-01-14)


### Bug Fixes

* **agency-estimates:** check esistenza contatti ([e3bc0f8](https://gitlab.pepita.io/getrix/mls-site/commit/e3bc0f8))
* **login-sso:** switched sso product http method ([94dd25a](https://gitlab.pepita.io/getrix/mls-site/commit/94dd25a))



<a name="5.15.0"></a>
# [5.15.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.14.0...v5.15.0) (2022-01-12)


### Bug Fixes

* **deps:** fork della dipendenza react-slot-fill ([200f568](https://gitlab.pepita.io/getrix/mls-site/commit/200f568))
* **sales:** import pacchetto react-slot-fill ([db7abcc](https://gitlab.pepita.io/getrix/mls-site/commit/db7abcc))



<a name="5.14.0"></a>
# [5.14.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.13.0...v5.14.0) (2022-01-11)


### Bug Fixes

* **messaging:** aggiunge proprietà agli eventi send_message e show_thread_details ([ff03eb8](https://gitlab.pepita.io/getrix/mls-site/commit/ff03eb8))
* **sales-request:** no result empty header counter label ([e449850](https://gitlab.pepita.io/getrix/mls-site/commit/e449850))



<a name="5.12.5"></a>
## [5.12.5](https://gitlab.pepita.io/getrix/mls-site/compare/v5.12.4...v5.12.5) (2021-12-14)


### Bug Fixes

* **image:** email messaging invite template image dimensions ([28ea3a6](https://gitlab.pepita.io/getrix/mls-site/commit/28ea3a6))
* rimosso json parse ([5cc6447](https://gitlab.pepita.io/getrix/mls-site/commit/5cc6447))


### Features

* aggiornato gtx common ([3bb780d](https://gitlab.pepita.io/getrix/mls-site/commit/3bb780d))
* gestito parametro Title ([ba3902a](https://gitlab.pepita.io/getrix/mls-site/commit/ba3902a))



<a name="5.12.4"></a>
## [5.12.4](https://gitlab.pepita.io/getrix/mls-site/compare/v5.12.3...v5.12.4) (2021-12-09)


### Bug Fixes

* **constant:** constant name ([8ffac9b](https://gitlab.pepita.io/getrix/mls-site/commit/8ffac9b))
* **landing-page:** rifattorizza e rimuove return non necessari ([37896c2](https://gitlab.pepita.io/getrix/mls-site/commit/37896c2))
* **signing:** url costant ([82d4ae4](https://gitlab.pepita.io/getrix/mls-site/commit/82d4ae4))



<a name="5.12.3"></a>
## [5.12.3](https://gitlab.pepita.io/getrix/mls-site/compare/v5.12.2...v5.12.3) (2021-12-09)


### Features

* **signin:** link style ([d9c56f5](https://gitlab.pepita.io/getrix/mls-site/commit/d9c56f5))
* **signin:** new string with url ([65989c6](https://gitlab.pepita.io/getrix/mls-site/commit/65989c6))



<a name="5.12.2"></a>
## [5.12.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.12.1...v5.12.2) (2021-12-09)


### Bug Fixes

* **messagging:** endpoint di prod del ws di gestione code ([af32327](https://gitlab.pepita.io/getrix/mls-site/commit/af32327))
* **messaging:** aggiunta costante per email della chat ([ec5441a](https://gitlab.pepita.io/getrix/mls-site/commit/ec5441a))
* **messaging:** traduzione oggetto email invito ([1699377](https://gitlab.pepita.io/getrix/mls-site/commit/1699377))
* **typo:** prop typo ([e6676ef](https://gitlab.pepita.io/getrix/mls-site/commit/e6676ef))


### Features

* **landing-modal:** landing and modal changes ([797a6c4](https://gitlab.pepita.io/getrix/mls-site/commit/797a6c4))
* **messaggistica:** gestione invio email di invito e landing page ([bb0458b](https://gitlab.pepita.io/getrix/mls-site/commit/bb0458b))
* **messaging:** factory/strategy email invito agenzie ([7a1faae](https://gitlab.pepita.io/getrix/mls-site/commit/7a1faae))



<a name="5.12.1"></a>
## [5.12.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.12.0...v5.12.1) (2021-11-29)


### Bug Fixes

* **agency-estimate:** replace nella url thumb immagine agente ([d5ee6ac](https://gitlab.pepita.io/getrix/mls-site/commit/d5ee6ac))
* **agent:** fixed delete agent ([31a09af](https://gitlab.pepita.io/getrix/mls-site/commit/31a09af))
* **ci:** rimosso limite per utilizzo singolo runner ([8ccab2e](https://gitlab.pepita.io/getrix/mls-site/commit/8ccab2e))
* **menu-scrollbar:** menu scrollbar hide ([8804a1e](https://gitlab.pepita.io/getrix/mls-site/commit/8804a1e))
* **messaggistica:** eventi mixpanel ([aee7149](https://gitlab.pepita.io/getrix/mls-site/commit/aee7149))
* **messaggistica:** gestione caso textHtml arriva vuoto, scroll al tag nuovo messaggio ([951891f](https://gitlab.pepita.io/getrix/mls-site/commit/951891f))
* **messaggistica:** modifica comportamento spunte. Risolve alcuni piccoli fix ([49e5521](https://gitlab.pepita.io/getrix/mls-site/commit/49e5521))
* **messaggistica:** modifica il nome del modulo messaggistica ([26bf94e](https://gitlab.pepita.io/getrix/mls-site/commit/26bf94e))
* **messaggistica:** nome estensione ([c2761e9](https://gitlab.pepita.io/getrix/mls-site/commit/c2761e9))
* **messaggistica:** ricarica la lista quando si torna indietro dal dettaglio thread ([c4c11c2](https://gitlab.pepita.io/getrix/mls-site/commit/c4c11c2))
* **scrollbar:** removed scrollbar ([957d42b](https://gitlab.pepita.io/getrix/mls-site/commit/957d42b))
* **sidebar-messaging:** scroll on sidebar ([5467640](https://gitlab.pepita.io/getrix/mls-site/commit/5467640))


### Features

* **messaggistica:** supporto richieste per nuove costruzioni ([d28dd03](https://gitlab.pepita.io/getrix/mls-site/commit/d28dd03))



<a name="5.12.0"></a>
# [5.12.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.11.0...v5.12.0) (2021-11-23)


### Bug Fixes

* domain cookie ([80eed83](https://gitlab.pepita.io/getrix/mls-site/commit/80eed83))
* **messaggistica:** al resize della finestra chiude la spalla del dettaglio ([f8fcc51](https://gitlab.pepita.io/getrix/mls-site/commit/f8fcc51))
* youdomus ([346809c](https://gitlab.pepita.io/getrix/mls-site/commit/346809c))
* **filter-clear:** remove filter clear ([f7b6643](https://gitlab.pepita.io/getrix/mls-site/commit/f7b6643))
* **messaggistica:** anche in caso di ricerca per codice viene mostrato il rimuovi filtri ([6549b2e](https://gitlab.pepita.io/getrix/mls-site/commit/6549b2e))
* **messaggistica:** corregge endpoint staging event server ([2c4b53f](https://gitlab.pepita.io/getrix/mls-site/commit/2c4b53f))
* **messaggistica:** decora le proprietà dei messaggi inviati o ricevuti stabilendo se è il primo o l ultimo ([71f8f7c](https://gitlab.pepita.io/getrix/mls-site/commit/71f8f7c))
* **messaggistica:** gestione conversazione letta quando utente è attivamente sulla pagina ([6b5320f](https://gitlab.pepita.io/getrix/mls-site/commit/6b5320f))
* **messaggistica:** layout fixes ([81be2ef](https://gitlab.pepita.io/getrix/mls-site/commit/81be2ef))
* **messaggistica:** ottimizzazione scroll al caricamento delle immagini ([1bce6b3](https://gitlab.pepita.io/getrix/mls-site/commit/1bce6b3))
* **messaging:** layout fixes ([9d62468](https://gitlab.pepita.io/getrix/mls-site/commit/9d62468))
* **messaging:** message border ([ff54507](https://gitlab.pepita.io/getrix/mls-site/commit/ff54507))
* **messaging:** message border ([5ebb005](https://gitlab.pepita.io/getrix/mls-site/commit/5ebb005))
* **messaging:** ui fixes ([3b0d145](https://gitlab.pepita.io/getrix/mls-site/commit/3b0d145))
* **multisend:** fixed contract filter ([4eb94a5](https://gitlab.pepita.io/getrix/mls-site/commit/4eb94a5))
* **ui:** header details + filter button ([ebaf0c0](https://gitlab.pepita.io/getrix/mls-site/commit/ebaf0c0))


### Features

* **message:** borders ([3db1bda](https://gitlab.pepita.io/getrix/mls-site/commit/3db1bda))
* **messaggistica:** flusso tracciamento eventi tramite sdk mixpanel ([8eb5970](https://gitlab.pepita.io/getrix/mls-site/commit/8eb5970))
* **messaging:** placeholder-no-border ([5388c34](https://gitlab.pepita.io/getrix/mls-site/commit/5388c34))



<a name="5.10.2"></a>
## [5.10.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.10.1...v5.10.2) (2021-11-12)


### Bug Fixes

* messaggio searchable badge ([36a0ee0](https://gitlab.pepita.io/getrix/mls-site/commit/36a0ee0))
* url immobiliare gestionale base url ([ba2b3d6](https://gitlab.pepita.io/getrix/mls-site/commit/ba2b3d6))



<a name="5.10.1"></a>
## [5.10.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.10.0...v5.10.1) (2021-11-11)


### Bug Fixes

* **report-validation:** fix a bug where phone number was required even if not selected ([6d0366b](https://gitlab.pepita.io/getrix/mls-site/commit/6d0366b))



<a name="5.10.0"></a>
# [5.10.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.9.2...v5.10.0) (2021-10-28)


### Bug Fixes

* **agency-estimates:** authorId cleanup if previous one does not exist anymore ([a3bb31b](https://gitlab.pepita.io/getrix/mls-site/commit/a3bb31b))
* **agency-estimates:** typo ([508f78e](https://gitlab.pepita.io/getrix/mls-site/commit/508f78e))
* **aste-tooltip:** add tooltip to auction action ([5dc6f31](https://gitlab.pepita.io/getrix/mls-site/commit/5dc6f31))
* **auction-tooltip-text:** tooltip string changes ([be8e619](https://gitlab.pepita.io/getrix/mls-site/commit/be8e619))


### Features

* **catalogo-aste:** auction update management ([6aac5d7](https://gitlab.pepita.io/getrix/mls-site/commit/6aac5d7))



<a name="5.9.0"></a>
# [5.9.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.8.1...v5.9.0) (2021-10-21)



<a name="5.8.1"></a>
## [5.8.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.8.0...v5.8.1) (2021-10-12)



<a name="5.8.0"></a>
# [5.8.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.7.2...v5.8.0) (2021-10-07)


### Features

* **youdomus:** integrazione nuova app ([1b85646](https://gitlab.pepita.io/getrix/mls-site/commit/1b85646))



<a name="5.7.2"></a>
## [5.7.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.7.1...v5.7.2) (2021-10-04)


### Bug Fixes

* **dashboaoard:** aggiunge check value elemento news-popup ([7b28750](https://gitlab.pepita.io/getrix/mls-site/commit/7b28750))
* **gtm:** sostituisce tag script gtm con inizializzazione tramite modulo gtm di getrix-common ([a21a50b](https://gitlab.pepita.io/getrix/mls-site/commit/a21a50b))



<a name="5.7.1"></a>
## [5.7.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.7.0...v5.7.1) (2021-09-29)



<a name="5.7.0"></a>
# [5.7.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.6.0...v5.7.0) (2021-09-27)


### Bug Fixes

* aggiorna build e alcune dipendenze per compatibilita con react 17 ([189a5e4](https://gitlab.pepita.io/getrix/mls-site/commit/189a5e4))
* aggiunta configurazione mancante per la spagna ([7aa6ae1](https://gitlab.pepita.io/getrix/mls-site/commit/7aa6ae1))
* correzioni husky ([ceeeaad](https://gitlab.pepita.io/getrix/mls-site/commit/ceeeaad))
* upgrade react alla 17.0.2 ([b37e955](https://gitlab.pepita.io/getrix/mls-site/commit/b37e955))



<a name="5.6.0"></a>
# [5.6.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.5.0...v5.6.0) (2021-09-21)


### Bug Fixes

* **acquisizione:** risolve problema reset filtri superficie e locali ([019b137](https://gitlab.pepita.io/getrix/mls-site/commit/019b137))
* **photoplan-vt360:** aggiunge versionamento assets per aggirare la cache della cdn ([af861ab](https://gitlab.pepita.io/getrix/mls-site/commit/af861ab))
* rimossa build per branch non più usata ([7a27407](https://gitlab.pepita.io/getrix/mls-site/commit/7a27407))


### Features

* **service-acquisizione:** moded SDK calls to API ([a79f160](https://gitlab.pepita.io/getrix/mls-site/commit/a79f160))



<a name="5.5.0"></a>
# [5.5.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.4.0...v5.5.0) (2021-09-13)


### Bug Fixes

* per prevenire attacchi xss viene aggiunto, dove manca, dataType al metodo Ajax di jquery ([a2165e7](https://gitlab.pepita.io/getrix/mls-site/commit/a2165e7))
* sostituisce jquery 1.10.2 con un fork della stessa versione con patch di sicurezza ([6754a2e](https://gitlab.pepita.io/getrix/mls-site/commit/6754a2e))
* sostituisce occorrenze $.extend(true, ..., ...) con spread operator ([cced4d9](https://gitlab.pepita.io/getrix/mls-site/commit/cced4d9))
* **components:** aggiunge e spegne attributi autocomplete e autcorrect all input del componente autocomplete formik ([afde706](https://gitlab.pepita.io/getrix/mls-site/commit/afde706))



<a name="5.4.0"></a>
# [5.4.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.3.0...v5.4.0) (2021-09-09)



<a name="5.2.4"></a>
## [5.2.4](https://gitlab.pepita.io/getrix/mls-site/compare/v5.2.3...v5.2.4) (2021-08-25)


### Bug Fixes

* **agency-estimates/properties lookup:** checked if requested lookup exists in api ([443dad6](https://gitlab.pepita.io/getrix/mls-site/commit/443dad6))


### Features

* icona immovisita common ([1bf548d](https://gitlab.pepita.io/getrix/mls-site/commit/1bf548d))



<a name="5.2.3"></a>
## [5.2.3](https://gitlab.pepita.io/getrix/mls-site/compare/v5.2.2...v5.2.3) (2021-08-04)


### Bug Fixes

* **settings-media:** solves agency delete video problem ([c7c695f](https://gitlab.pepita.io/getrix/mls-site/commit/c7c695f))
* aggiornate traduzioni ([cf2f75d](https://gitlab.pepita.io/getrix/mls-site/commit/cf2f75d))
* ripristinato composer update senza no-cache ([91583b4](https://gitlab.pepita.io/getrix/mls-site/commit/91583b4))
* test composer install no cache ([71d5acb](https://gitlab.pepita.io/getrix/mls-site/commit/71d5acb))



<a name="5.2.2"></a>
## [5.2.2](https://gitlab.pepita.io/getrix/mls-site/compare/v5.2.1...v5.2.2) (2021-07-30)


### Bug Fixes

* **dashboard:** corregge errore su loop su  oggetti array-like ([af3a97f](https://gitlab.pepita.io/getrix/mls-site/commit/af3a97f))
* aggiornate traduzioni ([c230d58](https://gitlab.pepita.io/getrix/mls-site/commit/c230d58))
* readme ([386935f](https://gitlab.pepita.io/getrix/mls-site/commit/386935f))


### Features

* abilitato stato ricerca immobile ([c5240bb](https://gitlab.pepita.io/getrix/mls-site/commit/c5240bb))



<a name="5.2.1"></a>
## [5.2.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.2.0...v5.2.1) (2021-07-21)


### Bug Fixes

* **dashboard:** aggiunge check ulteriore su correttezza dati listing ([ed76b5b](https://gitlab.pepita.io/getrix/mls-site/commit/ed76b5b))
* **security:** solves possible prototype pollution issue from querystring ([032e398](https://gitlab.pepita.io/getrix/mls-site/commit/032e398))
* aggiorna common e traduzioni per visualizzare il tag novita nel menu laterale ([75d163e](https://gitlab.pepita.io/getrix/mls-site/commit/75d163e))


### Features

* cms e common ([a2bc11e](https://gitlab.pepita.io/getrix/mls-site/commit/a2bc11e))



<a name="5.2.0"></a>
# [5.2.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.1.1...v5.2.0) (2021-07-15)


### Bug Fixes

* aggiornate traduzioni ([f4a22c0](https://gitlab.pepita.io/getrix/mls-site/commit/f4a22c0))


### Features

* lista immovisita ([93b7b22](https://gitlab.pepita.io/getrix/mls-site/commit/93b7b22))



<a name="5.1.1"></a>
## [5.1.1](https://gitlab.pepita.io/getrix/mls-site/compare/v5.1.0...v5.1.1) (2021-07-13)


### Bug Fixes

* corretto dominio mediaserver sviluppo ([aed7d3c](https://gitlab.pepita.io/getrix/mls-site/commit/aed7d3c))
* fix url gestionale nuovo e controllo news ([6aa34ca](https://gitlab.pepita.io/getrix/mls-site/commit/6aa34ca))
* **report:** valida il numero di telefono dell agente solo quando viene mostrato l input ([73ddf75](https://gitlab.pepita.io/getrix/mls-site/commit/73ddf75))



<a name="5.1.0"></a>
# [5.1.0](https://gitlab.pepita.io/getrix/mls-site/compare/v5.0.0...v5.1.0) (2021-07-08)


### Bug Fixes

* **ansible-deploy:** corrette variabili per pro.indomio.es) ([91f1a43](https://gitlab.pepita.io/getrix/mls-site/commit/91f1a43))
* aggiorna database browser di caniuse-lite ([65ccbdd](https://gitlab.pepita.io/getrix/mls-site/commit/65ccbdd))
* **dati fatturazione:** tooltip ([31567e3](https://gitlab.pepita.io/getrix/mls-site/commit/31567e3))
* aggiornate traduzioni ([c9fab98](https://gitlab.pepita.io/getrix/mls-site/commit/c9fab98))
* aggiornate traduzioni ([db01695](https://gitlab.pepita.io/getrix/mls-site/commit/db01695))
* dependencies cleanup ([36663f2](https://gitlab.pepita.io/getrix/mls-site/commit/36663f2))
* prepush checks ([2857be2](https://gitlab.pepita.io/getrix/mls-site/commit/2857be2))
* risolve errore pacchetto nuggets/carousel nei pre-push checks ([0564fa6](https://gitlab.pepita.io/getrix/mls-site/commit/0564fa6))
* validazione contatti in modifica cliente, se, stile errore autocomplete ([ea8f568](https://gitlab.pepita.io/getrix/mls-site/commit/ea8f568))


### Features

* **react-components:** migration to formik ([37e29a3](https://gitlab.pepita.io/getrix/mls-site/commit/37e29a3))



<a name="5.0.0"></a>
# [5.0.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.57.2...v5.0.0) (2021-06-21)


### Bug Fixes

* app_id indomio ([3736806](https://gitlab.pepita.io/getrix/mls-site/commit/3736806))
* **acquisizione:** ripristina il link dei riscontri ([cea91ec](https://gitlab.pepita.io/getrix/mls-site/commit/cea91ec))
* **administrative:** add _this to function keydown ([d2d537e](https://gitlab.pepita.io/getrix/mls-site/commit/d2d537e))
* **ansible-deploy:** evitato controllo certificato in fase di notifica operazione ([3a47ee5](https://gitlab.pepita.io/getrix/mls-site/commit/3a47ee5))
* **aste:** aggiunge attributo enableReinitialize al wrapper di formik per consentire la reinizializzazione dei filtri alla rimozione ([25292c3](https://gitlab.pepita.io/getrix/mls-site/commit/25292c3))
* **aste:** modale tos catalogo senza icona chiusura. Aggiunge prop closeButton al componente Modal per consentire di nascondere lil b bottone di chiusura ([bfbae89](https://gitlab.pepita.io/getrix/mls-site/commit/bfbae89))
* **common:** linked latest common changes ([de42d3c](https://gitlab.pepita.io/getrix/mls-site/commit/de42d3c))
* **dashboard:** corretto riferimento a this ([4c2e774](https://gitlab.pepita.io/getrix/mls-site/commit/4c2e774))
* corretti parametri ([5c5755d](https://gitlab.pepita.io/getrix/mls-site/commit/5c5755d))
* **dashboard:** indomiopro, al load del grafico riporta correttamente in tabella i dati premium ([c5dac43](https://gitlab.pepita.io/getrix/mls-site/commit/c5dac43))
* **dashboard:** label mancanti per la spagna ([810bb19](https://gitlab.pepita.io/getrix/mls-site/commit/810bb19))
* **dashboard:** ripristina filtri statistiche annunci su indomio ([904dd59](https://gitlab.pepita.io/getrix/mls-site/commit/904dd59))
* **deploy:** commentato controllo su versione momentaneamente ([e23e931](https://gitlab.pepita.io/getrix/mls-site/commit/e23e931))
* **deploy:** corrette alcune configurazioni deploy ([671ea85](https://gitlab.pepita.io/getrix/mls-site/commit/671ea85))
* **deploy:** corretto il valore di 2 variabili ([9b8ee49](https://gitlab.pepita.io/getrix/mls-site/commit/9b8ee49))
* **immo-gtx:** acquisizione privati - nasconde funzionalita importa annuncio per vista non getrix plus ([5872720](https://gitlab.pepita.io/getrix/mls-site/commit/5872720))
* **immo-gtx:** adds some existence checks on dashboard components ([02eb4a9](https://gitlab.pepita.io/getrix/mls-site/commit/02eb4a9))
* **immo-gtx:** allinea la label del box sito web con quella dell oggetto della modale di richiesta ([b0b9c26](https://gitlab.pepita.io/getrix/mls-site/commit/b0b9c26))
* **immo-gtx:** corregge testo della pagina di set password ([c8b0be4](https://gitlab.pepita.io/getrix/mls-site/commit/c8b0be4))
* **immo-gtx:** introdotto check estensione Clienti su report. Introduce check estensione lista annunci su Acquisizione privati. Uniforma colonna azioni listing acquisizione privati. Minor fix dashboard ([daddbd2](https://gitlab.pepita.io/getrix/mls-site/commit/daddbd2))
* **immo-gtx:** menu multinvio esportazioni attive label ([0730087](https://gitlab.pepita.io/getrix/mls-site/commit/0730087))
* **immo-gtx:** nasconde data scadenza servizi attivi tranne che per telefono smart e sito web ([ce686a4](https://gitlab.pepita.io/getrix/mls-site/commit/ce686a4))
* **immo-gtx:** rename delle feature delle liste immobiliare ([52a5a8a](https://gitlab.pepita.io/getrix/mls-site/commit/52a5a8a))
* **immo-gtx:** report - nasconde campo cliente su vista non getrix plus ([1602c5d](https://gitlab.pepita.io/getrix/mls-site/commit/1602c5d))
* acl update agency owner data ([31fe142](https://gitlab.pepita.io/getrix/mls-site/commit/31fe142))
* addDomain console ([88e1d10](https://gitlab.pepita.io/getrix/mls-site/commit/88e1d10))
* added feature toggle requests_new ([90327cb](https://gitlab.pepita.io/getrix/mls-site/commit/90327cb))
* aggiornate traduzioni ([f911b2d](https://gitlab.pepita.io/getrix/mls-site/commit/f911b2d))
* aggiornate traduzioni ([b1742bb](https://gitlab.pepita.io/getrix/mls-site/commit/b1742bb))
* aggiornato getrix-common ([c3a7a7e](https://gitlab.pepita.io/getrix/mls-site/commit/c3a7a7e))
* aggiornato getrix-common ([b5c4c4c](https://gitlab.pepita.io/getrix/mls-site/commit/b5c4c4c))
* aggiornato getrix-common ([50ab166](https://gitlab.pepita.io/getrix/mls-site/commit/50ab166))
* aggiornato getrix-common e getrix-bundles ([e5e7727](https://gitlab.pepita.io/getrix/mls-site/commit/e5e7727))
* aggiunge chiave gtx per gestionale es ([0f8473e](https://gitlab.pepita.io/getrix/mls-site/commit/0f8473e))
* aggiunti nomi servizi estensioni ([d5c2e96](https://gitlab.pepita.io/getrix/mls-site/commit/d5c2e96))
* aggiunto controllo feature zone_settings in dashboard ([c6e4757](https://gitlab.pepita.io/getrix/mls-site/commit/c6e4757))
* aggiunto controllo su integrità entità agenzia prima di redirect nuovo gestionale ([e19f358](https://gitlab.pepita.io/getrix/mls-site/commit/e19f358))
* aggiunto toggle per redirect a nuovo gestionale ([e2a315d](https://gitlab.pepita.io/getrix/mls-site/commit/e2a315d))
* api key indomioes deploy staging indomioes ([efb5607](https://gitlab.pepita.io/getrix/mls-site/commit/efb5607))
* appDomain base template twig ([d76cf4a](https://gitlab.pepita.io/getrix/mls-site/commit/d76cf4a))
* appDomain body class ([a6eaf76](https://gitlab.pepita.io/getrix/mls-site/commit/a6eaf76))
* check vox before set notifications ([4774871](https://gitlab.pepita.io/getrix/mls-site/commit/4774871))
* consistencies lookup ([ef74422](https://gitlab.pepita.io/getrix/mls-site/commit/ef74422))
* consistencies lookup report ([585d430](https://gitlab.pepita.io/getrix/mls-site/commit/585d430))
* cookie popup ([f32eda7](https://gitlab.pepita.io/getrix/mls-site/commit/f32eda7))
* corretta annotation nel model UserSearch per proprieta comune ([f72ab25](https://gitlab.pepita.io/getrix/mls-site/commit/f72ab25))
* corretta url api geocoder staging ([554795f](https://gitlab.pepita.io/getrix/mls-site/commit/554795f))
* corrette configurazioni mappe e geocoder ([be9c8f1](https://gitlab.pepita.io/getrix/mls-site/commit/be9c8f1))
* corretti parametri staging e prod ([e9748e8](https://gitlab.pepita.io/getrix/mls-site/commit/e9748e8))
* corretto check per modulo getrix attivo ([8ed7c50](https://gitlab.pepita.io/getrix/mls-site/commit/8ed7c50))
* corretto conteggio contatti dashboard ([adf5cff](https://gitlab.pepita.io/getrix/mls-site/commit/adf5cff))
* corretto path metriche ([9bcc521](https://gitlab.pepita.io/getrix/mls-site/commit/9bcc521))
* corretto path rpc_services e domini interni ([c0ae488](https://gitlab.pepita.io/getrix/mls-site/commit/c0ae488))
* corretto refuso ([228b9e3](https://gitlab.pepita.io/getrix/mls-site/commit/228b9e3))
* corretto sistema di rilascio ([c38729a](https://gitlab.pepita.io/getrix/mls-site/commit/c38729a))
* corretto stato attivazione tos ([e81f677](https://gitlab.pepita.io/getrix/mls-site/commit/e81f677))
* customers lookup response ([7166715](https://gitlab.pepita.io/getrix/mls-site/commit/7166715))
* empty value for geography lookup request ([569b337](https://gitlab.pepita.io/getrix/mls-site/commit/569b337))
* esplicitato il valore di showProvince nelle chiamate al suggest di immobiliare ([8300d8a](https://gitlab.pepita.io/getrix/mls-site/commit/8300d8a))
* feature toggle es ([ca0b254](https://gitlab.pepita.io/getrix/mls-site/commit/ca0b254))
* feature toggle indomioes ([eefcd78](https://gitlab.pepita.io/getrix/mls-site/commit/eefcd78))
* globals twig app is gtx ([c24a297](https://gitlab.pepita.io/getrix/mls-site/commit/c24a297))
* lookup countries report ([84993a1](https://gitlab.pepita.io/getrix/mls-site/commit/84993a1))
* modificata url per il recupero delle lookup da trunk ([b35212a](https://gitlab.pepita.io/getrix/mls-site/commit/b35212a))
* multisend projects projectFormatter missing ([41a086a](https://gitlab.pepita.io/getrix/mls-site/commit/41a086a))
* pass cookie to cookie manager ([3615299](https://gitlab.pepita.io/getrix/mls-site/commit/3615299))
* re-inserito il valore di default per le province dele associazioni nei settings ([b7b800d](https://gitlab.pepita.io/getrix/mls-site/commit/b7b800d))
* recovery password ([6a2d44f](https://gitlab.pepita.io/getrix/mls-site/commit/6a2d44f))
* reintegrate modifiche a lookup report dopo rebase ([6266dd8](https://gitlab.pepita.io/getrix/mls-site/commit/6266dd8))
* rimosso AppDomain ([20d9a6e](https://gitlab.pepita.io/getrix/mls-site/commit/20d9a6e))
* rimosso metodo duplicato AcquisitionHandler ([0145543](https://gitlab.pepita.io/getrix/mls-site/commit/0145543))
* rinominati servizi per permettere una maggiore comprensione ([c01d356](https://gitlab.pepita.io/getrix/mls-site/commit/c01d356))
* rpc login service host ([82a78e5](https://gitlab.pepita.io/getrix/mls-site/commit/82a78e5))
* signup page ([15d9da7](https://gitlab.pepita.io/getrix/mls-site/commit/15d9da7))
* sostituita immagine placeholder ([f12feef](https://gitlab.pepita.io/getrix/mls-site/commit/f12feef))
* strip tags dashboard news ([3d03d51](https://gitlab.pepita.io/getrix/mls-site/commit/3d03d51))
* typo ([e8f15c2](https://gitlab.pepita.io/getrix/mls-site/commit/e8f15c2))
* **immo-gtx:** revisione email ([3caf965](https://gitlab.pepita.io/getrix/mls-site/commit/3caf965))
* **immo-gtx:** rimuove bottone per sso a immobiliare sulle pagine di multinvio. Aggiunge nel feature toggle le sezioni delle liste "su immobiliare" ([01f5653](https://gitlab.pepita.io/getrix/mls-site/commit/01f5653))
* **immo-gtx:** rimuove le configurazioni di google analytics ([b5d99d2](https://gitlab.pepita.io/getrix/mls-site/commit/b5d99d2))
* **immo-gtx:** risolve check sul tipo dello stato estensione ([c92c388](https://gitlab.pepita.io/getrix/mls-site/commit/c92c388))
* **immo-gtx:** se estensione nuove costruzioni e spenta spegne modale richiesta attivazione servizio e mostra landing page ([b15bf8f](https://gitlab.pepita.io/getrix/mls-site/commit/b15bf8f))
* **immo-gtx:** unifica template riga utente gtxgtx e pro ([c56669a](https://gitlab.pepita.io/getrix/mls-site/commit/c56669a))
* **immo-gtx:** visualizza favicon e nome applicazione nei title specifici del contratto (immobiliarePro/Getrix) ([3d478ca](https://gitlab.pepita.io/getrix/mls-site/commit/3d478ca))
* **mutuo:** removed parsley parameters ([8b74326](https://gitlab.pepita.io/getrix/mls-site/commit/8b74326))
* **mutuo:** removed validation on keyup ([d97bcca](https://gitlab.pepita.io/getrix/mls-site/commit/d97bcca))
* **news:** corretta etichetta news gestionale ([b02bbed](https://gitlab.pepita.io/getrix/mls-site/commit/b02bbed))
* **profile:** input clearable with right class ([e5e2916](https://gitlab.pepita.io/getrix/mls-site/commit/e5e2916))
* **profile:** removed keyup trigger ([0a81bb8](https://gitlab.pepita.io/getrix/mls-site/commit/0a81bb8))
* **report:** corretta landing page ([183ff5d](https://gitlab.pepita.io/getrix/mls-site/commit/183ff5d))
* **report:** corretto controllo stato modulo ([bd23446](https://gitlab.pepita.io/getrix/mls-site/commit/bd23446))
* **tos:** removed parsley destroy to prevent console error ([68c7ae9](https://gitlab.pepita.io/getrix/mls-site/commit/68c7ae9))
* user ansible deploy indomio ([b62f98d](https://gitlab.pepita.io/getrix/mls-site/commit/b62f98d))
* **user-profile:** removed parsley on keyup from edit-user-profile ([00aa944](https://gitlab.pepita.io/getrix/mls-site/commit/00aa944))
* **youdomus:** aggiunta gestione richiesta servizio ([2a08e78](https://gitlab.pepita.io/getrix/mls-site/commit/2a08e78))
* vox extenxion from api ([a61dd09](https://gitlab.pepita.io/getrix/mls-site/commit/a61dd09))


### Features

* **aste:** aggiunge discaimer condizioni di servizio del catalogo ([516f893](https://gitlab.pepita.io/getrix/mls-site/commit/516f893))
* **aste:** catalouge section ([d4a7756](https://gitlab.pepita.io/getrix/mls-site/commit/d4a7756))
* **customers:** aggiunta landing page ([1a97fe9](https://gitlab.pepita.io/getrix/mls-site/commit/1a97fe9))
* **immo-gtx:** fix grafici contratto ([875d7be](https://gitlab.pepita.io/getrix/mls-site/commit/875d7be))
* **immo-gtx:** force password ([4b975e9](https://gitlab.pepita.io/getrix/mls-site/commit/4b975e9))
* **immo-gtx:** integrazione google tag manager e rimozione utilizzi google analytics ([e725c8a](https://gitlab.pepita.io/getrix/mls-site/commit/e725c8a))
* **immo-gtx:** landing page customers ([9766ffa](https://gitlab.pepita.io/getrix/mls-site/commit/9766ffa))
* **immo-gtx:** login ([07e8370](https://gitlab.pepita.io/getrix/mls-site/commit/07e8370))
* **immo-gtx:** menu ([b588854](https://gitlab.pepita.io/getrix/mls-site/commit/b588854))
* **immo-gtx:** revisione dashboard ([59f9d21](https://gitlab.pepita.io/getrix/mls-site/commit/59f9d21))
* **immo-gtx:** table chart ([538b608](https://gitlab.pepita.io/getrix/mls-site/commit/538b608))
* **immo+gtx:** contract ([13f274d](https://gitlab.pepita.io/getrix/mls-site/commit/13f274d))
* aggiunto ranking dashboard ([951b345](https://gitlab.pepita.io/getrix/mls-site/commit/951b345))
* composer e yarn ([4e09bb7](https://gitlab.pepita.io/getrix/mls-site/commit/4e09bb7))
* form richiesta consulenza mutuo ([d0fb2f0](https://gitlab.pepita.io/getrix/mls-site/commit/d0fb2f0))
* getrix common ([cec3bbe](https://gitlab.pepita.io/getrix/mls-site/commit/cec3bbe))
* integrazione/revisione news ([7fd666b](https://gitlab.pepita.io/getrix/mls-site/commit/7fd666b))
* label form mutuo ([2ff7a7d](https://gitlab.pepita.io/getrix/mls-site/commit/2ff7a7d))
* modale news ([58f8718](https://gitlab.pepita.io/getrix/mls-site/commit/58f8718))



<a name="4.57.2"></a>
## [4.57.2](https://gitlab.pepita.io/getrix/mls-site/compare/v4.58.0...v4.57.2) (2021-04-13)



<a name="4.56.6"></a>
## [4.56.6](https://gitlab.pepita.io/getrix/mls-site/compare/v4.56.5...v4.56.6) (2021-03-26)


### Bug Fixes

* **acquisition-privates:** city max-width ([123494f](https://gitlab.pepita.io/getrix/mls-site/commit/123494f))
* **acquisition-privates-mediaquery:** fixed mediaquery to aggregate table properties ([174e801](https://gitlab.pepita.io/getrix/mls-site/commit/174e801))



<a name="4.56.5"></a>
## [4.56.5](https://gitlab.pepita.io/getrix/mls-site/compare/v4.56.4...v4.56.5) (2021-03-25)


### Bug Fixes

* aggiornata versione virtual tour e fotoplan ([fb5699d](https://gitlab.pepita.io/getrix/mls-site/commit/fb5699d))


### Features

* controllo modulo attivo prima del controllo sul limite ([99af0ed](https://gitlab.pepita.io/getrix/mls-site/commit/99af0ed))



<a name="4.56.2"></a>
## [4.56.2](https://gitlab.pepita.io/getrix/mls-site/compare/v4.56.1...v4.56.2) (2021-03-17)


### Bug Fixes

* aggiornate traduzioni ([b789740](https://gitlab.pepita.io/getrix/mls-site/commit/b789740))
* enabled new multisend and increased api timeout ([1003db8](https://gitlab.pepita.io/getrix/mls-site/commit/1003db8))
* **impostazioni:** corretto nome società di riferimento ([1d1fcce](https://gitlab.pepita.io/getrix/mls-site/commit/1d1fcce))



<a name="4.56.1"></a>
## [4.56.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.56.0...v4.56.1) (2021-03-09)


### Bug Fixes

* sbloccata rotta per il recupero dei contatti ([ce2253c](https://gitlab.pepita.io/getrix/mls-site/commit/ce2253c))
* **acquisizione-privati:** aggiunto check url annuncio su card ([a8d0b7f](https://gitlab.pepita.io/getrix/mls-site/commit/a8d0b7f))
* **field-actiongroup:** fixed actiongroup bug ([4eaf2eb](https://gitlab.pepita.io/getrix/mls-site/commit/4eaf2eb))



<a name="4.56.0"></a>
# [4.56.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.55.3...v4.56.0) (2021-03-01)


### Features

* spento propertyfinder ([684df0a](https://gitlab.pepita.io/getrix/mls-site/commit/684df0a))



<a name="4.55.0"></a>
# [4.55.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.54.1...v4.55.0) (2021-02-01)


### Bug Fixes

* aggiunta costante servizio multinvio ([ccdc899](https://gitlab.pepita.io/getrix/mls-site/commit/ccdc899))
* **404:** aggiornata la grafica ([ce4fc49](https://gitlab.pepita.io/getrix/mls-site/commit/ce4fc49))
* **agent:** setAgentMobilePhoneNumber: deleted check for null agent profile and updated check for empty contacts ([98bfc5e](https://gitlab.pepita.io/getrix/mls-site/commit/98bfc5e))


### Features

* aggiunta possibilità di spegnere sezione ([e08c0da](https://gitlab.pepita.io/getrix/mls-site/commit/e08c0da))



<a name="4.53.0"></a>
# [4.53.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.52.2...v4.53.0) (2021-01-22)


### Bug Fixes

* **agent:** fixed error message for email not available ([0b0f6f7](https://gitlab.pepita.io/getrix/mls-site/commit/0b0f6f7))
* aggiunto controllo su oggetto ([dc7b737](https://gitlab.pepita.io/getrix/mls-site/commit/dc7b737))
* aggiunto messaggio generico su limite report ([b5bcecc](https://gitlab.pepita.io/getrix/mls-site/commit/b5bcecc))
* **fotoplan:** handles escape key press when a modal is open, viewpoint sizes, viewer images carousel on mobile devices, images optimization, viewer animation when plan is full screen opened ([ab6e820](https://gitlab.pepita.io/getrix/mls-site/commit/ab6e820))



<a name="4.50.1"></a>
## [4.50.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.50.0...v4.50.1) (2020-12-21)


### Bug Fixes

* ripristinato servizio vt360 ([9e4f920](https://gitlab.pepita.io/getrix/mls-site/commit/9e4f920))
* **photoplan:** ie11, safari 10 fotoplan compatibility ([0c88f7d](https://gitlab.pepita.io/getrix/mls-site/commit/0c88f7d))
* **virtual-tour:** restores upload file limit to 10mb. Upgrades photoplan package ([71a9ccd](https://gitlab.pepita.io/getrix/mls-site/commit/71a9ccd))



<a name="4.50.0"></a>
# [4.50.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.49.2...v4.50.0) (2020-12-18)


### Bug Fixes

* **photoplan:** photoplan/virtual tour packages upgrade ([17a9856](https://gitlab.pepita.io/getrix/mls-site/commit/17a9856))
* **photoplan:** tronca i nomi dei file delle planimetrie caricate a 50 caratteri ([9e2cc01](https://gitlab.pepita.io/getrix/mls-site/commit/9e2cc01))
* exclude agent contact if contact status is deleted ([1ac3dd8](https://gitlab.pepita.io/getrix/mls-site/commit/1ac3dd8))
* photoplan package upgrade ([e65bd2e](https://gitlab.pepita.io/getrix/mls-site/commit/e65bd2e))
* send mail agent activation token ([08a91da](https://gitlab.pepita.io/getrix/mls-site/commit/08a91da))
* set association id agent ([43b9446](https://gitlab.pepita.io/getrix/mls-site/commit/43b9446))
* typo ([7e70456](https://gitlab.pepita.io/getrix/mls-site/commit/7e70456))



<a name="4.49.0"></a>
# [4.49.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.48.3...v4.49.0) (2020-12-15)


### Bug Fixes

* **fotoplan:** viewer ([4417ba3](https://gitlab.pepita.io/getrix/mls-site/commit/4417ba3))
* **photoplan:** aggiornamento testi indomio ([a6762c2](https://gitlab.pepita.io/getrix/mls-site/commit/a6762c2))
* **photoplan:** attiva hotjar su fotoplan e lo disattiva da virtual-tour ([39c5746](https://gitlab.pepita.io/getrix/mls-site/commit/39c5746))
* **photoplan:** avanzamento versione fotoplan. Separazione css tra fotoplan e virtual-tour ([d53ffbf](https://gitlab.pepita.io/getrix/mls-site/commit/d53ffbf))
* **photoplan:** avanzamento versione pacchetto ([8815241](https://gitlab.pepita.io/getrix/mls-site/commit/8815241))
* **photoplan:** avanzamento versione photoplan per correzione issue ([ff0af54](https://gitlab.pepita.io/getrix/mls-site/commit/ff0af54))
* **photoplan:** avanzamento versione photoplan: risolve problema link dopo edit immagine planimetria ([c2e7654](https://gitlab.pepita.io/getrix/mls-site/commit/c2e7654))


### Features

* **photoplan:** api rest per modulo photoplan ([5ddebfe](https://gitlab.pepita.io/getrix/mls-site/commit/5ddebfe))



<a name="4.48.2"></a>
## [4.48.2](https://gitlab.pepita.io/getrix/mls-site/compare/v4.48.1...v4.48.2) (2020-11-26)


### Bug Fixes

* aggiornate traduzioni ([c05c1e8](https://gitlab.pepita.io/getrix/mls-site/commit/c05c1e8))
* **a2f:** corretta interpretazione stringa ([9f82938](https://gitlab.pepita.io/getrix/mls-site/commit/9f82938))


### Features

* aggiornato readme template ([fb46e2e](https://gitlab.pepita.io/getrix/mls-site/commit/fb46e2e))



<a name="4.48.1"></a>
## [4.48.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.48.0...v4.48.1) (2020-11-19)


### Bug Fixes

* **a2f:** commentato codice per rotta non più valida ([6e90690](https://gitlab.pepita.io/getrix/mls-site/commit/6e90690))
* **a2f:** irrobustiti controlli per a2f ([95d3928](https://gitlab.pepita.io/getrix/mls-site/commit/95d3928))


### Features

* **menu:** aggiunta voce per sottosezione venduti/affittati ([1b89387](https://gitlab.pepita.io/getrix/mls-site/commit/1b89387))



<a name="4.46.3"></a>
## [4.46.3](https://gitlab.pepita.io/getrix/mls-site/compare/v4.46.2...v4.46.3) (2020-10-19)


### Bug Fixes

* correzioni post refactoring ([b43cc01](https://gitlab.pepita.io/getrix/mls-site/commit/b43cc01))



<a name="4.46.2"></a>
## [4.46.2](https://gitlab.pepita.io/getrix/mls-site/compare/v4.46.1...v4.46.2) (2020-10-15)


### Bug Fixes

* **a2f:** corretta traduzione label ([51b6609](https://gitlab.pepita.io/getrix/mls-site/commit/51b6609))



<a name="4.46.1"></a>
## [4.46.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.46.0...v4.46.1) (2020-10-15)


### Bug Fixes

* **a2f:** gestito salvataggio primo numero telefonico ([1c71bbf](https://gitlab.pepita.io/getrix/mls-site/commit/1c71bbf))



<a name="4.46.0"></a>
# [4.46.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.45.3...v4.46.0) (2020-10-15)


### Bug Fixes

* corretta configurazione servizio lookup staging indomioPro ([db44cd3](https://gitlab.pepita.io/getrix/mls-site/commit/db44cd3))
* **a2f:** corretta durata token ([1635b5b](https://gitlab.pepita.io/getrix/mls-site/commit/1635b5b))


### Features

* **a2f:** nuovo numero per a2f inserito passando per la mail dell'agente ([2ddfb42](https://gitlab.pepita.io/getrix/mls-site/commit/2ddfb42))



<a name="4.45.3"></a>
## [4.45.3](https://gitlab.pepita.io/getrix/mls-site/compare/v4.45.2...v4.45.3) (2020-09-24)


### Bug Fixes

* aggiunta label per stato fattura emessa ([cc14f08](https://gitlab.pepita.io/getrix/mls-site/commit/cc14f08))
* corretta revisione numeri telefonici per aggiunta prefisso internazionale ([151cb4c](https://gitlab.pepita.io/getrix/mls-site/commit/151cb4c))



<a name="4.44.5"></a>
## [4.44.5](https://gitlab.pepita.io/getrix/mls-site/compare/v4.44.4...v4.44.5) (2020-09-15)


### Bug Fixes

* **customers:** evita refetch dati cache di react-query. Rimuove utilizzo di api finally ([3fdb53a](https://gitlab.pepita.io/getrix/mls-site/commit/3fdb53a))


### Features

* aggiunge metrica conteggio chiamate ad html2pdf per cartelloni ([e9fb7ea](https://gitlab.pepita.io/getrix/mls-site/commit/e9fb7ea))



<a name="4.44.4"></a>
## [4.44.4](https://gitlab.pepita.io/getrix/mls-site/compare/v4.44.3...v4.44.4) (2020-09-14)


### Bug Fixes

* **customers:** recupera id note preesistenti per evitare che durante gli update tutte le note vengano considerate come nuove ([660273b](https://gitlab.pepita.io/getrix/mls-site/commit/660273b))



<a name="4.44.3"></a>
## [4.44.3](https://gitlab.pepita.io/getrix/mls-site/compare/v4.44.2...v4.44.3) (2020-09-09)


### Bug Fixes

* **customers:** aggiunta fonte contatto (Riviste) mancante ([912a63f](https://gitlab.pepita.io/getrix/mls-site/commit/912a63f))



<a name="4.44.2"></a>
## [4.44.2](https://gitlab.pepita.io/getrix/mls-site/compare/v4.44.1...v4.44.2) (2020-09-09)


### Bug Fixes

* risolve highlight pagina corrente acquisizione valutazioni, visualizzazione mappe stampa da lista report, api scrollTo su aggiungi cliente ([eb4f168](https://gitlab.pepita.io/getrix/mls-site/commit/eb4f168))



<a name="4.44.1"></a>
## [4.44.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.44.0...v4.44.1) (2020-09-09)


### Bug Fixes

* **agency-estimates:** risolve problema visualizzazione report pro ([9678e1e](https://gitlab.pepita.io/getrix/mls-site/commit/9678e1e))



<a name="4.44.0"></a>
# [4.44.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.43.1...v4.44.0) (2020-09-08)


### Bug Fixes

* corretto path servizio lookup staging ([d495924](https://gitlab.pepita.io/getrix/mls-site/commit/d495924))
* **customer:** modale mobile ([a38325f](https://gitlab.pepita.io/getrix/mls-site/commit/a38325f))
* **customers:** corregge query string dopo cancellazione contenuto filtro fullSearch ([5337903](https://gitlab.pepita.io/getrix/mls-site/commit/5337903))
* **customers:** corregge visualizzazione provincia immobile appuntamento ([9a1565d](https://gitlab.pepita.io/getrix/mls-site/commit/9a1565d))
* **customers:** lookup province comuni esteri, azione lista richieste, visualizzazione nome/cognome ([2e5fe99](https://gitlab.pepita.io/getrix/mls-site/commit/2e5fe99))
* **customers:** rimuove patch per lookup province estere ([c48405c](https://gitlab.pepita.io/getrix/mls-site/commit/c48405c))



<a name="4.42.2"></a>
## [4.42.2](https://gitlab.pepita.io/getrix/mls-site/compare/v4.42.1...v4.42.2) (2020-08-03)


### Bug Fixes

* aggiornata versione editor virtual tour ([bb5d161](https://gitlab.pepita.io/getrix/mls-site/commit/bb5d161))



<a name="4.42.1"></a>
## [4.42.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.42.0...v4.42.1) (2020-07-30)


### Bug Fixes

* **agency-estimates:** check esistenza contatti agente ([05ee235](https://gitlab.pepita.io/getrix/mls-site/commit/05ee235))



<a name="4.41.8"></a>
## [4.41.8](https://gitlab.pepita.io/getrix/mls-site/compare/v4.41.7...v4.41.8) (2020-07-28)


### Bug Fixes

* spenta categoria commerciale ([113ec56](https://gitlab.pepita.io/getrix/mls-site/commit/113ec56))



<a name="4.41.7"></a>
## [4.41.7](https://gitlab.pepita.io/getrix/mls-site/compare/v4.41.6...v4.41.7) (2020-07-28)


### Features

* attivazione nuova categoria negozi ([98766f0](https://gitlab.pepita.io/getrix/mls-site/commit/98766f0))



<a name="4.41.6"></a>
## [4.41.6](https://gitlab.pepita.io/getrix/mls-site/compare/v4.41.5...v4.41.6) (2020-07-28)


### Bug Fixes

* **virtual-tour:** Corrects editFloor data mapping ([28b2f9b](https://gitlab.pepita.io/getrix/mls-site/commit/28b2f9b))
* **virtual-tour:** Corrects error handling on image upload ([3e234aa](https://gitlab.pepita.io/getrix/mls-site/commit/3e234aa))


### Features

* **virtual-tour-3d:** sbloccato pulsante per aggiunta del blurring ([eac9c2d](https://gitlab.pepita.io/getrix/mls-site/commit/eac9c2d))



<a name="4.41.4"></a>
## [4.41.4](https://gitlab.pepita.io/getrix/mls-site/compare/v4.41.3...v4.41.4) (2020-07-24)


### Bug Fixes

* **agency-estimate:** aggiunto controllo completezza step 4 basato sulla presenza dei prezzi automatici ([f6afdaa](https://gitlab.pepita.io/getrix/mls-site/commit/f6afdaa))


### Features

* attivata nuova categoria terreni ([ef98306](https://gitlab.pepita.io/getrix/mls-site/commit/ef98306))



<a name="4.41.1"></a>
## [4.41.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.41.0...v4.41.1) (2020-07-21)


### Features

* attivate categorie Palazzi e Garage ([fe5ab41](https://gitlab.pepita.io/getrix/mls-site/commit/fe5ab41))



<a name="4.39.0"></a>
# [4.39.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.38.1...v4.39.0) (2020-07-08)


### Bug Fixes

* **virtual-tour:** Corrects editHotspot payload data ([a05575f](https://gitlab.pepita.io/getrix/mls-site/commit/a05575f))
* **vt360:** rimosso scroll ([d0a295c](https://gitlab.pepita.io/getrix/mls-site/commit/d0a295c))
* aggiornato getrix-bundles, riattivato csrf token per il form di registrazione delle agenzie ([efc7d4c](https://gitlab.pepita.io/getrix/mls-site/commit/efc7d4c))
* aggiornato getrix-common ([ddbe683](https://gitlab.pepita.io/getrix/mls-site/commit/ddbe683))
* aggiornato getrix-common, traduzioni e sdk ([25d608d](https://gitlab.pepita.io/getrix/mls-site/commit/25d608d))
* Corrects deleteHotspot return type ([7aa7b8d](https://gitlab.pepita.io/getrix/mls-site/commit/7aa7b8d))
* Corrects virtual-tour close on Safari ([f4b74dc](https://gitlab.pepita.io/getrix/mls-site/commit/f4b74dc))
* enables the cachebusting ([c312b23](https://gitlab.pepita.io/getrix/mls-site/commit/c312b23))


### Features

* aggiornata versione getrix-common ([3454cf2](https://gitlab.pepita.io/getrix/mls-site/commit/3454cf2))
* Disables blur area drawing feature ([7683782](https://gitlab.pepita.io/getrix/mls-site/commit/7683782))
* Upgrade virtual-tour editor to v1.2.0 ([ed13ccc](https://gitlab.pepita.io/getrix/mls-site/commit/ed13ccc))
* Upgrade virtual-tour to 1.4.0 ([cba7e81](https://gitlab.pepita.io/getrix/mls-site/commit/cba7e81))
* Upgraded virtual tour to 1.1.0 ([42d401f](https://gitlab.pepita.io/getrix/mls-site/commit/42d401f))
* Upgrades virtual tour to 1.3.0 ([f33243e](https://gitlab.pepita.io/getrix/mls-site/commit/f33243e))



<a name="4.38.1"></a>
## [4.38.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.38.0...v4.38.1) (2020-06-23)


### Bug Fixes

* disabled rotate log ([9e41afb](https://gitlab.pepita.io/getrix/mls-site/commit/9e41afb))



<a name="4.38.0"></a>
# [4.38.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.37.6...v4.38.0) (2020-06-22)


### Bug Fixes

* aggiornata url servizi agenzie indomiopro ([24b9589](https://gitlab.pepita.io/getrix/mls-site/commit/24b9589))
* issue provenienti da sentry ([ea26c22](https://gitlab.pepita.io/getrix/mls-site/commit/ea26c22))
* **agent-pricing:** aumenta raggio marker a 600m. Fix bottone conferma disabilitato ([1e5b423](https://gitlab.pepita.io/getrix/mls-site/commit/1e5b423))
* **agent-pricing:** step5, aggiorna stato con id copertina di default durante creazione nuovo report ([9c67469](https://gitlab.pepita.io/getrix/mls-site/commit/9c67469))
* **property-map:** aggiunge palanguage e scale ealla url delle tile ([99d5205](https://gitlab.pepita.io/getrix/mls-site/commit/99d5205))


### Features

* **agent-pricing:** modifica indirizzo su mappa interattiva ([646df0c](https://gitlab.pepita.io/getrix/mls-site/commit/646df0c))



<a name="4.37.6"></a>
## [4.37.6](https://gitlab.pepita.io/getrix/mls-site/compare/v4.37.5...v4.37.6) (2020-06-17)


### Bug Fixes

* **agent-pricing:** aggiunge check endpoint sul componente geographyFilters ([604ec9f](https://gitlab.pepita.io/getrix/mls-site/commit/604ec9f))
* **agent-pricing:** aggiunge prop showRequiredSymbol  al componente inputGroup ([f1aec3d](https://gitlab.pepita.io/getrix/mls-site/commit/f1aec3d))
* **agent-pricing:** simbolo campo eseguito da richiesto, fix alert cambio stato ([9ec8c5f](https://gitlab.pepita.io/getrix/mls-site/commit/9ec8c5f))



<a name="4.37.3"></a>
## [4.37.3](https://gitlab.pepita.io/getrix/mls-site/compare/v4.37.2...v4.37.3) (2020-06-01)


### Bug Fixes

* **feature-toggle:** corretta attivazione categoria annunci ([01789ea](https://gitlab.pepita.io/getrix/mls-site/commit/01789ea))



<a name="4.37.0"></a>
# [4.37.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.36.6...v4.37.0) (2020-05-28)


### Bug Fixes

* **agent-pricing:** step1, aggiunge controlli quando viene sbiancata select agente ([e5a874e](https://gitlab.pepita.io/getrix/mls-site/commit/e5a874e))
* **report:** box agenzia e altri fix ([69bf9d0](https://gitlab.pepita.io/getrix/mls-site/commit/69bf9d0))
* nascosto virtual tour spagna ([448d8b1](https://gitlab.pepita.io/getrix/mls-site/commit/448d8b1))



<a name="4.36.6"></a>
## [4.36.6](https://gitlab.pepita.io/getrix/mls-site/compare/v4.36.5...v4.36.6) (2020-05-27)


### Bug Fixes

* **agent-pricing:** gestione notify report creato ([507770e](https://gitlab.pepita.io/getrix/mls-site/commit/507770e))
* **agent-pricing:** percentuali relative provenienza stranieri ([49e43b8](https://gitlab.pepita.io/getrix/mls-site/commit/49e43b8))



<a name="4.36.0"></a>
# [4.36.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.35.0...v4.36.0) (2020-05-18)


### Bug Fixes

* **agency-estimates:** impostate costanti per servizio report ([4109089](https://gitlab.pepita.io/getrix/mls-site/commit/4109089))
* **agent-pricing:** cancella prezzi agente contestualmente all update dei prezzi e modifica della valutazione ([23c46cf](https://gitlab.pepita.io/getrix/mls-site/commit/23c46cf))
* **agent-pricing:** corretti errore passaggio da step3 a 4. Migliorato slider. Fix minori ([dea1fe3](https://gitlab.pepita.io/getrix/mls-site/commit/dea1fe3))
* **agent-pricing:** download parallelo delle mappe del dettaglio completo. Fix modifica telefono. Aggiunto ReactQueryDevTools ([79fef8b](https://gitlab.pepita.io/getrix/mls-site/commit/79fef8b))
* **agent-pricing:** fix superficie, gestione redirect modulo spento, migliorata gestione immobili simili tramite cache ([4344428](https://gitlab.pepita.io/getrix/mls-site/commit/4344428))
* **agent-pricing:** gestione errore 403 su api di cambio tipo ([bad8901](https://gitlab.pepita.io/getrix/mls-site/commit/bad8901))
* **agent-pricing:** nel dettaglio report completo vengono scaricate solo le mappe ad alta risoluzione ([f1b8521](https://gitlab.pepita.io/getrix/mls-site/commit/f1b8521))
* **agent-pricing:** risolve redirect landing page da dettaglio base e label raggruppamento scuole ([bb09c8c](https://gitlab.pepita.io/getrix/mls-site/commit/bb09c8c))
* **report:** cover thumb e altro ([aa01739](https://gitlab.pepita.io/getrix/mls-site/commit/aa01739))
* **report:** fix vari ([de45926](https://gitlab.pepita.io/getrix/mls-site/commit/de45926))
* **report:** landing page ([930a51f](https://gitlab.pepita.io/getrix/mls-site/commit/930a51f))
* **report:** modifiche pagina aggiungi report e fix POI pdf ([54adb6b](https://gitlab.pepita.io/getrix/mls-site/commit/54adb6b))
* **report:** modifiche pdf ([58c04b6](https://gitlab.pepita.io/getrix/mls-site/commit/58c04b6))
* aggiornato getrix-common ([624e371](https://gitlab.pepita.io/getrix/mls-site/commit/624e371))
* corretta annotation module report change type action ([234e9fa](https://gitlab.pepita.io/getrix/mls-site/commit/234e9fa))
* **report:** note agente ([3c6f25f](https://gitlab.pepita.io/getrix/mls-site/commit/3c6f25f))



<a name="4.35.0"></a>
# [4.35.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.34.5...v4.35.0) (2020-05-04)


### Features

* attivati uffici per indomioPro ([017fd6c](https://gitlab.pepita.io/getrix/mls-site/commit/017fd6c))



<a name="4.34.2"></a>
## [4.34.2](https://gitlab.pepita.io/getrix/mls-site/compare/v4.34.1...v4.34.2) (2020-04-22)


### Bug Fixes

* Rimuove una chiamata a funzione non definita ([6544a9d](https://gitlab.pepita.io/getrix/mls-site/commit/6544a9d))



<a name="4.34.1"></a>
## [4.34.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.34.0...v4.34.1) (2020-04-22)


### Bug Fixes

* Corregge la gestione degli asset sulla pagina 500 ([4be7c5e](https://gitlab.pepita.io/getrix/mls-site/commit/4be7c5e))
* **assets-copy:** Corrects production build ([d453420](https://gitlab.pepita.io/getrix/mls-site/commit/d453420))



<a name="4.34.0"></a>
# [4.34.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.33.0...v4.34.0) (2020-04-22)


### Bug Fixes

* **a2f:** modificata api key per servizio a2f ([1d3eb37](https://gitlab.pepita.io/getrix/mls-site/commit/1d3eb37))
* **extra-visibilita:** restyle icone extra visibilità annunci ([6b3d501](https://gitlab.pepita.io/getrix/mls-site/commit/6b3d501))
* aggiornate traduzioni ([7724f7d](https://gitlab.pepita.io/getrix/mls-site/commit/7724f7d))
* aggiunta libreria per pepita cli ([c3c1f5f](https://gitlab.pepita.io/getrix/mls-site/commit/c3c1f5f))
* corretta build ci ([99a5017](https://gitlab.pepita.io/getrix/mls-site/commit/99a5017))
* Rimuove il doppio bundle da profile/me e da pf/detail ([a85d3c1](https://gitlab.pepita.io/getrix/mls-site/commit/a85d3c1))


### Features

* Codemod to fix special imports ([bafce9d](https://gitlab.pepita.io/getrix/mls-site/commit/bafce9d))
* **build:** Migration to Pepita CLI ([ffe520f](https://gitlab.pepita.io/getrix/mls-site/commit/ffe520f))



<a name="4.33.0"></a>
# [4.33.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.32.1...v4.33.0) (2020-04-09)



<a name="4.32.1"></a>
## [4.32.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.32.0...v4.32.1) (2020-04-09)


### Bug Fixes

* **sicurezza:** corrette traduzioni e layout tabella ([b56aa15](https://gitlab.pepita.io/getrix/mls-site/commit/b56aa15))



<a name="4.31.0"></a>
# [4.31.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.30.0...v4.31.0) (2020-03-27)


### Bug Fixes

* **dashboard:** differenziata gestione terreni nuovi e vecchi ([e653bef](https://gitlab.pepita.io/getrix/mls-site/commit/e653bef))
* **multinvio:** corretta gestione annunci esteri su immobiliare ([a3383d0](https://gitlab.pepita.io/getrix/mls-site/commit/a3383d0))
* **pie chart:** modificati colori ([5052792](https://gitlab.pepita.io/getrix/mls-site/commit/5052792))


### Features

* **traduzioni:** introdotta cache in fase di building ([a054448](https://gitlab.pepita.io/getrix/mls-site/commit/a054448))
* attivate nuove categorie capannoni e magazzini ([041f123](https://gitlab.pepita.io/getrix/mls-site/commit/041f123))



<a name="4.30.0"></a>
# [4.30.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.29.0...v4.30.0) (2020-03-18)


### Features

* attivazione inserimento nuova categoria garage ([0128a37](https://gitlab.pepita.io/getrix/mls-site/commit/0128a37))



<a name="4.28.4"></a>
## [4.28.4](https://gitlab.pepita.io/getrix/mls-site/compare/v4.28.3...v4.28.4) (2020-03-02)


### Bug Fixes

* **dashboard:** corretto ordine voci statistiche annunci ([30dcd63](https://gitlab.pepita.io/getrix/mls-site/commit/30dcd63))



<a name="4.27.0"></a>
# [4.27.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.26.4...v4.27.0) (2020-02-25)


### Bug Fixes

* **gitlab-ci:** rimosso prepare composer per merge master ([d35808f](https://gitlab.pepita.io/getrix/mls-site/commit/d35808f))
* **impostazioni:** corrette chiavi e traduzioni in inserimento video youtube agenzia ([46785c6](https://gitlab.pepita.io/getrix/mls-site/commit/46785c6))
* property exist ([f696c0c](https://gitlab.pepita.io/getrix/mls-site/commit/f696c0c))


### Features

* **dashboard:** aggiunte nuove categorie ([fda8443](https://gitlab.pepita.io/getrix/mls-site/commit/fda8443))



<a name="4.26.4"></a>
## [4.26.4](https://gitlab.pepita.io/getrix/mls-site/compare/v4.26.3...v4.26.4) (2020-02-13)


### Bug Fixes

* **agent-pricing:** corretto stampa e download pdf ([0d112e8](https://gitlab.pepita.io/getrix/mls-site/commit/0d112e8))
* **agent-pricing:** ripristinato codice precedente per il download dei pdf ([e5c763d](https://gitlab.pepita.io/getrix/mls-site/commit/e5c763d))
* **billboard:** corregge impossibilita modifica titolo, riferimento e telefono ([28d7b30](https://gitlab.pepita.io/getrix/mls-site/commit/28d7b30))
* **gitlab-ci:** rimossa build/composer dev per branch master ([f8dbd0d](https://gitlab.pepita.io/getrix/mls-site/commit/f8dbd0d))


### Features

* **hotjar:** aggiunge snippet hotjar nell pagine di report immobiliare ([bab11a0](https://gitlab.pepita.io/getrix/mls-site/commit/bab11a0))



<a name="4.26.3"></a>
## [4.26.3](https://gitlab.pepita.io/getrix/mls-site/compare/v4.26.2...v4.26.3) (2020-02-12)


### Bug Fixes

* **agent-pricing:** corregge mapping classe energetica esente e in attesa ([252fe9a](https://gitlab.pepita.io/getrix/mls-site/commit/252fe9a))



<a name="4.26.2"></a>
## [4.26.2](https://gitlab.pepita.io/getrix/mls-site/compare/v4.26.1...v4.26.2) (2020-02-11)


### Bug Fixes

* **agent-pricing:** rivisto metodo di download del dettaglio ([5a20934](https://gitlab.pepita.io/getrix/mls-site/commit/5a20934))



<a name="4.26.1"></a>
## [4.26.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.26.0...v4.26.1) (2020-02-11)


### Bug Fixes

* **agent-pricing:** corretto comportamento per firefox ([601cb02](https://gitlab.pepita.io/getrix/mls-site/commit/601cb02))
* **agent-princing:** corregge url prod api ([0e81bdb](https://gitlab.pepita.io/getrix/mls-site/commit/0e81bdb))



<a name="4.25.3"></a>
## [4.25.3](https://gitlab.pepita.io/getrix/mls-site/compare/v4.25.2...v4.25.3) (2020-02-04)


### Bug Fixes

* aggiornate traduzioni ([1a8f1ad](https://gitlab.pepita.io/getrix/mls-site/commit/1a8f1ad))



<a name="4.25.2"></a>
## [4.25.2](https://gitlab.pepita.io/getrix/mls-site/compare/v4.25.1...v4.25.2) (2020-01-17)


### Bug Fixes

* aggirato momentanemanete il controllo dei trusted proxy ([500f065](https://gitlab.pepita.io/getrix/mls-site/commit/500f065))



<a name="4.25.1"></a>
## [4.25.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.25.0...v4.25.1) (2020-01-15)


### Bug Fixes

* **fatture:** corrette traduzioni stati fatture ([904091c](https://gitlab.pepita.io/getrix/mls-site/commit/904091c))



<a name="4.25.0"></a>
# [4.25.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.24.1...v4.25.0) (2020-01-14)


### Features

* **acquisizione-privati:** aggiunto conteggio accessi a sezione ([b259cf5](https://gitlab.pepita.io/getrix/mls-site/commit/b259cf5))



<a name="4.24.1"></a>
## [4.24.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.24.0...v4.24.1) (2019-12-20)


### Features

* **valutazioni:** migliorata impaginazione geografia ([e81612d](https://gitlab.pepita.io/getrix/mls-site/commit/e81612d))
* **valutazioni:** spostate righe geografia ([168039e](https://gitlab.pepita.io/getrix/mls-site/commit/168039e))



<a name="4.24.0"></a>
# [4.24.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.23.3...v4.24.0) (2019-12-12)


### Features

* **login:** notifica tentativo login agenzia disabilitata ([3566094](https://gitlab.pepita.io/getrix/mls-site/commit/3566094))



<a name="4.23.3"></a>
## [4.23.3](https://gitlab.pepita.io/getrix/mls-site/compare/v4.23.2...v4.23.3) (2019-12-09)


### Bug Fixes

* risolve problema traspiling gesu getrix-common, fix polyfill ([d3429d9](https://gitlab.pepita.io/getrix/mls-site/commit/d3429d9))



<a name="4.23.2"></a>
## [4.23.2](https://gitlab.pepita.io/getrix/mls-site/compare/v4.23.1...v4.23.2) (2019-11-27)


### Bug Fixes

* metriche ([f90321d](https://gitlab.pepita.io/getrix/mls-site/commit/f90321d))



<a name="4.23.1"></a>
## [4.23.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.23.0...v4.23.1) (2019-11-27)


### Bug Fixes

* corretto url produzione ([49313f1](https://gitlab.pepita.io/getrix/mls-site/commit/49313f1))
* **acquisizione:** sistemate metriche ([080eaff](https://gitlab.pepita.io/getrix/mls-site/commit/080eaff))



<a name="4.23.0"></a>
# [4.23.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.22.4...v4.23.0) (2019-11-25)


### Bug Fixes

* aggiunto controllo url anche su id annuncio ([c04b2b3](https://gitlab.pepita.io/getrix/mls-site/commit/c04b2b3))


### Features

* **acquisizione:** verifica validita annuncio ([6c16545](https://gitlab.pepita.io/getrix/mls-site/commit/6c16545))



<a name="4.22.4"></a>
## [4.22.4](https://gitlab.pepita.io/getrix/mls-site/compare/v4.22.3...v4.22.4) (2019-11-22)


### Bug Fixes

* **esportazioni-attive:** risolve problema validazione se una delle date (inizio/fine contratto) non e visibile ([25818e0](https://gitlab.pepita.io/getrix/mls-site/commit/25818e0))



<a name="4.22.3"></a>
## [4.22.3](https://gitlab.pepita.io/getrix/mls-site/compare/v4.22.2...v4.22.3) (2019-11-22)


### Bug Fixes

* rimosso csrf token dai form gestiti da symfony ([557e4cf](https://gitlab.pepita.io/getrix/mls-site/commit/557e4cf))



<a name="4.22.2"></a>
## [4.22.2](https://gitlab.pepita.io/getrix/mls-site/compare/v4.22.1...v4.22.2) (2019-11-21)


### Bug Fixes

* gestita eccezione generica ([ad2e2a5](https://gitlab.pepita.io/getrix/mls-site/commit/ad2e2a5))
* sistemato stile resize page ([8a0f6a6](https://gitlab.pepita.io/getrix/mls-site/commit/8a0f6a6))



<a name="4.22.1"></a>
## [4.22.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.22.0...v4.22.1) (2019-11-15)


### Bug Fixes

* label pagine pubblicità ([899a662](https://gitlab.pepita.io/getrix/mls-site/commit/899a662))



<a name="4.21.1"></a>
## [4.21.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.21.0...v4.21.1) (2019-11-14)


### Bug Fixes

* **module:** fixato checker estimates ([92a72cc](https://gitlab.pepita.io/getrix/mls-site/commit/92a72cc))



<a name="4.21.0"></a>
# [4.21.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.20.0...v4.21.0) (2019-11-13)


### Bug Fixes

* aggiorato getrix-bundles ([95a7293](https://gitlab.pepita.io/getrix/mls-site/commit/95a7293))
* aggiornato getrix-bundles ([a5bc7c7](https://gitlab.pepita.io/getrix/mls-site/commit/a5bc7c7))
* corrette dipendenze controller users e estimates ([6339d3c](https://gitlab.pepita.io/getrix/mls-site/commit/6339d3c))
* **estimtes:** valutazioni senza zona ([f95fc6f](https://gitlab.pepita.io/getrix/mls-site/commit/f95fc6f))
* **pubblicità:** fixato side-menu e title ([9613938](https://gitlab.pepita.io/getrix/mls-site/commit/9613938))
* **stats:** fixati tipo e nome servizio ([70346c3](https://gitlab.pepita.io/getrix/mls-site/commit/70346c3))


### Features

* **estimates:** landing page valutazioni ([7c347b7](https://gitlab.pepita.io/getrix/mls-site/commit/7c347b7))
* **estimates:** landing page valutazioni ([0ed02d1](https://gitlab.pepita.io/getrix/mls-site/commit/0ed02d1))
* **landing-page:** aggiornata grafica e aggiunta pagina per modulo richieste di valutazione ([90c859d](https://gitlab.pepita.io/getrix/mls-site/commit/90c859d))
* **module-activation:** landing page attivazione moduli ([f5468dd](https://gitlab.pepita.io/getrix/mls-site/commit/f5468dd))



<a name="4.20.0"></a>
# [4.20.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.19.4...v4.20.0) (2019-11-07)



<a name="4.19.4"></a>
## [4.19.4](https://gitlab.pepita.io/getrix/mls-site/compare/v4.19.3...v4.19.4) (2019-10-30)


### Bug Fixes

* corretta url mediaserver indomiopro.es ([2c78261](https://gitlab.pepita.io/getrix/mls-site/commit/2c78261))



<a name="4.19.3"></a>
## [4.19.3](https://gitlab.pepita.io/getrix/mls-site/compare/v4.19.2...v4.19.3) (2019-10-28)


### Bug Fixes

* ripristinata url mediaserver se non in ambiente prod ([cba057a](https://gitlab.pepita.io/getrix/mls-site/commit/cba057a))
* **agenzia:** fixato controllo orari vuoti ([ef8faab](https://gitlab.pepita.io/getrix/mls-site/commit/ef8faab))



<a name="4.19.2"></a>
## [4.19.2](https://gitlab.pepita.io/getrix/mls-site/compare/v4.19.1...v4.19.2) (2019-10-28)


### Bug Fixes

* aggiornato sdk per consentire inserimento in immagini_hd in fase di inserimento immagini agenzia ([640ec0b](https://gitlab.pepita.io/getrix/mls-site/commit/640ec0b))



<a name="4.19.1"></a>
## [4.19.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.19.0...v4.19.1) (2019-10-25)


### Bug Fixes

* **orari-agenzia:** nascosto check 'solo su appuntamento' ([9222fd2](https://gitlab.pepita.io/getrix/mls-site/commit/9222fd2))



<a name="4.19.0"></a>
# [4.19.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.18.0...v4.19.0) (2019-10-25)


### Features

* **service-agenzie:** orari agenzie ([2d43d26](https://gitlab.pepita.io/getrix/mls-site/commit/2d43d26))



<a name="4.18.0"></a>
# [4.18.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.17.2...v4.18.0) (2019-10-25)


### Bug Fixes

* **feature-toggle:** spenta la sezione zone per indomiopro ([607b767](https://gitlab.pepita.io/getrix/mls-site/commit/607b767))
* aggiornata versione getrix-common ([da06544](https://gitlab.pepita.io/getrix/mls-site/commit/da06544))


### Features

* side menu/header helper. Twig parser ([c907078](https://gitlab.pepita.io/getrix/mls-site/commit/c907078))
* stile nuovo header sezione ([0973f2c](https://gitlab.pepita.io/getrix/mls-site/commit/0973f2c))



<a name="4.17.2"></a>
## [4.17.2](https://gitlab.pepita.io/getrix/mls-site/compare/v4.17.1...v4.17.2) (2019-10-18)


### Bug Fixes

* corrette label valutazioni + aggiornato sdk ([85348f9](https://gitlab.pepita.io/getrix/mls-site/commit/85348f9))
* modificata url api migration verifier per staging di indomio con quella di produzione ([6bc2482](https://gitlab.pepita.io/getrix/mls-site/commit/6bc2482))



<a name="4.17.0"></a>
# [4.17.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.16.2...v4.17.0) (2019-10-02)


### Bug Fixes

* **valutazioni:** ricerche utente: corregge format del prezzo in assenza del contratto ([1ed7f82](https://gitlab.pepita.io/getrix/mls-site/commit/1ed7f82))
* settings media agency url ([dc962ab](https://gitlab.pepita.io/getrix/mls-site/commit/dc962ab))


### Features

* **annunci:** posizione ricerca annuncio ([55aac51](https://gitlab.pepita.io/getrix/mls-site/commit/55aac51))



<a name="4.16.2"></a>
## [4.16.2](https://gitlab.pepita.io/getrix/mls-site/compare/v4.16.1...v4.16.2) (2019-10-01)


### Bug Fixes

* rimossi path fs nfs per fare in modo che le immagini siano gestite solo tramite sapi ([bd2d1b9](https://gitlab.pepita.io/getrix/mls-site/commit/bd2d1b9))


### Features

* **acquisizione:** rimosso riferimento portale da annunci e filtri. Aggiunto componente react Image ([fe49f07](https://gitlab.pepita.io/getrix/mls-site/commit/fe49f07))



<a name="4.16.1"></a>
## [4.16.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.16.0...v4.16.1) (2019-09-24)


### Bug Fixes

* **acquisizione:** rispristinato metodo get-private-propery nell handler ([914be1a](https://gitlab.pepita.io/getrix/mls-site/commit/914be1a))



<a name="4.16.0"></a>
# [4.16.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.15.1...v4.16.0) (2019-09-24)


### Bug Fixes

* **acquisizione:** corregge regex rotta sett-outcome ([d57be49](https://gitlab.pepita.io/getrix/mls-site/commit/d57be49))
* **acquisizione-privati:** validazione filtro riferimento ([f8621c1](https://gitlab.pepita.io/getrix/mls-site/commit/f8621c1))
* **backlink:** snackbar and fix divider error ([a1d9426](https://gitlab.pepita.io/getrix/mls-site/commit/a1d9426))
* **valutazioni:** aggiunti controlli formatter ricerche. Cursore su mostra altre zone ([d3a6acc](https://gitlab.pepita.io/getrix/mls-site/commit/d3a6acc))
* **valutazioni:** corregge format delle date restituite al client ([dadddf0](https://gitlab.pepita.io/getrix/mls-site/commit/dadddf0))



<a name="4.15.1"></a>
## [4.15.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.15.0...v4.15.1) (2019-09-20)


### Bug Fixes

* **zone-valutazioni:** corretta generazione url ricerche ([d1bc3f9](https://gitlab.pepita.io/getrix/mls-site/commit/d1bc3f9))



<a name="4.15.0"></a>
# [4.15.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.14.0...v4.15.0) (2019-09-20)


### Bug Fixes

* **valutazioni:** corretto messaggio ([9c46520](https://gitlab.pepita.io/getrix/mls-site/commit/9c46520))
* **valutazioni:** history back sul click bottone torna indietro nel dettaglio ([a4824d7](https://gitlab.pepita.io/getrix/mls-site/commit/a4824d7))


### Features

* **valutazioni:** aggiunge ricerche salvate al dettaglio. Aggiunge filtri, ordinamento e paginazione alla lista. Integrazione servizio sdk ([0f7721e](https://gitlab.pepita.io/getrix/mls-site/commit/0f7721e))



<a name="4.14.0"></a>
# [4.14.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.13.1...v4.14.0) (2019-09-18)


### Bug Fixes

* **login:** allineamento testo bottone ([f01387a](https://gitlab.pepita.io/getrix/mls-site/commit/f01387a))


### Features

* **backlink:** nuova gestione link dettaglio annuncio ([8d1b383](https://gitlab.pepita.io/getrix/mls-site/commit/8d1b383))
* **backlink:** nuova gestione link dettaglio annuncio ([ff7430c](https://gitlab.pepita.io/getrix/mls-site/commit/ff7430c))



<a name="4.13.1"></a>
## [4.13.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.13.0...v4.13.1) (2019-09-06)


### Bug Fixes

* **storybook:** Ripristina il funzionamento di storybook ([c055eaa](https://gitlab.pepita.io/getrix/mls-site/commit/c055eaa))


### Features

* rimossa autorizzazione privacy ([15a53d1](https://gitlab.pepita.io/getrix/mls-site/commit/15a53d1))



<a name="4.13.0"></a>
# [4.13.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.12.4...v4.13.0) (2019-09-02)


### Bug Fixes

* **immagini-portali:** rimosso commento per poter utilizzare la nuova rotta ([c4c38aa](https://gitlab.pepita.io/getrix/mls-site/commit/c4c38aa))
* aggiornati vendor traduzioni ([98c739c](https://gitlab.pepita.io/getrix/mls-site/commit/98c739c))



<a name="4.12.4"></a>
## [4.12.4](https://gitlab.pepita.io/getrix/mls-site/compare/v4.12.3...v4.12.4) (2019-08-28)


### Bug Fixes

* rimosso testo non più utilizzato da template mr ([a2635ed](https://gitlab.pepita.io/getrix/mls-site/commit/a2635ed))
* **multinvio:** corregge malfunzionamento filtro stato invio ([9fa42c3](https://gitlab.pepita.io/getrix/mls-site/commit/9fa42c3))
* **portali:** commentato metodo per evitare temporaneamente di passare alla nuova gestione delle immagini ([e8fa3f8](https://gitlab.pepita.io/getrix/mls-site/commit/e8fa3f8))


### Features

* **multinvio:** rotta dedicata loghi portali ([6979e52](https://gitlab.pepita.io/getrix/mls-site/commit/6979e52))
* **thumbnail:** visualizzazione thumbnail acquisizione privati ([3b85a27](https://gitlab.pepita.io/getrix/mls-site/commit/3b85a27))



<a name="4.12.3"></a>
## [4.12.3](https://gitlab.pepita.io/getrix/mls-site/compare/v4.12.2...v4.12.3) (2019-08-02)


### Features

* **estimate:** mapping feedback ([0ee0ecd](https://gitlab.pepita.io/getrix/mls-site/commit/0ee0ecd))



<a name="4.12.2"></a>
## [4.12.2](https://gitlab.pepita.io/getrix/mls-site/compare/v4.12.1...v4.12.2) (2019-08-01)


### Bug Fixes

* **acquisizione:** mostra bottone rimuovi filtri quando si ricarica la pagina con il codice in qs ([2e516fc](https://gitlab.pepita.io/getrix/mls-site/commit/2e516fc))
* **acquisizione:** spaziature ([7d802e5](https://gitlab.pepita.io/getrix/mls-site/commit/7d802e5))
* **acquisizione-privati:** gestione querystring e richiesta con filtro codice annuncio. Rimozione select risultati pagina con solo un risultato ([7694c3b](https://gitlab.pepita.io/getrix/mls-site/commit/7694c3b))
* **valutazione:** spaziature verticali valutazione + placeholder codiceSDI ([44d2e8d](https://gitlab.pepita.io/getrix/mls-site/commit/44d2e8d))
* **valutazioni:** cdn mappe statiche immopro ([019d3aa](https://gitlab.pepita.io/getrix/mls-site/commit/019d3aa))
* **valutazioni:** corretta gestione informazioni di giardino, garage e terrazzo/balcone ([5421814](https://gitlab.pepita.io/getrix/mls-site/commit/5421814))
* **valutazioni:** modifica label stato vendita in progetto di vendita ([27242aa](https://gitlab.pepita.io/getrix/mls-site/commit/27242aa))
* aggiornato getrix-common ([96f85ae](https://gitlab.pepita.io/getrix/mls-site/commit/96f85ae))
* aggiornato getrix-common ([937c082](https://gitlab.pepita.io/getrix/mls-site/commit/937c082))



<a name="4.12.1"></a>
## [4.12.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.12.0...v4.12.1) (2019-07-24)


### Bug Fixes

* aggiornato getrix-common ([5576cf2](https://gitlab.pepita.io/getrix/mls-site/commit/5576cf2))
* corretto nome feature zone ([a983fa8](https://gitlab.pepita.io/getrix/mls-site/commit/a983fa8))



<a name="4.12.0"></a>
# [4.12.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.11.0...v4.12.0) (2019-07-24)


### Bug Fixes

* aggiornato getrix-common ([b7fbe25](https://gitlab.pepita.io/getrix/mls-site/commit/b7fbe25))
* aggiunto controllo su accesso a dati agenzia ([c91ad35](https://gitlab.pepita.io/getrix/mls-site/commit/c91ad35))
* **valutazioni-immobiliare:** corretto valore di ritorno ([7303e95](https://gitlab.pepita.io/getrix/mls-site/commit/7303e95))


### Features

* **valutazioni:** listing e dettaglio richieste di valutazione ([243f1a6](https://gitlab.pepita.io/getrix/mls-site/commit/243f1a6))



<a name="4.11.0"></a>
# [4.11.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.10.2...v4.11.0) (2019-07-24)


### Bug Fixes

* **acquisizione:** modifica ordine campi ([6aaec22](https://gitlab.pepita.io/getrix/mls-site/commit/6aaec22))
* **acquisizione-privati:** corrette proprietà metodi ([5d8ab53](https://gitlab.pepita.io/getrix/mls-site/commit/5d8ab53))
* **acquisizione-privati:** corretti mapping ([64a64ca](https://gitlab.pepita.io/getrix/mls-site/commit/64a64ca))
* **acquisizione-privati:** rimosso controllo su parametro ([a9614e8](https://gitlab.pepita.io/getrix/mls-site/commit/a9614e8))
* **agent:** sistemato layout form ([a947095](https://gitlab.pepita.io/getrix/mls-site/commit/a947095))
* **agent:** sistemato layout form ([29f23bc](https://gitlab.pepita.io/getrix/mls-site/commit/29f23bc))


### Features

* **acquisizione:** aggiunge api rest per recupero dati annuncio. Aggiunge info portale nella querystring della url riscontro ([9473dfa](https://gitlab.pepita.io/getrix/mls-site/commit/9473dfa))
* **acquisizione:** aggiunto filtro portale ([21b4a11](https://gitlab.pepita.io/getrix/mls-site/commit/21b4a11))



<a name="4.10.2"></a>
## [4.10.2](https://gitlab.pepita.io/getrix/mls-site/compare/v4.10.1...v4.10.2) (2019-07-22)


### Bug Fixes

* **modularizzazione:** aggiunto refresh delle estensioni in fase di controllo stato ([52a29db](https://gitlab.pepita.io/getrix/mls-site/commit/52a29db))



<a name="4.10.1"></a>
## [4.10.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.10.0...v4.10.1) (2019-07-19)


### Bug Fixes

* **acquisizione-privati:** corretto nome annotation modularizzazione ([7fdbbcb](https://gitlab.pepita.io/getrix/mls-site/commit/7fdbbcb))



<a name="4.10.0"></a>
# [4.10.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.9.2...v4.10.0) (2019-07-19)


### Bug Fixes

* **lading-page-attivazione:** aggiunge gestione notifiche e loader al submit del form ([18e4e49](https://gitlab.pepita.io/getrix/mls-site/commit/18e4e49))


### Features

* **landing attivazione:** landing multisend ([5a5aa64](https://gitlab.pepita.io/getrix/mls-site/commit/5a5aa64))
* **landing-page:** landing acquisition + fix form tel input error background ([e985a15](https://gitlab.pepita.io/getrix/mls-site/commit/e985a15))



<a name="4.9.2"></a>
## [4.9.2](https://gitlab.pepita.io/getrix/mls-site/compare/v4.9.1...v4.9.2) (2019-07-12)


### Bug Fixes

* **settings:** corretta traduzione titolo pagina sicurezza ([ebdfb13](https://gitlab.pepita.io/getrix/mls-site/commit/ebdfb13))
* aggiornati vendor traduzioni ([4890cdf](https://gitlab.pepita.io/getrix/mls-site/commit/4890cdf))
* aggiunta gestione a tutte le build della ci ([d456fd3](https://gitlab.pepita.io/getrix/mls-site/commit/d456fd3))
* corretti parametri d'ambiente di staging ([83edad0](https://gitlab.pepita.io/getrix/mls-site/commit/83edad0))



<a name="4.9.0"></a>
# [4.9.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.8.0...v4.9.0) (2019-07-04)


### Bug Fixes

* **multinvio-progetti:** gestione interfaccia nel caso in cui non ci siano portali attivi ([491e1c0](https://gitlab.pepita.io/getrix/mls-site/commit/491e1c0))



<a name="4.8.0"></a>
# [4.8.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.7.3...v4.8.0) (2019-06-24)


### Bug Fixes

* aggiunge pepita ai moduli da trasformare in ES5 ([0ca5446](https://gitlab.pepita.io/getrix/mls-site/commit/0ca5446))
* aggiunti header no-cache alle redirect ([e918f0a](https://gitlab.pepita.io/getrix/mls-site/commit/e918f0a))
* corretta cancellazione immagine nel caso in cui non venga effettuato il salvataggio su filesystem (indomioPro) ([46672ce](https://gitlab.pepita.io/getrix/mls-site/commit/46672ce))
* polyfill assign, fix path risorse webpack e watch ([1fbefa3](https://gitlab.pepita.io/getrix/mls-site/commit/1fbefa3))
* **login:** revert url login rest ([9532227](https://gitlab.pepita.io/getrix/mls-site/commit/9532227))
* **login:** side's height ([130faeb](https://gitlab.pepita.io/getrix/mls-site/commit/130faeb))
* rimossi warning linter ([67aa67a](https://gitlab.pepita.io/getrix/mls-site/commit/67aa67a))
* **profilo-utente:** corretta etichetta traduzione e aggiunto autocomplete off ([e2bd177](https://gitlab.pepita.io/getrix/mls-site/commit/e2bd177))



<a name="4.7.3"></a>
## [4.7.3](https://gitlab.pepita.io/getrix/mls-site/compare/v4.7.2...v4.7.3) (2019-06-17)


### Bug Fixes

* aggiornato getrix-common ([40463c4](https://gitlab.pepita.io/getrix/mls-site/commit/40463c4))
* aggiornato getrix-common e vendor traduzioni ([5402ddf](https://gitlab.pepita.io/getrix/mls-site/commit/5402ddf))
* aggiornato sdk e vendor traduzioni ([9be2bc4](https://gitlab.pepita.io/getrix/mls-site/commit/9be2bc4))
* settata key google analytics per pro.indomio.es ([be79459](https://gitlab.pepita.io/getrix/mls-site/commit/be79459))



<a name="4.7.2"></a>
## [4.7.2](https://gitlab.pepita.io/getrix/mls-site/compare/v4.7.1...v4.7.2) (2019-06-14)


### Bug Fixes

* aggiornamento vendor traduzioni ([b0c8732](https://gitlab.pepita.io/getrix/mls-site/commit/b0c8732))



<a name="4.7.1"></a>
## [4.7.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.7.0...v4.7.1) (2019-06-13)


### Bug Fixes

* **esportazioni:** fix invio email attivazione portale ([39f9e45](https://gitlab.pepita.io/getrix/mls-site/commit/39f9e45))
* **sede:** aumenta larghezza box bianco della mappa ([736ff5b](https://gitlab.pepita.io/getrix/mls-site/commit/736ff5b))
* aggiornato getrix-common ([6b49190](https://gitlab.pepita.io/getrix/mls-site/commit/6b49190))
* aggiornato getrix-common ([a9ccd35](https://gitlab.pepita.io/getrix/mls-site/commit/a9ccd35))
* aggiornato vendor traduzioni ([8cc60e6](https://gitlab.pepita.io/getrix/mls-site/commit/8cc60e6))



<a name="4.7.0"></a>
# [4.7.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.6.0...v4.7.0) (2019-06-13)


### Bug Fixes

* **icons messages:** icone messaggi recupera password ([a7cf14e](https://gitlab.pepita.io/getrix/mls-site/commit/a7cf14e))
* **recovery-password:** corretto messaggio errore utente non trovato ([711c01b](https://gitlab.pepita.io/getrix/mls-site/commit/711c01b))
* aggiornati vendor traduzioni e getrix-common ([f32dbc1](https://gitlab.pepita.io/getrix/mls-site/commit/f32dbc1))
* aggiornato getrix-common ([c0d7dd2](https://gitlab.pepita.io/getrix/mls-site/commit/c0d7dd2))
* corretto messaggio modale planimetrie e aggiornato vendor traduzioni ([5e4a3e0](https://gitlab.pepita.io/getrix/mls-site/commit/5e4a3e0))


### Features

* **email-set-password:** invio email set password agenzie migrate di indomio, refactoring ([76d0ad9](https://gitlab.pepita.io/getrix/mls-site/commit/76d0ad9))



<a name="4.6.0"></a>
# [4.6.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.5.4...v4.6.0) (2019-06-11)


### Bug Fixes

* aggiornamento getrix-common e vendor trnaslations ([406352d](https://gitlab.pepita.io/getrix/mls-site/commit/406352d))
* aggiornamento vendor traduzioni ([95f87da](https://gitlab.pepita.io/getrix/mls-site/commit/95f87da))
* aggiornati getrix-bundles, getrix-common e vendor indomio/translations ([10d66cf](https://gitlab.pepita.io/getrix/mls-site/commit/10d66cf))
* **image-editor:** traduzione testi dei bottoni ([4a36ec3](https://gitlab.pepita.io/getrix/mls-site/commit/4a36ec3))
* aggiornati getrix-common e getrix-bundles ([0d5347d](https://gitlab.pepita.io/getrix/mls-site/commit/0d5347d))
* **agent:** Fix su update di agente loggato ([16693f8](https://gitlab.pepita.io/getrix/mls-site/commit/16693f8))
* **agenti:** corretta validazione ([2e4fd55](https://gitlab.pepita.io/getrix/mls-site/commit/2e4fd55))
* **contatti-utente:** corretto prefisso di default in aggiunta nuovo contatto ([d88fbe3](https://gitlab.pepita.io/getrix/mls-site/commit/d88fbe3))
* **login:** allineamento icone ([570e429](https://gitlab.pepita.io/getrix/mls-site/commit/570e429))
* **login:** parametrizzato logo ([a9e60b7](https://gitlab.pepita.io/getrix/mls-site/commit/a9e60b7))
* **logo login:** svg optimization ([f07519e](https://gitlab.pepita.io/getrix/mls-site/commit/f07519e))
* **mail:** corretta traduzione testo mail recovery password ([b356896](https://gitlab.pepita.io/getrix/mls-site/commit/b356896))
* **profile:** aggiunta validazione form a inserimento immagine profilo ([69ba283](https://gitlab.pepita.io/getrix/mls-site/commit/69ba283))
* **profile:** fix validazione profile prima di caricamento immagine ([d3cda71](https://gitlab.pepita.io/getrix/mls-site/commit/d3cda71))
* aggiornato vendor traduzioni ([ca6c3d7](https://gitlab.pepita.io/getrix/mls-site/commit/ca6c3d7))
* **profilo-utente:** evita di mostrare il type nell input del comune di residenza ([6c22917](https://gitlab.pepita.io/getrix/mls-site/commit/6c22917))
* aggiornati vendor traduzioni ([f73f09f](https://gitlab.pepita.io/getrix/mls-site/commit/f73f09f))
* aggiornati vendor traduzioni ([e2569d1](https://gitlab.pepita.io/getrix/mls-site/commit/e2569d1))
* aggiornato getrix-common ([36cd06a](https://gitlab.pepita.io/getrix/mls-site/commit/36cd06a))
* aggiornato getrix-common e vendor traduzioni, trustata tutta la rete di indomio ([948e41a](https://gitlab.pepita.io/getrix/mls-site/commit/948e41a))
* aggiornato vendor traduzioni ([940d562](https://gitlab.pepita.io/getrix/mls-site/commit/940d562))
* aggiunta configurazione mancante ([5c6e5e5](https://gitlab.pepita.io/getrix/mls-site/commit/5c6e5e5))
* **property-finder:** rimossa mappa interattiva con cartine blu ([54d115e](https://gitlab.pepita.io/getrix/mls-site/commit/54d115e))
* aggiunta gestione cambio password agente titolare ([6e1f15b](https://gitlab.pepita.io/getrix/mls-site/commit/6e1f15b))
* aggiunto ID NAZIONE DEFAULT a costanti client ([11e48b1](https://gitlab.pepita.io/getrix/mls-site/commit/11e48b1))
* **registrazione:** Corretta mail destinataria della notifica di avvenuta registrazione ([a13e2e6](https://gitlab.pepita.io/getrix/mls-site/commit/a13e2e6))
* corrette traduzioni title pagina sede e nomi visibilita agenzia ([5e1923f](https://gitlab.pepita.io/getrix/mls-site/commit/5e1923f))
* corretto nome virtual tour su getrix-bundles ([bef8265](https://gitlab.pepita.io/getrix/mls-site/commit/bef8265))
* **settings:** corretta etichetta traduzione placeholder input comune ([14766d9](https://gitlab.pepita.io/getrix/mls-site/commit/14766d9))
* **utente:** in attesa del pattern di validazione del nif spagnolo viene bypassato il controllo del codice fiscale ([de4c853](https://gitlab.pepita.io/getrix/mls-site/commit/de4c853))
* **utente:** risolve problema autocomplete comune di residenza per indomio pro ([3e2a4e0](https://gitlab.pepita.io/getrix/mls-site/commit/3e2a4e0))



<a name="4.5.4"></a>
## [4.5.4](https://gitlab.pepita.io/getrix/mls-site/compare/v4.5.3...v4.5.4) (2019-05-24)



<a name="4.5.3"></a>
## [4.5.3](https://gitlab.pepita.io/getrix/mls-site/compare/v4.5.2...v4.5.3) (2019-05-15)


### Bug Fixes

* **agenzia:** corretta agenzia light ([8bf4938](https://gitlab.pepita.io/getrix/mls-site/commit/8bf4938))
* aggiornata versione getrix-common ([6a61a28](https://gitlab.pepita.io/getrix/mls-site/commit/6a61a28))
* aggiornata versione sdk ([4a705e5](https://gitlab.pepita.io/getrix/mls-site/commit/4a705e5))


### Features

* **support:** cambiato indirizzo email ([d5567f5](https://gitlab.pepita.io/getrix/mls-site/commit/d5567f5))



<a name="4.5.2"></a>
## [4.5.2](https://gitlab.pepita.io/getrix/mls-site/compare/v4.5.1...v4.5.2) (2019-05-07)


### Bug Fixes

* **dashboard:** corretta inizializzazione array statistiche di pubblicazione ([de0572f](https://gitlab.pepita.io/getrix/mls-site/commit/de0572f))
* aggiornata versione sdk ([e87499f](https://gitlab.pepita.io/getrix/mls-site/commit/e87499f))



<a name="4.5.1"></a>
## [4.5.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.5.0...v4.5.1) (2019-03-26)


### Bug Fixes

* **cartellone:** corretto pdf con immagine verticale ([863a762](https://gitlab.pepita.io/getrix/mls-site/commit/863a762))
* modificato stile bottoni Annulla secondo le direttive dello styleguide ([b1eddae](https://gitlab.pepita.io/getrix/mls-site/commit/b1eddae))



<a name="4.5.0"></a>
# [4.5.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.4.1...v4.5.0) (2019-03-22)


### Bug Fixes

* **collaborazioni:** corretta posizione loader ([e0cd191](https://gitlab.pepita.io/getrix/mls-site/commit/e0cd191))
* corretta configurazione feature toggle pro ([e003106](https://gitlab.pepita.io/getrix/mls-site/commit/e003106))
* corretto path configurazione immobiliarepro ([ffaf0c4](https://gitlab.pepita.io/getrix/mls-site/commit/ffaf0c4))
* rimosso folder non più esistente ([3a0728c](https://gitlab.pepita.io/getrix/mls-site/commit/3a0728c))


### Features

* svg icons ([c5a32c4](https://gitlab.pepita.io/getrix/mls-site/commit/c5a32c4))



<a name="4.4.1"></a>
## [4.4.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.4.0...v4.4.1) (2019-03-20)


### Bug Fixes

* aggiunta copia .env indomiopro-es ([34ddc3c](https://gitlab.pepita.io/getrix/mls-site/commit/34ddc3c))
* clear cache composer ([22a1772](https://gitlab.pepita.io/getrix/mls-site/commit/22a1772))
* rivista organizzazione configurazione wl ([980c647](https://gitlab.pepita.io/getrix/mls-site/commit/980c647))
* **whitelabel:** corretto riferimento a pagina di errore ([4a2236c](https://gitlab.pepita.io/getrix/mls-site/commit/4a2236c))



<a name="4.4.0"></a>
# [4.4.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.3.0...v4.4.0) (2019-03-19)


### Bug Fixes

* aggiornata versione getrix-common ([a71bab3](https://gitlab.pepita.io/getrix/mls-site/commit/a71bab3))
* **menu:** corretto nome sezione ([65e7a9c](https://gitlab.pepita.io/getrix/mls-site/commit/65e7a9c))
* rimossa creazione artefatto staging per develop ([835764e](https://gitlab.pepita.io/getrix/mls-site/commit/835764e))


### Features

* **indomio:** aggiunte configurazioni per indomio pro ([274a813](https://gitlab.pepita.io/getrix/mls-site/commit/274a813))



<a name="4.3.0"></a>
# [4.3.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.2.1...v4.3.0) (2019-03-11)


### Bug Fixes

* gestita retrocompatibilità valori delle variabili d'ambiente ([3b47685](https://gitlab.pepita.io/getrix/mls-site/commit/3b47685))
* **impostazioni:** corretto sbiancamento campi ([c9f98c2](https://gitlab.pepita.io/getrix/mls-site/commit/c9f98c2))



<a name="4.2.1"></a>
## [4.2.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.2.0...v4.2.1) (2019-03-05)


### Bug Fixes

* corretto path template ([5739e23](https://gitlab.pepita.io/getrix/mls-site/commit/5739e23))



<a name="4.2.0"></a>
# [4.2.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.1.2...v4.2.0) (2019-03-05)



<a name="4.1.2"></a>
## [4.1.2](https://gitlab.pepita.io/getrix/mls-site/compare/v4.1.1...v4.1.2) (2019-02-28)


### Bug Fixes

* **acquisizione:** dopo il cambiamento del filtro provincia e dopo la rimozione dei filtri ripristina come filtro comune quello della sede agenzia. ([98e9410](https://gitlab.pepita.io/getrix/mls-site/commit/98e9410))
* **property-finder:** corregge paginazione ([353848d](https://gitlab.pepita.io/getrix/mls-site/commit/353848d))



<a name="4.1.1"></a>
## [4.1.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.1.0...v4.1.1) (2019-02-25)


### Bug Fixes

* **acquisizione:** permette di filtrare per qualsiasi comune di una determinata provincia ([8d2623f](https://gitlab.pepita.io/getrix/mls-site/commit/8d2623f))
* **agenzie:** rimossi eventuali tag html inseriti in fase di salvataggio agenzia ([749cf90](https://gitlab.pepita.io/getrix/mls-site/commit/749cf90))



<a name="4.1.0"></a>
# [4.1.0](https://gitlab.pepita.io/getrix/mls-site/compare/v4.0.2...v4.1.0) (2019-02-22)


### Features

* **chat-bot:** aggiunge la funzionalita chat bot quando il responsabile dell agenzia e abilitato ([73925ca](https://gitlab.pepita.io/getrix/mls-site/commit/73925ca))



<a name="4.0.2"></a>
## [4.0.2](https://gitlab.pepita.io/getrix/mls-site/compare/v4.0.1...v4.0.2) (2019-02-21)


### Bug Fixes

* **acquisizione:** filtro comune di default (sede agenzia), fix filtro riscontri ([15d7eb2](https://gitlab.pepita.io/getrix/mls-site/commit/15d7eb2))
* **amministrazione:** aggiunti controlli su codice SDI ([463694e](https://gitlab.pepita.io/getrix/mls-site/commit/463694e))
* **gitlab-ci:** aggiunto clear cache dopo composer install ([9547bb7](https://gitlab.pepita.io/getrix/mls-site/commit/9547bb7))



<a name="4.0.1"></a>
## [4.0.1](https://gitlab.pepita.io/getrix/mls-site/compare/v4.0.0...v4.0.1) (2019-02-20)


### Bug Fixes

* corrette configurazioni ekbl_stats ([8072f42](https://gitlab.pepita.io/getrix/mls-site/commit/8072f42))
* **session:** corretta configurazione per utilizzo del corretto user provider ([7ca4f92](https://gitlab.pepita.io/getrix/mls-site/commit/7ca4f92))



<a name="4.0.0"></a>
# [4.0.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.42.0...v4.0.0) (2019-02-19)


### Bug Fixes

* aggiunto composer json ai file dell'artefatto ([68b11af](https://gitlab.pepita.io/getrix/mls-site/commit/68b11af))
* corrette variabili per ci ([df9379f](https://gitlab.pepita.io/getrix/mls-site/commit/df9379f))
* corretti parametri app.asset_version ([93d7017](https://gitlab.pepita.io/getrix/mls-site/commit/93d7017))
* **application:** minor fix configuration ([e8dc9cb](https://gitlab.pepita.io/getrix/mls-site/commit/e8dc9cb))
* **application:** remove deprecated method annotation from UserController ([8a46165](https://gitlab.pepita.io/getrix/mls-site/commit/8a46165))
* **application:** removed all method annotations ([7a5bb85](https://gitlab.pepita.io/getrix/mls-site/commit/7a5bb85))
* **config:** asset version da file json ([bea0819](https://gitlab.pepita.io/getrix/mls-site/commit/bea0819))



<a name="3.42.0"></a>
# [3.42.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.41.0...v3.42.0) (2019-02-01)


### Features

* **agenzie:** Aggiunte descrizione multilingua all'agenzia. ([635fbed](https://gitlab.pepita.io/getrix/mls-site/commit/635fbed))



<a name="3.41.0"></a>
# [3.41.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.40.3...v3.41.0) (2019-01-29)


### Bug Fixes

* **step-4:** aggiunti metodi rest per recupero zone portali ([932646c](https://gitlab.pepita.io/getrix/mls-site/commit/932646c))



<a name="3.40.3"></a>
## [3.40.3](https://gitlab.pepita.io/getrix/mls-site/compare/v3.40.2...v3.40.3) (2019-01-25)


### Bug Fixes

* **property-finder:** corretto allineamento Cerca + rimosso filtro Aste ([53c61a0](https://gitlab.pepita.io/getrix/mls-site/commit/53c61a0))



<a name="3.40.2"></a>
## [3.40.2](https://gitlab.pepita.io/getrix/mls-site/compare/v3.40.1...v3.40.2) (2019-01-23)


### Bug Fixes

* abilitata cache twig in sviluppo per immobiliare pro ([e271d81](https://gitlab.pepita.io/getrix/mls-site/commit/e271d81))
* attivata cache di twig in sviluppo ([5e73b18](https://gitlab.pepita.io/getrix/mls-site/commit/5e73b18))
* **profilo-utente:** correzioni grafiche immagine del profilo ([ac33745](https://gitlab.pepita.io/getrix/mls-site/commit/ac33745))



<a name="3.40.1"></a>
## [3.40.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.40.0...v3.40.1) (2019-01-17)


### Bug Fixes

* risolve comportamento anomalo delle linkedSelectGroup in presenza di una sola option. Ripristina comportamento click su checkbox ([0ece6c6](https://gitlab.pepita.io/getrix/mls-site/commit/0ece6c6))



<a name="3.40.0"></a>
# [3.40.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.39.0...v3.40.0) (2019-01-17)


### Bug Fixes

* introduce componente linked select group ([9a386dc](https://gitlab.pepita.io/getrix/mls-site/commit/9a386dc))
* **multinvio:** disabilita i bottoni Disattiva e Annulla durante il salvataggio delle impostazioni ([a1d08b8](https://gitlab.pepita.io/getrix/mls-site/commit/a1d08b8))



<a name="3.39.0"></a>
# [3.39.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.38.0...v3.39.0) (2019-01-14)


### Features

* drop legacy browsers ([a170159](https://gitlab.pepita.io/getrix/mls-site/commit/a170159))
* **multinvio:** aggiunta partita iva nella notifica al portale attivato ([f2daa0b](https://gitlab.pepita.io/getrix/mls-site/commit/f2daa0b))
* introduce componente react per la gestione della query string nel multinvio e in acquisizione privati ([75e6af7](https://gitlab.pepita.io/getrix/mls-site/commit/75e6af7))



<a name="3.38.0"></a>
# [3.38.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.37.0...v3.38.0) (2019-01-07)


### Bug Fixes

* **multinvio-progetti:** fix minori ([2379b96](https://gitlab.pepita.io/getrix/mls-site/commit/2379b96))
* aggiunta use mancante ([4bd5d7c](https://gitlab.pepita.io/getrix/mls-site/commit/4bd5d7c))
* corretta formattazione url dopo pubblicazione ([e2fb536](https://gitlab.pepita.io/getrix/mls-site/commit/e2fb536))



<a name="3.37.0"></a>
# [3.37.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.36.4...v3.37.0) (2018-12-19)


### Bug Fixes

* **acquisizione:** evita che l antemprima della nota venga tagliata al centro di una parola ([d8c30c1](https://gitlab.pepita.io/getrix/mls-site/commit/d8c30c1))
* corretta lunghezza massima campo partita iva ([7635031](https://gitlab.pepita.io/getrix/mls-site/commit/7635031))


### Features

* **composer:** aggiunto composer.lock e unificati i composer.json ([89d6754](https://gitlab.pepita.io/getrix/mls-site/commit/89d6754))



<a name="3.36.4"></a>
## [3.36.4](https://gitlab.pepita.io/getrix/mls-site/compare/v3.36.3...v3.36.4) (2018-12-17)


### Bug Fixes

* **acquisizione:** ellipsis, barra help ([b49dc14](https://gitlab.pepita.io/getrix/mls-site/commit/b49dc14))
* **acquisizione:** migliorata visualizzazione pulsante reset filtri, abilitato tab nei range input ([30e6811](https://gitlab.pepita.io/getrix/mls-site/commit/30e6811))
* **multinvio-annunci:** evita che al caricamento della pagina il filtro localita non si autoselezioni ([1033cdf](https://gitlab.pepita.io/getrix/mls-site/commit/1033cdf))
* corrette dimensioni colonna in media query ([c931db4](https://gitlab.pepita.io/getrix/mls-site/commit/c931db4))



<a name="3.36.3"></a>
## [3.36.3](https://gitlab.pepita.io/getrix/mls-site/compare/v3.36.2...v3.36.3) (2018-12-13)


### Bug Fixes

* sostituita apertuna nuovo tab con redirect ([9f15d07](https://gitlab.pepita.io/getrix/mls-site/commit/9f15d07))



<a name="3.36.2"></a>
## [3.36.2](https://gitlab.pepita.io/getrix/mls-site/compare/v3.36.1...v3.36.2) (2018-12-13)


### Bug Fixes

* **acquisizione:** ad importazione avvenuta viene simulato invio form per aprire il tab dell annuncio importato ([4e56d1a](https://gitlab.pepita.io/getrix/mls-site/commit/4e56d1a))
* **acquisizione:** corregge messaggio confirm importazione, fix e trim nota ([29d6ea1](https://gitlab.pepita.io/getrix/mls-site/commit/29d6ea1))
* **acquisizione:** corregge problema su nota esito ([b8af5a6](https://gitlab.pepita.io/getrix/mls-site/commit/b8af5a6))
* corretto warning su close ([a7b7a75](https://gitlab.pepita.io/getrix/mls-site/commit/a7b7a75))



<a name="3.36.1"></a>
## [3.36.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.36.0...v3.36.1) (2018-12-12)


### Bug Fixes

* ripristinata definizione servizio ([53ee977](https://gitlab.pepita.io/getrix/mls-site/commit/53ee977))



<a name="3.36.0"></a>
# [3.36.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.35.2...v3.36.0) (2018-12-12)


### Bug Fixes

* aggiornata url satis per staging e produzione ([2ccc35b](https://gitlab.pepita.io/getrix/mls-site/commit/2ccc35b))
* aggiunte configurazioni rpc services per acquisizione su staging e prod ([a12fde0](https://gitlab.pepita.io/getrix/mls-site/commit/a12fde0))
* **acquisizione:** stile prezzo ([37104fe](https://gitlab.pepita.io/getrix/mls-site/commit/37104fe))
* **acquisizione-privati:** cursore label,ottimizzazione rotta ricerca signolo annuncio, fix rimuovi filtri ([dc9cced](https://gitlab.pepita.io/getrix/mls-site/commit/dc9cced))
* **acquisizione-privati:** fix minori ([790d5f2](https://gitlab.pepita.io/getrix/mls-site/commit/790d5f2))
* corretti title locali ([8195f57](https://gitlab.pepita.io/getrix/mls-site/commit/8195f57))



<a name="3.35.2"></a>
## [3.35.2](https://gitlab.pepita.io/getrix/mls-site/compare/v3.35.1...v3.35.2) (2018-11-28)


### Bug Fixes

* **impostazioni:** corretta localizzazione San Marino ([67ddc04](https://gitlab.pepita.io/getrix/mls-site/commit/67ddc04))
* aggiornata versione getrix-common ([ec87512](https://gitlab.pepita.io/getrix/mls-site/commit/ec87512))



<a name="3.35.1"></a>
## [3.35.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.35.0...v3.35.1) (2018-11-21)


### Bug Fixes

* **settings:** spostato campo email pec in sezione fatturazione ([791a677](https://gitlab.pepita.io/getrix/mls-site/commit/791a677))



<a name="3.35.0"></a>
# [3.35.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.34.2...v3.35.0) (2018-11-20)



<a name="3.34.2"></a>
## [3.34.2](https://gitlab.pepita.io/getrix/mls-site/compare/v3.34.1...v3.34.2) (2018-10-29)


### Bug Fixes

* aggiornato full-search-form ([1f1e588](https://gitlab.pepita.io/getrix/mls-site/commit/1f1e588))
* errore js in caso di eccezione nel salvataggio del bookmark ([0241248](https://gitlab.pepita.io/getrix/mls-site/commit/0241248))



<a name="3.34.1"></a>
## [3.34.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.34.0...v3.34.1) (2018-10-16)


### Bug Fixes

* **portali:** aggiunto username o codice relazionale (se presenti) nella mail inviata ai portali in fase di attivazione ([5f1bdb1](https://gitlab.pepita.io/getrix/mls-site/commit/5f1bdb1))



<a name="3.34.0"></a>
# [3.34.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.33.2...v3.34.0) (2018-10-16)


### Bug Fixes

* **impostazioni-generale:** trim dei valori inseriti nel form impostazioni/generale ([e6665d5](https://gitlab.pepita.io/getrix/mls-site/commit/e6665d5))
* aggiunto controlla esistenza dato ([adeaf6c](https://gitlab.pepita.io/getrix/mls-site/commit/adeaf6c))
* Corrette cartine lazio piemonte e lombardia ([f6d56c1](https://gitlab.pepita.io/getrix/mls-site/commit/f6d56c1))



<a name="3.33.2"></a>
## [3.33.2](https://gitlab.pepita.io/getrix/mls-site/compare/v3.33.1...v3.33.2) (2018-10-09)



<a name="3.33.1"></a>
## [3.33.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.33.0...v3.33.1) (2018-10-08)


### Bug Fixes

* Corrette cartine lazio piemonte e lombardia ([519f5ba](https://gitlab.pepita.io/getrix/mls-site/commit/519f5ba))



<a name="3.33.0"></a>
# [3.33.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.32.0...v3.33.0) (2018-10-08)



<a name="3.32.0"></a>
# [3.32.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.31.0...v3.32.0) (2018-10-03)



<a name="3.31.0"></a>
# [3.31.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.30.0...v3.31.0) (2018-09-28)


### Bug Fixes

* **portali:** impostata preselezione attiva di default ([fb39022](https://gitlab.pepita.io/getrix/mls-site/commit/fb39022))
* aggiunto controllo su presenza annunci ([4856121](https://gitlab.pepita.io/getrix/mls-site/commit/4856121))


### Features

* **multinvio:** aggiornato sdk ([584dde7](https://gitlab.pepita.io/getrix/mls-site/commit/584dde7))
* **multinvio:** aggiunte categorie portali ([969ca34](https://gitlab.pepita.io/getrix/mls-site/commit/969ca34))
* **multinvio:** implementate categorie pubblicazione portali attivi ([6ce8e24](https://gitlab.pepita.io/getrix/mls-site/commit/6ce8e24))



<a name="3.30.0"></a>
# [3.30.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.29.4...v3.30.0) (2018-09-25)


### Features

* **agency:** aggiunta creazione nuova agenzia via rest ([941b232](https://gitlab.pepita.io/getrix/mls-site/commit/941b232))
* **agenzia:** aggiunto secret key ([f8647fe](https://gitlab.pepita.io/getrix/mls-site/commit/f8647fe))



<a name="3.29.4"></a>
## [3.29.4](https://gitlab.pepita.io/getrix/mls-site/compare/v3.29.3...v3.29.4) (2018-09-25)


### Bug Fixes

* corretta mail claudia ([098e982](https://gitlab.pepita.io/getrix/mls-site/commit/098e982))



<a name="3.29.3"></a>
## [3.29.3](https://gitlab.pepita.io/getrix/mls-site/compare/v3.29.2...v3.29.3) (2018-09-25)


### Bug Fixes

* Gestita eccezione in caso di mail già utilizzata ([9e0efd0](https://gitlab.pepita.io/getrix/mls-site/commit/9e0efd0))
* Gestita eccezione in caso di mail già utilizzata ([493cfc2](https://gitlab.pepita.io/getrix/mls-site/commit/493cfc2))



<a name="3.29.2"></a>
## [3.29.2](https://gitlab.pepita.io/getrix/mls-site/compare/v3.29.1...v3.29.2) (2018-09-19)


### Bug Fixes

* **dashboard:** corretto caso assenza stats ([e4a0bac](https://gitlab.pepita.io/getrix/mls-site/commit/e4a0bac))
* **multinvio:** aggiunta gestione caso di errore senza messaggio ([658c963](https://gitlab.pepita.io/getrix/mls-site/commit/658c963))
* corretta url business ([2ece49d](https://gitlab.pepita.io/getrix/mls-site/commit/2ece49d))


### Features

* **settings:** aggiunta gestione messagi ([0b66955](https://gitlab.pepita.io/getrix/mls-site/commit/0b66955))



<a name="3.29.1"></a>
## [3.29.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.29.0...v3.29.1) (2018-09-10)


### Bug Fixes

* **a2f:** aggiunti controlli nelle response in caso di errore ([d047c8e](https://gitlab.pepita.io/getrix/mls-site/commit/d047c8e))
* **box-visibilita:** corretto padding e dimensioni box ([8199ce8](https://gitlab.pepita.io/getrix/mls-site/commit/8199ce8))
* **ImmoPro:** box visibilità uguale ad Immobiliare ([be8b326](https://gitlab.pepita.io/getrix/mls-site/commit/be8b326))
* **multinvio-annunci:** il badge errori del portale scompare quando non ci sono piu annunci con errori. Rimossa mappatura dei messaggi di errore. Quando viene pubblicato il badge errore appare in tempo reale ([8e1b7ca](https://gitlab.pepita.io/getrix/mls-site/commit/8e1b7ca))
* **pro-visibilita:** nel banner agenzia in evidenza quando la url non e popolata allora di default il testo punta alla pagina agenzia su immobiliare.it ([0825589](https://gitlab.pepita.io/getrix/mls-site/commit/0825589))
* **propery-finder:** in assenza della mappa evita di inizializzarla lato client ([b4ee0c0](https://gitlab.pepita.io/getrix/mls-site/commit/b4ee0c0))
* **tos:** nel caso in cui si logga un agente evita che venga montato il componente js ([ac40d75](https://gitlab.pepita.io/getrix/mls-site/commit/ac40d75))


### Features

* **a2f:** aggiunto testo generico ([196b789](https://gitlab.pepita.io/getrix/mls-site/commit/196b789))



<a name="3.29.0"></a>
# [3.29.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.28.2...v3.29.0) (2018-09-07)


### Bug Fixes

* corretta url pro staging ([1e0fbc9](https://gitlab.pepita.io/getrix/mls-site/commit/1e0fbc9))
* **multinvio-annunci:** gestione link visibilita e link annucio del portale immobiliare durante la pubblicazione ([f494918](https://gitlab.pepita.io/getrix/mls-site/commit/f494918))
* **sentry-client:** risolta duplicazione eventi ([b1259db](https://gitlab.pepita.io/getrix/mls-site/commit/b1259db))
* url pro staging ([a032e8a](https://gitlab.pepita.io/getrix/mls-site/commit/a032e8a))



<a name="3.28.2"></a>
## [3.28.2](https://gitlab.pepita.io/getrix/mls-site/compare/v3.28.1...v3.28.2) (2018-09-04)



<a name="3.28.1"></a>
## [3.28.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.28.0...v3.28.1) (2018-08-31)


### Bug Fixes

* Aggiunta sezione per metriche ([6e4c503](https://gitlab.pepita.io/getrix/mls-site/commit/6e4c503))



<a name="3.28.0"></a>
# [3.28.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.27.3...v3.28.0) (2018-08-31)


### Bug Fixes

* aggiornata versione getrix-common ([84ffa1d](https://gitlab.pepita.io/getrix/mls-site/commit/84ffa1d))
* Aggiunta possibilità di gestire campo vuoto ([0c342e2](https://gitlab.pepita.io/getrix/mls-site/commit/0c342e2))



<a name="3.27.3"></a>
## [3.27.3](https://gitlab.pepita.io/getrix/mls-site/compare/v3.27.2...v3.27.3) (2018-08-24)


### Bug Fixes

* Aggiornata versione getrix-common ([36ba082](https://gitlab.pepita.io/getrix/mls-site/commit/36ba082))
* Aggiornata versione getrix-common ([3ace9d9](https://gitlab.pepita.io/getrix/mls-site/commit/3ace9d9))
* Rimosso orario da data ultimo invio ([14392be](https://gitlab.pepita.io/getrix/mls-site/commit/14392be))



<a name="3.27.2"></a>
## [3.27.2](https://gitlab.pepita.io/getrix/mls-site/compare/v3.27.1...v3.27.2) (2018-08-03)


### Bug Fixes

* **multinvio:** messaggio più specifico in caso di errore di geolocalizzazione ([4843397](https://gitlab.pepita.io/getrix/mls-site/commit/4843397))



<a name="3.27.1"></a>
## [3.27.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.27.0...v3.27.1) (2018-08-02)


### Bug Fixes

* **GDPR:** padding ([66047f4](https://gitlab.pepita.io/getrix/mls-site/commit/66047f4))



<a name="3.27.0"></a>
# [3.27.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.26.5...v3.27.0) (2018-08-02)


### Bug Fixes

* **check-status:** esclude pro dagli update tos ([1b19066](https://gitlab.pepita.io/getrix/mls-site/commit/1b19066))
* **contatto:** sostituiti i dati dell'anagrafica utente loggato ([3bb924d](https://gitlab.pepita.io/getrix/mls-site/commit/3bb924d))
* **prepend:** corretto ordine di verifica change password e update tos ([8f5c011](https://gitlab.pepita.io/getrix/mls-site/commit/8f5c011))
* **pro:** allineate rotte prodotti ([09cd9b3](https://gitlab.pepita.io/getrix/mls-site/commit/09cd9b3))
* **typo:** account diventa profilo ([a87112e](https://gitlab.pepita.io/getrix/mls-site/commit/a87112e))


### Features

* **change-status:** verifica e forza l'aggiornamento dei TOS ([cba02b8](https://gitlab.pepita.io/getrix/mls-site/commit/cba02b8))
* **gdpr:** grafica form  accetazione gdpr ([13528c4](https://gitlab.pepita.io/getrix/mls-site/commit/13528c4))



<a name="3.26.5"></a>
## [3.26.5](https://gitlab.pepita.io/getrix/mls-site/compare/v3.26.4...v3.26.5) (2018-07-18)


### Bug Fixes

* **multinvio:** sostituita email agenzia con id nella mail inviata ai portali in fase di attivazione ([3b3b761](https://gitlab.pepita.io/getrix/mls-site/commit/3b3b761))



<a name="3.26.4"></a>
## [3.26.4](https://gitlab.pepita.io/getrix/mls-site/compare/v3.26.3...v3.26.4) (2018-07-13)


### Bug Fixes

* Corretti messaggi d'errore e inizializzazione styled select ([049c116](https://gitlab.pepita.io/getrix/mls-site/commit/049c116))



<a name="3.26.3"></a>
## [3.26.3](https://gitlab.pepita.io/getrix/mls-site/compare/v3.26.2...v3.26.3) (2018-07-12)


### Bug Fixes

* aggiornata versione getrix-common ([0e5dad7](https://gitlab.pepita.io/getrix/mls-site/commit/0e5dad7))



<a name="3.26.2"></a>
## [3.26.2](https://gitlab.pepita.io/getrix/mls-site/compare/v3.26.1...v3.26.2) (2018-07-12)


### Bug Fixes

* Aggiornata versione getrix-common ([27abcb7](https://gitlab.pepita.io/getrix/mls-site/commit/27abcb7))
* Corretta visualizzazione valori filtri ([131669a](https://gitlab.pepita.io/getrix/mls-site/commit/131669a))



<a name="3.26.1"></a>
## [3.26.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.26.0...v3.26.1) (2018-07-12)


### Bug Fixes

* corretta url nella mail inviata in fase di autenticazione a 2 fattori ([0042bf3](https://gitlab.pepita.io/getrix/mls-site/commit/0042bf3))



<a name="3.26.0"></a>
# [3.26.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.25.4...v3.26.0) (2018-07-12)


### Bug Fixes

* **impostazioni-generale:** modificata grafica sezione telefono smart di impostazioni-generale e formattatti i numeri di telefono ([2872266](https://gitlab.pepita.io/getrix/mls-site/commit/2872266))
* **multinvio-annunci:** corretto recupero lista portali per i filtri ([b5f34ae](https://gitlab.pepita.io/getrix/mls-site/commit/b5f34ae))
* corrette rotte per mantenere retrocompatibilità ([ce176ad](https://gitlab.pepita.io/getrix/mls-site/commit/ce176ad))
* modifiche per split menu impostazioni amministrazione ([12812ff](https://gitlab.pepita.io/getrix/mls-site/commit/12812ff))
* rimosso refuso ([442bffc](https://gitlab.pepita.io/getrix/mls-site/commit/442bffc))



<a name="3.25.4"></a>
## [3.25.4](https://gitlab.pepita.io/getrix/mls-site/compare/v3.25.3...v3.25.4) (2018-07-10)


### Bug Fixes

* **multinvio-portali:** modifica label e testo delle modali di attivazione/modifica dei portali. Ripristinato bottone Disattiva per la modale di modifica di immobiliare.it ([394e171](https://gitlab.pepita.io/getrix/mls-site/commit/394e171))


### Features

* **gtx-react:** aggiunti polyfill Map e Set ([d518ab4](https://gitlab.pepita.io/getrix/mls-site/commit/d518ab4))
* **multinvio:** aggiunto sentry client ([d59c2fd](https://gitlab.pepita.io/getrix/mls-site/commit/d59c2fd))



<a name="3.25.3"></a>
## [3.25.3](https://gitlab.pepita.io/getrix/mls-site/compare/v3.25.2...v3.25.3) (2018-07-06)


### Bug Fixes

* **multinvio:** correzione spaziature ([2caf721](https://gitlab.pepita.io/getrix/mls-site/commit/2caf721))
* **multinvio-portali:** aggiunge responsabile portale in cc nella email richiesta attivazione. Invia email ai referenti del portale in caso di attivazione diretta ([ecab223](https://gitlab.pepita.io/getrix/mls-site/commit/ecab223))
* modificata referenza sdk ([7084bc3](https://gitlab.pepita.io/getrix/mls-site/commit/7084bc3))
* setta cookie quando viene inviata l email di espostazione attivata ai contatti del portale ([d3d5f5f](https://gitlab.pepita.io/getrix/mls-site/commit/d3d5f5f))
* **multinvio-portali:** effettua correzioni alla grafica e al contenuto della email inviata ai portali dopo l attivazione dell esportazione ([69d3574](https://gitlab.pepita.io/getrix/mls-site/commit/69d3574))



<a name="3.25.2"></a>
## [3.25.2](https://gitlab.pepita.io/getrix/mls-site/compare/v3.25.1...v3.25.2) (2018-07-03)


### Bug Fixes

* **dashboard:** elimitato riferimento alle statistiche degli annunci turistici ([209b1ef](https://gitlab.pepita.io/getrix/mls-site/commit/209b1ef))
* **polyfill:** Rimuove il doppio import di promise ([007a02c](https://gitlab.pepita.io/getrix/mls-site/commit/007a02c))


### Features

* **multisend:** Sposta il caricamento dei dati iniziali lato client ([33effcb](https://gitlab.pepita.io/getrix/mls-site/commit/33effcb))


### Performance Improvements

* **multisend:** Divide l'API initial-data in 3 chiamate ([65032b7](https://gitlab.pepita.io/getrix/mls-site/commit/65032b7))



<a name="3.25.1"></a>
## [3.25.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.25.0...v3.25.1) (2018-07-02)


### Bug Fixes

* Cambiato testo messaggio sms autenticazione 2 fattori ([f40fbca](https://gitlab.pepita.io/getrix/mls-site/commit/f40fbca))
* **impostazioni:** aggiunto nuovo ruolo utente: responsabile commercializzazione. Modificata label in sezione generale: ripeti email -> conferma email ([071a34c](https://gitlab.pepita.io/getrix/mls-site/commit/071a34c))
* **multinvio:** cambiato nome classe label preselezione ([b07b7f2](https://gitlab.pepita.io/getrix/mls-site/commit/b07b7f2))
* versione getrix-common aggiornata ([655054a](https://gitlab.pepita.io/getrix/mls-site/commit/655054a))
* **multinvio-portali:** modifica label preselezione dopo update impostazioni ([9b1d77a](https://gitlab.pepita.io/getrix/mls-site/commit/9b1d77a))
* **multisend-portali:** aggiunta alla card del portale la label preselezione attiva/non attiva ([db8f667](https://gitlab.pepita.io/getrix/mls-site/commit/db8f667))


### Features

* **billobard:** aggiunta gestione errore in fase di salvataggio ([a0bcab8](https://gitlab.pepita.io/getrix/mls-site/commit/a0bcab8))



<a name="3.25.0"></a>
# [3.25.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.24.1...v3.25.0) (2018-06-27)



<a name="3.24.1"></a>
## [3.24.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.24.0...v3.24.1) (2018-06-27)


### Bug Fixes

* **amministrazione:** pulsante help ([c62f27f](https://gitlab.pepita.io/getrix/mls-site/commit/c62f27f))
* **billboard:** corretto download pdf su safari ([ada3591](https://gitlab.pepita.io/getrix/mls-site/commit/ada3591))
* **multinvio-portali:** aggiunta attivazione/disattivazione portale tramite modale di richiesta servizio ([b400f3d](https://gitlab.pepita.io/getrix/mls-site/commit/b400f3d))



<a name="3.24.0"></a>
# [3.24.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.23.0...v3.24.0) (2018-06-26)


### Bug Fixes

* cambiata opzione generazione source-map per non avere i commenti relativi nel codice ([41a1382](https://gitlab.pepita.io/getrix/mls-site/commit/41a1382))
* **cartellone:** introdotto polyfill per IE11 ([6bc4d18](https://gitlab.pepita.io/getrix/mls-site/commit/6bc4d18))
* **multinvio-annunci:** aggiunge gestione eccezione di server non raggiungibile per la chiamata che recupera gli spazi pubblicitari rimanenti ([e7c3180](https://gitlab.pepita.io/getrix/mls-site/commit/e7c3180))
* **multisend-portali:** Quando il portale 207 (sito web getrix) attivo il portale 32 (sito web immobiliare) viene rimosso dalla lista ([c87fdae](https://gitlab.pepita.io/getrix/mls-site/commit/c87fdae))
* **sentry-js:** Corregge la gestione delle sourcemaps ([fd0d307](https://gitlab.pepita.io/getrix/mls-site/commit/fd0d307))



<a name="3.23.0"></a>
# [3.23.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.22.8...v3.23.0) (2018-06-21)


### Bug Fixes

* **multinvio-portali:** aggiunti log per le azioni di attivazione/disattivazione portale ([8d8c76e](https://gitlab.pepita.io/getrix/mls-site/commit/8d8c76e))
* **sentry-js:** Sposta l'import di Sentry in cima al main ([07fee36](https://gitlab.pepita.io/getrix/mls-site/commit/07fee36))
* Aggiunto set della versione degli assets in fase di install o update di composer ([b127f42](https://gitlab.pepita.io/getrix/mls-site/commit/b127f42))
* revertato aggiornamento versione full-search-form per rinvio rilascio geografia ([f6acbba](https://gitlab.pepita.io/getrix/mls-site/commit/f6acbba))


### Features

* **billboard:** Aggiunge sentry lato client :rocket: ([99aa25a](https://gitlab.pepita.io/getrix/mls-site/commit/99aa25a))



<a name="3.22.8"></a>
## [3.22.8](https://gitlab.pepita.io/getrix/mls-site/compare/v3.22.7...v3.22.8) (2018-06-19)


### Bug Fixes

* risolve bug del componente pager ([94e4565](https://gitlab.pepita.io/getrix/mls-site/commit/94e4565))



<a name="3.22.7"></a>
## [3.22.7](https://gitlab.pepita.io/getrix/mls-site/compare/v3.22.6...v3.22.7) (2018-06-18)


### Bug Fixes

* **multinvio-portali:** patch per risolvere il problema di visualizzazione dello spinner su windows all interno della modale riordina portali ([8d2922c](https://gitlab.pepita.io/getrix/mls-site/commit/8d2922c))
* **multinvio-portali:** risolve trasparenza elementi draggati all interno della modale ordinamento portali su windows ([61ce5ed](https://gitlab.pepita.io/getrix/mls-site/commit/61ce5ed))



<a name="3.22.6"></a>
## [3.22.6](https://gitlab.pepita.io/getrix/mls-site/compare/v3.22.5...v3.22.6) (2018-06-18)


### Bug Fixes

* **multinvio:** aggiunto pulsante per l'ordinamento dei portali nella pagina gestione esportazioni ([0d9a619](https://gitlab.pepita.io/getrix/mls-site/commit/0d9a619))
* **multinvio-annunci:** dopo l azione rimuovi filtri la paginazione viene resettata ([d17d855](https://gitlab.pepita.io/getrix/mls-site/commit/d17d855))
* **spinner:** corretto comportamento spinner all'interno della modale riordina portali' ([aa3bc7d](https://gitlab.pepita.io/getrix/mls-site/commit/aa3bc7d))



<a name="3.22.5"></a>
## [3.22.5](https://gitlab.pepita.io/getrix/mls-site/compare/v3.22.4...v3.22.5) (2018-06-14)


### Bug Fixes

* **registrazione:** corretta label check privacy getrix ([dcbbbb7](https://gitlab.pepita.io/getrix/mls-site/commit/dcbbbb7))
* Aggiornata versione di getrix-common ([03e9f84](https://gitlab.pepita.io/getrix/mls-site/commit/03e9f84))



<a name="3.22.4"></a>
## [3.22.4](https://gitlab.pepita.io/getrix/mls-site/compare/v3.22.3...v3.22.4) (2018-06-12)


### Bug Fixes

* Aggiornamento versione getrix-common ([42d9721](https://gitlab.pepita.io/getrix/mls-site/commit/42d9721))
* **multinvio:** ottimizzati i testi della modale attivazione portale ([ec2a956](https://gitlab.pepita.io/getrix/mls-site/commit/ec2a956))
* **multinvio:** Portali diventa Esportazioni ([7ab773f](https://gitlab.pepita.io/getrix/mls-site/commit/7ab773f))
* **multinvio:** rename portale in sito/esportazione ([b215046](https://gitlab.pepita.io/getrix/mls-site/commit/b215046))
* **registrazione:** fix posizione checkbox su pro ([5147267](https://gitlab.pepita.io/getrix/mls-site/commit/5147267))
* **registrazione Pro:** adeguamento testo per GDPR ([79688f7](https://gitlab.pepita.io/getrix/mls-site/commit/79688f7))
* rename title select da portali a siti ([e9bec4d](https://gitlab.pepita.io/getrix/mls-site/commit/e9bec4d))



<a name="3.22.3"></a>
## [3.22.3](https://gitlab.pepita.io/getrix/mls-site/compare/v3.22.2...v3.22.3) (2018-06-07)


### Bug Fixes

* **multinvio:** corretti bug modali ([612c403](https://gitlab.pepita.io/getrix/mls-site/commit/612c403))
* **multinvio-annunci:** corregge perdita ordinamento al cambio di pagina ([1963586](https://gitlab.pepita.io/getrix/mls-site/commit/1963586))
* aggiornata versione getrix-common ([021448c](https://gitlab.pepita.io/getrix/mls-site/commit/021448c))



<a name="3.22.2"></a>
## [3.22.2](https://gitlab.pepita.io/getrix/mls-site/compare/v3.22.1...v3.22.2) (2018-06-07)


### Bug Fixes

* Corretto nome metodo ([3f29d66](https://gitlab.pepita.io/getrix/mls-site/commit/3f29d66))



<a name="3.22.1"></a>
## [3.22.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.22.0...v3.22.1) (2018-06-07)



<a name="3.22.0"></a>
# [3.22.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.21.1...v3.22.0) (2018-06-07)


### Bug Fixes

* aggiornata versione getrix-common ([d8036ec](https://gitlab.pepita.io/getrix/mls-site/commit/d8036ec))
* Aggiornata versione getrix-common ([8d98a6f](https://gitlab.pepita.io/getrix/mls-site/commit/8d98a6f))
* fissata versione ekbl-stats per dev ([5118ae4](https://gitlab.pepita.io/getrix/mls-site/commit/5118ae4))
* **multinvio:** impostata larghezza celle per quando i portali attivi sono pochi ([3acdff4](https://gitlab.pepita.io/getrix/mls-site/commit/3acdff4))
* revert parameters_dev.yml ([61065f0](https://gitlab.pepita.io/getrix/mls-site/commit/61065f0))
* **multinvio:** aggiunta factory mancante + corretto portale Nan ([2dcbef4](https://gitlab.pepita.io/getrix/mls-site/commit/2dcbef4))
* **multinvio:** corretta visualizzazione pulsanti Attiva/Disattiva su tablet/mobile ([1409d97](https://gitlab.pepita.io/getrix/mls-site/commit/1409d97))
* **multinvio:** corretto aggiornamento spazi inviati ([62f75df](https://gitlab.pepita.io/getrix/mls-site/commit/62f75df))
* **multinvio:** corretto comportamento popover ([309b665](https://gitlab.pepita.io/getrix/mls-site/commit/309b665))
* **multinvio:** corretto link a branch di bundles ([c092898](https://gitlab.pepita.io/getrix/mls-site/commit/c092898))
* **multinvio:** Corretto messaggio modale zone ([e93c23d](https://gitlab.pepita.io/getrix/mls-site/commit/e93c23d))
* **multinvio:** fix minori ([bcbb4b3](https://gitlab.pepita.io/getrix/mls-site/commit/bcbb4b3))
* **multinvio:** fix navigazione pagina portali, aggiunto testo descrittivo per il codice relazionale ([1c4c0ff](https://gitlab.pepita.io/getrix/mls-site/commit/1c4c0ff))
* **multinvio:** piccole correzioni grafiche ([e7cea06](https://gitlab.pepita.io/getrix/mls-site/commit/e7cea06))
* **multinvio-annunci:** migliorata formattazione delle informazioni dell annuncio nel listing ([32ed6ee](https://gitlab.pepita.io/getrix/mls-site/commit/32ed6ee))
* **multinvio-annunci:** setta eventuali badge errori contestualmente alla pubblicazione bulk ([e18986e](https://gitlab.pepita.io/getrix/mls-site/commit/e18986e))
* **multinvio-portali:** fix durante lo spostamento del primo portale della lista ([7a079a6](https://gitlab.pepita.io/getrix/mls-site/commit/7a079a6))
* **multinvio-portali:** nascosto btn disattiva per Immobiliare ([41815e3](https://gitlab.pepita.io/getrix/mls-site/commit/41815e3))


### Features

* **multinvio:** aggiornate dipendenze sdk e getrix-bundles ([135a989](https://gitlab.pepita.io/getrix/mls-site/commit/135a989))
* **multinvio:** aggiunta notice portale disattivato ([4f2c8ba](https://gitlab.pepita.io/getrix/mls-site/commit/4f2c8ba))
* **multinvio:** aggiunto controllo su data fine contratto ([012944f](https://gitlab.pepita.io/getrix/mls-site/commit/012944f))
* **multinvio:** implementato sdk ([51664de](https://gitlab.pepita.io/getrix/mls-site/commit/51664de))
* **multinvio:** lista di tutti i portali + modale attivazione Immobiliare ([5e902fe](https://gitlab.pepita.io/getrix/mls-site/commit/5e902fe))
* **multinvio-portale:** aggiunta action Disattiva ([b4a2fe2](https://gitlab.pepita.io/getrix/mls-site/commit/b4a2fe2))
* **multinvio-portali:** data fine contratto italiana + fix datepicker position ([1b0c89e](https://gitlab.pepita.io/getrix/mls-site/commit/1b0c89e))
* **multinvio-portali:** info portale da popover a modale ([19afa12](https://gitlab.pepita.io/getrix/mls-site/commit/19afa12))
* **multinvio-portali:** modificata modale configurazione portale ([c3777e0](https://gitlab.pepita.io/getrix/mls-site/commit/c3777e0))



<a name="3.21.1"></a>
## [3.21.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.21.0...v3.21.1) (2018-05-24)


### Bug Fixes

* **billboard:** hover venduto e affittato ([ac01c06](https://gitlab.pepita.io/getrix/mls-site/commit/ac01c06))
* **billboard-description:** corrette dimensioni toolbar modale descrizione in mobile ([578cbb2](https://gitlab.pepita.io/getrix/mls-site/commit/578cbb2))
* **multinvio-annunci:** miglioramento pattern ed eventi delle azioni bulk sui portali ([90b5ebd](https://gitlab.pepita.io/getrix/mls-site/commit/90b5ebd))
* fissata versione ekbl-stats per dev ([b481889](https://gitlab.pepita.io/getrix/mls-site/commit/b481889))
* **Settings:** Modificate label titolo e descrizione per il flag 'comunicazioni' ([31635db](https://gitlab.pepita.io/getrix/mls-site/commit/31635db))


### Features

* **billboard:** aggiunte opzioni email e sito ([0ad8f8d](https://gitlab.pepita.io/getrix/mls-site/commit/0ad8f8d))
* **billboard:** aggiunto reset descrizione ([8d1940a](https://gitlab.pepita.io/getrix/mls-site/commit/8d1940a))
* **billboard:** diversa posizione pulsante su mobile ([c04bf54](https://gitlab.pepita.io/getrix/mls-site/commit/c04bf54))
* **billboard:** se layout nasconde il campo email, viene nascosta la relativa checkbox ([0f12461](https://gitlab.pepita.io/getrix/mls-site/commit/0f12461))



<a name="3.21.0"></a>
# [3.21.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.20.2...v3.21.0) (2018-05-18)


### Bug Fixes

* **multinvio-annunci:** fix filtro portali e stato invio, rimossi decimali dal prezzo ([8fb014a](https://gitlab.pepita.io/getrix/mls-site/commit/8fb014a))



<a name="3.20.2"></a>
## [3.20.2](https://gitlab.pepita.io/getrix/mls-site/compare/v3.20.1...v3.20.2) (2018-05-16)


### Bug Fixes

* **billboard:** aggiunto controllo parametro referer ([427b7c0](https://gitlab.pepita.io/getrix/mls-site/commit/427b7c0))
* **multinvio-annunci:** abilitato filtro contratto e fix filtri range numerici ([470fb99](https://gitlab.pepita.io/getrix/mls-site/commit/470fb99))
* **multinvio-annunci:** fix filtro contratto ([b3d53da](https://gitlab.pepita.io/getrix/mls-site/commit/b3d53da))
* **multinvio-annunci:** modifica valori contratto affitto/vendita ([0a6c0be](https://gitlab.pepita.io/getrix/mls-site/commit/0a6c0be))


### Features

* **billboard:** host preso da parametro ([cca8c9a](https://gitlab.pepita.io/getrix/mls-site/commit/cca8c9a))



<a name="3.20.1"></a>
## [3.20.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.20.0...v3.20.1) (2018-05-15)



<a name="3.20.0"></a>
# [3.20.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.19.1...v3.20.0) (2018-05-15)


### Bug Fixes

* **multinvio:** alcune correzioni grafiche nella sezione annunci e portali ([a313481](https://gitlab.pepita.io/getrix/mls-site/commit/a313481))
* **multinvio:** allineamenti pulsanti, background testata, icone pulsanti ([0de7d17](https://gitlab.pepita.io/getrix/mls-site/commit/0de7d17))
* **multinvio:** Corretta associazione formatter filtri ricerca ([8685d74](https://gitlab.pepita.io/getrix/mls-site/commit/8685d74))
* **multinvio:** Corretta formattazione informazioni badge ricercabile ([f14ee17](https://gitlab.pepita.io/getrix/mls-site/commit/f14ee17))
* **multinvio:** Corretta inizializzazione paginazione ([e63f995](https://gitlab.pepita.io/getrix/mls-site/commit/e63f995))
* **multinvio:** Corretta stycky header ([f84fa27](https://gitlab.pepita.io/getrix/mls-site/commit/f84fa27))
* **multinvio:** Corrette azioni successive a chiamata bulk ([b3d8c70](https://gitlab.pepita.io/getrix/mls-site/commit/b3d8c70))
* **multinvio:** corrette procedure di pubblicazione e notifica su immobiliare ([afa8a4d](https://gitlab.pepita.io/getrix/mls-site/commit/afa8a4d))
* **multinvio:** fix grafico modale settaggio zone e aggiornato getrix-common ([ce96a09](https://gitlab.pepita.io/getrix/mls-site/commit/ce96a09))
* **multinvio:** nascosti spin degli input number per i browser webkit ([4603bc0](https://gitlab.pepita.io/getrix/mls-site/commit/4603bc0))
* **multinvio-annunci:** corretta logica badge zone dopo chiamata per ottenere gli annunci senza zone ([f813d8f](https://gitlab.pepita.io/getrix/mls-site/commit/f813d8f))
* **multinvio-annunci:** fix alla modale annunci senza zone, ripristinati annunci automatici, fix badge portale, fix posizione popover, fix ordinamento ([f47ad5b](https://gitlab.pepita.io/getrix/mls-site/commit/f47ad5b))
* **multinvio-annunci:** modale annunci senza zone: gestione offset paginazione dopo la selezione di una zona ([f53c127](https://gitlab.pepita.io/getrix/mls-site/commit/f53c127))
* **multinvio-annunci:** ottimizzazione della pubblicazione bulk, mapping errori backlink, gestione eccezioni in pubblicazione bulk, aggiunto controllo eccezioni mancanti ([82b41f5](https://gitlab.pepita.io/getrix/mls-site/commit/82b41f5))
* **multinvio-annunci:** rimossi i portali con invio automatico dai portali attivi, cambiata logica badge ricercabile, aggiunto badge errore su azioni bulk ([04a157c](https://gitlab.pepita.io/getrix/mls-site/commit/04a157c))
* **multinvio-portali:** corretto refuso che non permetteva di visualizzare i loghi dei portali associati ([7057c69](https://gitlab.pepita.io/getrix/mls-site/commit/7057c69))
* **multisend:** max-height modal-body ([b43c099](https://gitlab.pepita.io/getrix/mls-site/commit/b43c099))
* **multisend:** numero portali listato e elenco portali ([25d8d7c](https://gitlab.pepita.io/getrix/mls-site/commit/25d8d7c))
* aggiornata versione getrix-common per fix logo immobiliare pro ([4aa1ff4](https://gitlab.pepita.io/getrix/mls-site/commit/4aa1ff4))
* **portali:** Corretto forma informazioni portali ([08a9ed7](https://gitlab.pepita.io/getrix/mls-site/commit/08a9ed7))
* **portali:** Rimosso pulsante informazioni, aggiunte info in pagina ([74ce42b](https://gitlab.pepita.io/getrix/mls-site/commit/74ce42b))
* aggiornamento versione getrix-common ([9ed0fd4](https://gitlab.pepita.io/getrix/mls-site/commit/9ed0fd4))
* **PropertiesWithoutZones:** Added pagination ([2cee1e9](https://gitlab.pepita.io/getrix/mls-site/commit/2cee1e9))
* aggiornata versione getrix-common ([1f1f619](https://gitlab.pepita.io/getrix/mls-site/commit/1f1f619))
* Corregge il posizionamento del Popover quando manca spazio ([9742bde](https://gitlab.pepita.io/getrix/mls-site/commit/9742bde))
* decommentate costanti necessarie per il nuovo multinvio ([c71d165](https://gitlab.pepita.io/getrix/mls-site/commit/c71d165))
* Modificata versione soa/sdk ([81c58dd](https://gitlab.pepita.io/getrix/mls-site/commit/81c58dd))


### Features

* **StateMachine:** A component to manage complex state ([c654256](https://gitlab.pepita.io/getrix/mls-site/commit/c654256))



<a name="3.19.1"></a>
## [3.19.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.19.0...v3.19.1) (2018-05-08)


### Bug Fixes

* Aggiornamento versione full-search-form per correzione cartine ([3b89ff9](https://gitlab.pepita.io/getrix/mls-site/commit/3b89ff9))



<a name="3.19.0"></a>
# [3.19.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.18.1...v3.19.0) (2018-05-08)



<a name="3.18.1"></a>
## [3.18.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.18.0...v3.18.1) (2018-05-07)


### Bug Fixes

* **billboard:** corretto hover su descrizione ([5906125](https://gitlab.pepita.io/getrix/mls-site/commit/5906125))
* **billboard:** font-size esente da certificazione ([f3f7fa7](https://gitlab.pepita.io/getrix/mls-site/commit/f3f7fa7))
* **billboard:** lo status Venduto/Affittato finiva sotto le 3 foto ([ced0f4f](https://gitlab.pepita.io/getrix/mls-site/commit/ced0f4f))
* **composer:** update ([5e19590](https://gitlab.pepita.io/getrix/mls-site/commit/5e19590))
* **iframe:** Corregge il flash degli stili non caricati ([0ddb38d](https://gitlab.pepita.io/getrix/mls-site/commit/0ddb38d))
* **ios:** corretta visualizzazione cartellone tre foto verticale ([3bca486](https://gitlab.pepita.io/getrix/mls-site/commit/3bca486))


### Features

* **billboard:** adottato tooltip delle foto anche per la descrizione ([c423aa2](https://gitlab.pepita.io/getrix/mls-site/commit/c423aa2))



<a name="3.18.0"></a>
# [3.18.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.17.17...v3.18.0) (2018-05-02)


### Bug Fixes

* aggiornato react-dom e corretto selettore classe epi ([e8b115f](https://gitlab.pepita.io/getrix/mls-site/commit/e8b115f))
* Aggiunge il button secondary ([6da5688](https://gitlab.pepita.io/getrix/mls-site/commit/6da5688))
* aggiunto polyfill Object assign ([34f42fe](https://gitlab.pepita.io/getrix/mls-site/commit/34f42fe))
* Aggiunto qa/twig-extensions al composer.json di staging e produzione ([87e6d9b](https://gitlab.pepita.io/getrix/mls-site/commit/87e6d9b))
* cambio logica match annuncio vs portale ([318445f](https://gitlab.pepita.io/getrix/mls-site/commit/318445f))
* className al posto di class ([2d27ecf](https://gitlab.pepita.io/getrix/mls-site/commit/2d27ecf))
* Commentate momentaneamente i riferimenti al backend del multinvio ([5db63c5](https://gitlab.pepita.io/getrix/mls-site/commit/5db63c5))
* **billboard-photo:** corretto selector delle foto da mostrare ([7a35de1](https://gitlab.pepita.io/getrix/mls-site/commit/7a35de1))
* Corrects watch on webpack ([fd7c972](https://gitlab.pepita.io/getrix/mls-site/commit/fd7c972))
* **billboard:** a-capo nella descrizione ([5fb5ae8](https://gitlab.pepita.io/getrix/mls-site/commit/5fb5ae8))
* **billboard:** aggiunta azione Mostra/Nascondi per Epi ([b13b2b0](https://gitlab.pepita.io/getrix/mls-site/commit/b13b2b0))
* **billboard:** bottone orientamento e help ([9efbbc7](https://gitlab.pepita.io/getrix/mls-site/commit/9efbbc7))
* **billboard:** Corretta altezza immagine e spostato css nel giusto file ([3305b01](https://gitlab.pepita.io/getrix/mls-site/commit/3305b01))
* **billboard:** corretta gestione classe energetica + modal foto ([516c9ce](https://gitlab.pepita.io/getrix/mls-site/commit/516c9ce))
* **billboard:** Corrette proporzioni immagini cartellone vista immobile orizzontale e altezza box riferimento ([7dd1f59](https://gitlab.pepita.io/getrix/mls-site/commit/7dd1f59))
* **billboard:** corretto rebase ([c9b7ac2](https://gitlab.pepita.io/getrix/mls-site/commit/c9b7ac2))
* **billboard:** corretto url servizio htmlpdf ([707e814](https://gitlab.pepita.io/getrix/mls-site/commit/707e814))
* **billboard:** effetto hover immagini quando ci sono tre foto su Immobile orizzontale ([97a9b6a](https://gitlab.pepita.io/getrix/mls-site/commit/97a9b6a))
* **billboard:** fix firefox vetrina orizzontale tre foto ([c588544](https://gitlab.pepita.io/getrix/mls-site/commit/c588544))
* **billboard:** fix numero telefonico lungo ([522807f](https://gitlab.pepita.io/getrix/mls-site/commit/522807f))
* **billboard:** modale immagine firefox ([7d3f0b2](https://gitlab.pepita.io/getrix/mls-site/commit/7d3f0b2))
* **billboard:** modificato font size contratto e numero di telefono, corretta barra bianca help, classe energetica ([304062b](https://gitlab.pepita.io/getrix/mls-site/commit/304062b))
* **billboard:** ripristinati componenti + phone ([5dd8c12](https://gitlab.pepita.io/getrix/mls-site/commit/5dd8c12))
* **billboard:** tolto codice inutile ([32b7199](https://gitlab.pepita.io/getrix/mls-site/commit/32b7199))
* **billboard-description:** dimensioni font responsive ([18deb04](https://gitlab.pepita.io/getrix/mls-site/commit/18deb04))
* **billboard-energetic-class:** convertite classi in lowercase per corrispondenza con componente ([93a043f](https://gitlab.pepita.io/getrix/mls-site/commit/93a043f))
* **billboard-energetic-class:** corretti casi classi assenti ([e4102e6](https://gitlab.pepita.io/getrix/mls-site/commit/e4102e6))
* **billboard-energetic-class:** corretto caso Non classificabile ([d99258d](https://gitlab.pepita.io/getrix/mls-site/commit/d99258d))
* **billboard-features:** corretto comportamento alternativo Venduto/Affittato ([76cd865](https://gitlab.pepita.io/getrix/mls-site/commit/76cd865))
* **billboard-modal-description:** corretto stile font nella textarea ([ddb31c8](https://gitlab.pepita.io/getrix/mls-site/commit/ddb31c8))
* **billboard-orientamento:** altezza uguale agli altri pulsanti ([eeb332d](https://gitlab.pepita.io/getrix/mls-site/commit/eeb332d))
* **billboard-photo:** corretto comportamento selezione numero foto ([be7593b](https://gitlab.pepita.io/getrix/mls-site/commit/be7593b))
* **billboard-photo:** corretto conteggio opzioni numero foto ([8986111](https://gitlab.pepita.io/getrix/mls-site/commit/8986111))
* **build:** Rimuove la possiblita di condividere direttamente JS tra i bundles ([eea4600](https://gitlab.pepita.io/getrix/mls-site/commit/eea4600))
* **gtx-react-components:** rimossi oggetti non usati ([b15bf1a](https://gitlab.pepita.io/getrix/mls-site/commit/b15bf1a))
* **gtx-react-components:** sostituito type con on in GlobalHandler ([6267934](https://gitlab.pepita.io/getrix/mls-site/commit/6267934))
* **http-client:** corretto metodo richieste http ([7d78e70](https://gitlab.pepita.io/getrix/mls-site/commit/7d78e70))
* **loghi portali:** Loghi visibili e in https ([4a5077b](https://gitlab.pepita.io/getrix/mls-site/commit/4a5077b))
* **Modal:** risolto isOpen ([4a4454a](https://gitlab.pepita.io/getrix/mls-site/commit/4a4454a))
* **multi-invio-annunci:** Corregge l'area di click sull'attivazione dei portali ([d83130c](https://gitlab.pepita.io/getrix/mls-site/commit/d83130c))
* **multiinvio-annunci:** risolve problema filtro esclusivo zona-localita ([07416ff](https://gitlab.pepita.io/getrix/mls-site/commit/07416ff))
* **multinvio-annunci:** aggiunta option autoselect per far scattare l'onchange quando la select è già selezionata ([928a630](https://gitlab.pepita.io/getrix/mls-site/commit/928a630))
* **multinvio-annunci:** risolve alcuni problemi sull interazione della pagina ([f665c1a](https://gitlab.pepita.io/getrix/mls-site/commit/f665c1a))
* Corretto valore GetrixVersion ([6ee7d21](https://gitlab.pepita.io/getrix/mls-site/commit/6ee7d21))
* icona justify ([818316d](https://gitlab.pepita.io/getrix/mls-site/commit/818316d))
* polyfill Promise ([c89ac93](https://gitlab.pepita.io/getrix/mls-site/commit/c89ac93))
* post rebase ([a4245fc](https://gitlab.pepita.io/getrix/mls-site/commit/a4245fc))
* refactor model portali. Introduzione formetter portali ([2af3101](https://gitlab.pepita.io/getrix/mls-site/commit/2af3101))
* refactoring e fix vari annunci pubblicita e gestione portali ([16c48d8](https://gitlab.pepita.io/getrix/mls-site/commit/16c48d8))
* sass dev watch ([4be2dd0](https://gitlab.pepita.io/getrix/mls-site/commit/4be2dd0))
* **multinvio-annunci:** Corretto formatter backlink e inizializzazione popover ([dcefcd7](https://gitlab.pepita.io/getrix/mls-site/commit/dcefcd7))
* **multinvio-annunci:** corretto logo pulsante accesso ImmobiliarePro ([3424714](https://gitlab.pepita.io/getrix/mls-site/commit/3424714))
* **multinvio-annunci:** fix filtri e paginazione successivo/precedente ([062049b](https://gitlab.pepita.io/getrix/mls-site/commit/062049b))
* **multinvio-annunci:** fix vari sulla lista annunci ([dc70226](https://gitlab.pepita.io/getrix/mls-site/commit/dc70226))
* **multinvio-annunci:** ripristino codice perso nel merge ([5cb302a](https://gitlab.pepita.io/getrix/mls-site/commit/5cb302a))
* **multinvio-annuncio:** corretto nome metodo ([fe35c56](https://gitlab.pepita.io/getrix/mls-site/commit/fe35c56))
* **multinvio-annuncio:** Corretto valore ritorno settaggio zone e testo modale di errore ([f6f9bf0](https://gitlab.pepita.io/getrix/mls-site/commit/f6f9bf0))
* **Popover:** positioning ([5fa7684](https://gitlab.pepita.io/getrix/mls-site/commit/5fa7684))


### Features

* **billboard:** Aggiunge il un main con React ([90d5792](https://gitlab.pepita.io/getrix/mls-site/commit/90d5792))
* **billboard:** aggiunta azione pulsante back ([1507942](https://gitlab.pepita.io/getrix/mls-site/commit/1507942))
* **billboard:** aggiunta classe per contratto vendesi/affittasi ([89d7ccc](https://gitlab.pepita.io/getrix/mls-site/commit/89d7ccc))
* **billboard:** aggiunta modale di modifica al titolo ([a0f41ed](https://gitlab.pepita.io/getrix/mls-site/commit/a0f41ed))
* **billboard:** aggiunta modale modifica descrizione ([5ffc655](https://gitlab.pepita.io/getrix/mls-site/commit/5ffc655))
* **billboard:** aggiunta url html2pdf di produzione ([bd58396](https://gitlab.pepita.io/getrix/mls-site/commit/bd58396))
* **billboard:** aggiunte azioni Mostra/Nascondi ([eb4c38c](https://gitlab.pepita.io/getrix/mls-site/commit/eb4c38c))
* **billboard:** aggiunto effetto hover a elementi editabili ([3f9341b](https://gitlab.pepita.io/getrix/mls-site/commit/3f9341b))
* **billboard:** aggiunto salvataggio nelle azioni pdf ([5f310ea](https://gitlab.pepita.io/getrix/mls-site/commit/5f310ea))
* **billboard:** gestite opzioni non disponibili ([a2257b7](https://gitlab.pepita.io/getrix/mls-site/commit/a2257b7))
* **billboard:** nome pdf dinamico ([fbc5d7e](https://gitlab.pepita.io/getrix/mls-site/commit/fbc5d7e))
* **billboard:** opzione numero foto disabilitato se 0 ([f4bfa9c](https://gitlab.pepita.io/getrix/mls-site/commit/f4bfa9c))
* **billboard-colore:** aggiunta interazione color picker ([f6fff88](https://gitlab.pepita.io/getrix/mls-site/commit/f6fff88))
* **billboard-colorpicker:** inibita action su colore già selezionato ([fd51747](https://gitlab.pepita.io/getrix/mls-site/commit/fd51747))
* **billboard-contratto:** distinzione vende/vendesi e affitta/affittasi ([c941628](https://gitlab.pepita.io/getrix/mls-site/commit/c941628))
* **billboard-dati-dinamici:** gestione dati immobile ([39cbbcb](https://gitlab.pepita.io/getrix/mls-site/commit/39cbbcb))
* **billboard-dati-dinamici:** implementata Strategy usando WS su GV2 ([e9dc0bf](https://gitlab.pepita.io/getrix/mls-site/commit/e9dc0bf))
* **billboard-dati-dinamici:** implementate azioni stampa e download Pdf ([789e771](https://gitlab.pepita.io/getrix/mls-site/commit/789e771))
* **billboard-description:** applicato allineamento anche a indirizzo ([2184781](https://gitlab.pepita.io/getrix/mls-site/commit/2184781))
* **billboard-description:** modificate dimensioni descrizione ([3a84b7b](https://gitlab.pepita.io/getrix/mls-site/commit/3a84b7b))
* **billboard-editor-form:** aggiunta modalità responsive per la palette ([6390d4b](https://gitlab.pepita.io/getrix/mls-site/commit/6390d4b))
* **billboard-editor-form:** aggiunti titolo e footer della modale; aggiunta mediaquery ([758c110](https://gitlab.pepita.io/getrix/mls-site/commit/758c110))
* **billboard-energetic-class:** applicata nuova versione del componente ([329b730](https://gitlab.pepita.io/getrix/mls-site/commit/329b730))
* **billboard-features:** aggiunte strip Venduto e Affittato ([f5902c5](https://gitlab.pepita.io/getrix/mls-site/commit/f5902c5))
* **billboard-features:** il render di Venduto e Affittato dipendente dal contratto ([9b04988](https://gitlab.pepita.io/getrix/mls-site/commit/9b04988))
* **billboard-modal-description:** aggiunta preview allineamento e fontsize ([4edda5b](https://gitlab.pepita.io/getrix/mls-site/commit/4edda5b))
* **billboard-modal-photo:** aggiunta modale per selezione foto ([23bfbcd](https://gitlab.pepita.io/getrix/mls-site/commit/23bfbcd))
* **billboard-modal-photo:** migliorata gestione numero di foto presenti ([aa55d0f](https://gitlab.pepita.io/getrix/mls-site/commit/aa55d0f))
* **billboard-modals:** aggiunto reset valori e modificate dimensioni ([865b8e2](https://gitlab.pepita.io/getrix/mls-site/commit/865b8e2))
* **billboard-orientamento:** aggiunto toggle orientamento ([88ade44](https://gitlab.pepita.io/getrix/mls-site/commit/88ade44))
* **billboard-orientamento:** aggiunto toggle orientamento ([01670fa](https://gitlab.pepita.io/getrix/mls-site/commit/01670fa))
* **billboard-phone:** icona telefono nascosta dinamicamente ([aeaaa8b](https://gitlab.pepita.io/getrix/mls-site/commit/aeaaa8b))
* **billboard-phone-modal:** aggiunta modale modifica telefono ([2785d1f](https://gitlab.pepita.io/getrix/mls-site/commit/2785d1f))
* **billboard-tooltip:** aggiunto tooltip ([1d46698](https://gitlab.pepita.io/getrix/mls-site/commit/1d46698))
* **billborard:** aggiunta gestione prezzo vuoto ([9ec61c8](https://gitlab.pepita.io/getrix/mls-site/commit/9ec61c8))
* **build:** Semplifica la gestione 'multibundle' della build ([5e2fa32](https://gitlab.pepita.io/getrix/mls-site/commit/5e2fa32))
* **Checkbox:** Aggiunge la possiblita di gestire l'area di click ([99aeb15](https://gitlab.pepita.io/getrix/mls-site/commit/99aeb15))
* **DebounceHandler:** A solution for panic click ([a533124](https://gitlab.pepita.io/getrix/mls-site/commit/a533124))
* **Dropdpown:** gestito il caret ([d26a97e](https://gitlab.pepita.io/getrix/mls-site/commit/d26a97e))
* **filters-form:** gestione interazioni form dei filtri e implementazione componenti interessati ([adbd4ad](https://gitlab.pepita.io/getrix/mls-site/commit/adbd4ad))
* **gtx-react:** Aggiunge il component Badge! :sparkles: ([aa2f349](https://gitlab.pepita.io/getrix/mls-site/commit/aa2f349))
* **gtx-react:** Aggiunge il componente EnergeticClass ([3d47a40](https://gitlab.pepita.io/getrix/mls-site/commit/3d47a40))
* **gtx-react:** Aggiunge un modulo polyfill con le feature utili a scrivere codice piùidiomatico e performante ([647a24c](https://gitlab.pepita.io/getrix/mls-site/commit/647a24c))
* **gtx-react-components:** Aggiunge il componente IframeSandbox ([30b7a6f](https://gitlab.pepita.io/getrix/mls-site/commit/30b7a6f))
* **gtx-react-components:** aggiunto componente popover e colorpicker ([9ffaf5a](https://gitlab.pepita.io/getrix/mls-site/commit/9ffaf5a))
* **gtx-react-components:** aggiunto componente Tooltip ([c55966d](https://gitlab.pepita.io/getrix/mls-site/commit/c55966d))
* **lint:** Effettua il passaggio a eslint ([72eb211](https://gitlab.pepita.io/getrix/mls-site/commit/72eb211))
* **modal:** aggiunta modale per editing ([a03b48e](https://gitlab.pepita.io/getrix/mls-site/commit/a03b48e))
* **multi-invio-annunci:** aggiunge barra dei risultati ([ed23f20](https://gitlab.pepita.io/getrix/mls-site/commit/ed23f20))
* **multi-invio-annunci:** Aggiunge i filtri di ricerca ([263d862](https://gitlab.pepita.io/getrix/mls-site/commit/263d862))
* **multi-invio-annunci:** Aggiunge i filtri di ricerca ([3b7e2d0](https://gitlab.pepita.io/getrix/mls-site/commit/3b7e2d0))
* **multi-invio-annunci:** Aggiunge il rendering condizionale sull'header della lista ([a700d8e](https://gitlab.pepita.io/getrix/mls-site/commit/a700d8e))
* **multi-invio-annunci:** Implementa l'header della tabella ([ae8cff5](https://gitlab.pepita.io/getrix/mls-site/commit/ae8cff5))
* **multi-invio-annunci:** Implementa la logia per la modale Altri Portali ([37fe00a](https://gitlab.pepita.io/getrix/mls-site/commit/37fe00a))
* Aggiunto componente InlineSelect e init form cartellone ([d14d4c8](https://gitlab.pepita.io/getrix/mls-site/commit/d14d4c8))
* **multi-invio-annunci:** mock per la lista annunci ([29de794](https://gitlab.pepita.io/getrix/mls-site/commit/29de794))
* **Popover:** Adds smart positioning ([26149bb](https://gitlab.pepita.io/getrix/mls-site/commit/26149bb))
* **Popover:** Aggiunto fix del posizionamento quando il popover va fuori il display ([914f354](https://gitlab.pepita.io/getrix/mls-site/commit/914f354))
* **Popover:** Implementa il copmortamento "responsive" ([fe16a68](https://gitlab.pepita.io/getrix/mls-site/commit/fe16a68))
* **Popover:** Removes PopoverSwitch ([d8697f0](https://gitlab.pepita.io/getrix/mls-site/commit/d8697f0))
* Aggiorna React alla 16.3 ([b92ea91](https://gitlab.pepita.io/getrix/mls-site/commit/b92ea91))
* **react:** update a react 16.3.2 ([0e0785f](https://gitlab.pepita.io/getrix/mls-site/commit/0e0785f))
* Adds autoformatting on staged changes ([5a1fb85](https://gitlab.pepita.io/getrix/mls-site/commit/5a1fb85))
* Aggiornata versione getrix-common e yarn lock ([37f0072](https://gitlab.pepita.io/getrix/mls-site/commit/37f0072))
* Aggiunge il componente Modal ([9a6f2ef](https://gitlab.pepita.io/getrix/mls-site/commit/9a6f2ef))
* Aggiunge il componente TestAttributes per aggiungere attributi per i test e2e ([bcc99be](https://gitlab.pepita.io/getrix/mls-site/commit/bcc99be))
* Aggiunge l'autodiscovery delle entry nelle cartelle pages/nome_entry/main.(js|scss) ([35ccc9e](https://gitlab.pepita.io/getrix/mls-site/commit/35ccc9e))
* aggiunti entity model per gli annunci e i portali attivi. Refactoring ([582bbff](https://gitlab.pepita.io/getrix/mls-site/commit/582bbff))
* aggiunto bundle per gestione annunci ([48ae785](https://gitlab.pepita.io/getrix/mls-site/commit/48ae785))
* Avvia lo sviluppo di multiinvio annunci (React + Components) ([a6cd3aa](https://gitlab.pepita.io/getrix/mls-site/commit/a6cd3aa))
* Grafica cartellone! ([48c2e0a](https://gitlab.pepita.io/getrix/mls-site/commit/48c2e0a))
* Grafica cartellone! ([742198b](https://gitlab.pepita.io/getrix/mls-site/commit/742198b))
* Migration to webpack 4 ([45bee9a](https://gitlab.pepita.io/getrix/mls-site/commit/45bee9a))


### Performance Improvements

* **build:** Rimuove grunt-concurrent ed effettua una pulizia dei task ([4b70a11](https://gitlab.pepita.io/getrix/mls-site/commit/4b70a11))



<a name="3.17.17"></a>
## [3.17.17](https://gitlab.pepita.io/getrix/mls-site/compare/v3.17.16...v3.17.17) (2018-04-03)


### Bug Fixes

* Corretta formattazione data ultima attività dispositivi autorizzati ([51199ef](https://gitlab.pepita.io/getrix/mls-site/commit/51199ef))
* **full-search-form:** Aggiornamento cartine Roma e Trieste ([80bf1f5](https://gitlab.pepita.io/getrix/mls-site/commit/80bf1f5))



<a name="3.17.16"></a>
## [3.17.16](https://gitlab.pepita.io/getrix/mls-site/compare/v3.17.15...v3.17.16) (2018-03-27)


### Bug Fixes

* **Settings:** Impostato autocomplete off per impedire il caching selvaggio delle informazioni negli input da parte di firefox ([4526753](https://gitlab.pepita.io/getrix/mls-site/commit/4526753))
* **User:** Differenziato messaggio errore email già in uso per getrix e pro ([f0742e9](https://gitlab.pepita.io/getrix/mls-site/commit/f0742e9))
* Aggiornata versione getrix-bundles ([e2f8880](https://gitlab.pepita.io/getrix/mls-site/commit/e2f8880))


### Features

* Aggiunto controllo su minimo numero parole descrizione agenzia ([02d64b6](https://gitlab.pepita.io/getrix/mls-site/commit/02d64b6))



<a name="3.17.14"></a>
## [3.17.14](https://gitlab.pepita.io/getrix/mls-site/compare/v3.17.13...v3.17.14) (2018-03-14)



<a name="3.17.13"></a>
## [3.17.13](https://gitlab.pepita.io/getrix/mls-site/compare/v3.17.12...v3.17.13) (2018-03-14)


### Bug Fixes

* **mailer:** switch transport da smtp a mail() ([4c2ea3f](https://gitlab.pepita.io/getrix/mls-site/commit/4c2ea3f))
* **propertyfinder:** Corretta gestione bookmark in caso di contatto inesistente ([9ae478c](https://gitlab.pepita.io/getrix/mls-site/commit/9ae478c))



<a name="3.17.12"></a>
## [3.17.12](https://gitlab.pepita.io/getrix/mls-site/compare/v3.17.11...v3.17.12) (2018-03-09)



<a name="3.17.11"></a>
## [3.17.11](https://gitlab.pepita.io/getrix/mls-site/compare/v3.17.10...v3.17.11) (2018-03-08)


### Bug Fixes

* **webpack:** Estende babel anche ai bundle 'esterni' alla build ([8325e91](https://gitlab.pepita.io/getrix/mls-site/commit/8325e91))
* Corrects watch on webpack ([76f834c](https://gitlab.pepita.io/getrix/mls-site/commit/76f834c))


### Features

* **build:** Semplifica la gestione 'multibundle' della build ([c45094e](https://gitlab.pepita.io/getrix/mls-site/commit/c45094e))



<a name="3.17.10"></a>
## [3.17.10](https://gitlab.pepita.io/getrix/mls-site/compare/v3.17.9...v3.17.10) (2018-03-05)



<a name="3.17.9"></a>
## [3.17.9](https://gitlab.pepita.io/getrix/mls-site/compare/v3.17.8...v3.17.9) (2018-03-01)



<a name="3.17.8"></a>
## [3.17.8](https://gitlab.pepita.io/getrix/mls-site/compare/v3.17.7...v3.17.8) (2018-02-28)



<a name="3.17.7"></a>
## [3.17.7](https://gitlab.pepita.io/getrix/mls-site/compare/v3.17.6...v3.17.7) (2018-02-27)



<a name="3.17.6"></a>
## [3.17.6](https://gitlab.pepita.io/getrix/mls-site/compare/v3.17.5...v3.17.6) (2018-02-22)



<a name="3.17.5"></a>
## [3.17.5](https://gitlab.pepita.io/getrix/mls-site/compare/v3.17.4...v3.17.5) (2018-02-20)



<a name="3.17.4"></a>
## [3.17.4](https://gitlab.pepita.io/getrix/mls-site/compare/v3.17.3...v3.17.4) (2018-02-19)



<a name="3.17.3"></a>
## [3.17.3](https://gitlab.pepita.io/getrix/mls-site/compare/v3.17.2...v3.17.3) (2018-02-16)



<a name="3.17.2"></a>
## [3.17.2](https://gitlab.pepita.io/getrix/mls-site/compare/v3.17.1...v3.17.2) (2018-02-14)



<a name="3.17.1"></a>
## [3.17.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.17.0...v3.17.1) (2018-02-14)


### Bug Fixes

* **build:** Moves bloodhound alias to node_modules to fix the symlink issue of webpack loaders ([ff3cdd0](https://gitlab.pepita.io/getrix/mls-site/commit/ff3cdd0))


### Features

* **build:** Effettua il passaggio a yarn, deprecando cosi sia bower che npm ([3af57e2](https://gitlab.pepita.io/getrix/mls-site/commit/3af57e2))



<a name="3.17.0"></a>
# [3.17.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.16.6...v3.17.0) (2018-02-12)



<a name="3.16.6"></a>
## [3.16.6](https://gitlab.pepita.io/getrix/mls-site/compare/v3.16.5...v3.16.6) (2018-02-08)



<a name="3.16.5"></a>
## [3.16.5](https://gitlab.pepita.io/getrix/mls-site/compare/v3.16.4...v3.16.5) (2018-02-06)



<a name="3.16.4"></a>
## [3.16.4](https://gitlab.pepita.io/getrix/mls-site/compare/v3.16.3...v3.16.4) (2018-01-31)



<a name="3.16.3"></a>
## [3.16.3](https://gitlab.pepita.io/getrix/mls-site/compare/v3.16.2...v3.16.3) (2018-01-29)



<a name="3.16.2"></a>
## [3.16.2](https://gitlab.pepita.io/getrix/mls-site/compare/v3.16.1...v3.16.2) (2018-01-26)



<a name="3.16.1"></a>
## [3.16.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.16.0...v3.16.1) (2018-01-25)



<a name="3.16.0"></a>
# [3.16.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.15.10...v3.16.0) (2018-01-19)



<a name="3.15.10"></a>
## [3.15.10](https://gitlab.pepita.io/getrix/mls-site/compare/v3.15.9...v3.15.10) (2018-01-17)



<a name="3.15.9"></a>
## [3.15.9](https://gitlab.pepita.io/getrix/mls-site/compare/v3.15.8...v3.15.9) (2018-01-12)



<a name="3.15.8"></a>
## [3.15.8](https://gitlab.pepita.io/getrix/mls-site/compare/v3.15.7...v3.15.8) (2017-12-20)



<a name="3.15.7"></a>
## [3.15.7](https://gitlab.pepita.io/getrix/mls-site/compare/v3.15.6...v3.15.7) (2017-12-14)



<a name="3.15.6"></a>
## [3.15.6](https://gitlab.pepita.io/getrix/mls-site/compare/v3.15.5...v3.15.6) (2017-12-14)



<a name="3.15.5"></a>
## [3.15.5](https://gitlab.pepita.io/getrix/mls-site/compare/v3.15.4...v3.15.5) (2017-12-13)



<a name="3.15.4"></a>
## [3.15.4](https://gitlab.pepita.io/getrix/mls-site/compare/v3.15.3...v3.15.4) (2017-12-12)



<a name="3.15.3"></a>
## [3.15.3](https://gitlab.pepita.io/getrix/mls-site/compare/v3.15.2...v3.15.3) (2017-12-12)



<a name="3.15.2"></a>
## [3.15.2](https://gitlab.pepita.io/getrix/mls-site/compare/v3.15.1...v3.15.2) (2017-12-11)



<a name="3.15.1"></a>
## [3.15.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.15.0...v3.15.1) (2017-12-11)



<a name="3.15.0"></a>
# [3.15.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.13.0...v3.15.0) (2017-12-11)



<a name="3.13.0"></a>
# [3.13.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.12.1...v3.13.0) (2017-12-04)



<a name="3.12.1"></a>
## [3.12.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.12.0...v3.12.1) (2017-11-23)



<a name="3.12.0"></a>
# [3.12.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.11.0...v3.12.0) (2017-11-21)



<a name="3.11.0"></a>
# [3.11.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.10.0...v3.11.0) (2017-11-21)



<a name="3.10.0"></a>
# [3.10.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.9.0...v3.10.0) (2017-11-20)



<a name="3.9.0"></a>
# [3.9.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.8.0...v3.9.0) (2017-11-14)



<a name="3.8.0"></a>
# [3.8.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.7.1...v3.8.0) (2017-11-07)



<a name="3.7.1"></a>
## [3.7.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.7.0...v3.7.1) (2017-10-31)



<a name="3.7.0"></a>
# [3.7.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.6.0...v3.7.0) (2017-10-27)



<a name="3.6.0"></a>
# [3.6.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.5.0...v3.6.0) (2017-10-25)



<a name="3.5.0"></a>
# [3.5.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.4.0...v3.5.0) (2017-10-23)



<a name="3.4.0"></a>
# [3.4.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.3.0...v3.4.0) (2017-10-17)



<a name="3.3.0"></a>
# [3.3.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.2.0...v3.3.0) (2017-10-13)



<a name="3.2.0"></a>
# [3.2.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.1.4...v3.2.0) (2017-10-11)



<a name="3.1.4"></a>
## [3.1.4](https://gitlab.pepita.io/getrix/mls-site/compare/v3.1.3...v3.1.4) (2017-10-06)



<a name="3.1.3"></a>
## [3.1.3](https://gitlab.pepita.io/getrix/mls-site/compare/v3.1.2...v3.1.3) (2017-10-06)



<a name="3.1.2"></a>
## [3.1.2](https://gitlab.pepita.io/getrix/mls-site/compare/v3.1.1...v3.1.2) (2017-10-05)



<a name="3.1.1"></a>
## [3.1.1](https://gitlab.pepita.io/getrix/mls-site/compare/v3.1.0...v3.1.1) (2017-10-05)



<a name="3.1.0"></a>
# [3.1.0](https://gitlab.pepita.io/getrix/mls-site/compare/v3.0.4...v3.1.0) (2017-10-04)



<a name="3.0.4"></a>
## [3.0.4](https://gitlab.pepita.io/getrix/mls-site/compare/v3.0.3...v3.0.4) (2017-09-28)



<a name="3.0.3"></a>
## [3.0.3](https://gitlab.pepita.io/getrix/mls-site/compare/v3.0.2...v3.0.3) (2017-09-27)



<a name="3.0.2"></a>
## [3.0.2](https://gitlab.pepita.io/getrix/mls-site/compare/v3.0.1...v3.0.2) (2017-09-26)



<a name="3.0.1"></a>
## [3.0.1](https://gitlab.pepita.io/getrix/mls-site/compare/v0.0.1...v3.0.1) (2017-09-26)



<a name="0.0.1"></a>
## [0.0.1](https://gitlab.pepita.io/getrix/mls-site/compare/v2.0.0-0...v0.0.1) (2017-08-23)


### Bug Fixes

* **getrix-common:** Rende la versione piu strict per rimuovere conflitto tra prerelease ([ae0513a](https://gitlab.pepita.io/getrix/mls-site/commit/ae0513a))
* **valutazione-immobiliare:** Fixes 'Map container is already initialized' error ([7be2894](https://gitlab.pepita.io/getrix/mls-site/commit/7be2894))


### Features

* **build:** Adds babel transpiling to webpack! 🎉 ([0124ad3](https://gitlab.pepita.io/getrix/mls-site/commit/0124ad3))
* Migrazione al sistema di mappe di immobiliare ([9edb082](https://gitlab.pepita.io/getrix/mls-site/commit/9edb082))



<a name="1.0.0-1"></a>
# [1.0.0-1](https://gitlab.pepita.io/getrix/mls-site/compare/v1.0.0-0...v1.0.0-1) (2015-07-22)
