parameters:
    country_tag: "%env(COUNTRY_TAG)%"
    app.side_menu_config_file_path: "/templates/base/Vendor/getrix/common/config/side-menu.json"
    app.header_config_file_path: "/templates/base/Vendor/getrix/common/config/header.json"
    app.gx_navigation_menu: "%kernel.project_dir%/config/%env(COUNTRY_TAG)%/menu.yaml"
    app.sentry_enabled: "%env(SENTRY_ENABLED)%"
    app.performance_price_proposal_enabled: "%env(PERFORMANCE_PRICE_PROPOSAL_ENABLED)%"
    app.performance_show_agent_card: "%env(PERFORMANCE_SHOW_AGENT_CARD)%"
    app.performance_show_absolute_data: "%env(PERFORMANCE_SHOW_ABSOLUTE_DATA)%"
    app.performance_show_improve_advices: "%env(PERFORMANCE_SHOW_IMPROVE_ADVICES)%"
    app.performance_show_perday_contacts_chart: "%env(PERFORMANCE_SHOW_PERDAY_CONTACTS_CHART)%"
    app.performance_show_previous_stats: "%env(PERFORMANCE_SHOW_PREVIOUS_STATS)%"
    app.performance_show_property_cta: "%env(PERFORMANCE_SHOW_PROPERTY_CTA)%"
    app.settings_invoices_show_landing_page: "%env(bool:SETTINGS_INVOICES_SHOW_LANDING_PAGE)%"
    app.zone_package_tier_low: "bronze"
    app.zone_package_tier_medium: "silver"
    app.zone_package_tier_high: "gold"
    app.agency.logo.endpoint: "agencylogo"
    app.ad.imagenoresize.endpoint: "imagenoresize"
    app.ad.image.endpoint: "image"
    app.ad.plan.endpoint: "plan"
    app.agent.logo.endpoint: "agenti"
    app.portal.logo.endpoint: "portali"
    app.responsible.logo.endpoint: "image"
    app.helpjuice_enabled: '%env(HELPJUICE_ENABLED)%'
    app.helpjuice_account_url: '%env(HELPJUICE_ACCOUNT_URL)%'
    app.helpjuice_subdomain: '%env(HELPJUICE_SUBDOMAIN)%'
    app.braze.api_key: '%env(BRAZE_API_KEY)%'
    app.braze.in_app_messages: '%env(BRAZE_IN_APP_MESSAGES)%'
    app.braze.logging: '%env(BRAZE_LOGGING)%'
    app.braze.logging_users: '%env(BRAZE_LOGGING_USERS)%'
    app.braze.push_notifications: '%env(BRAZE_PUSH_NOTIFICATIONS)%'
    app.braze.sdk_endpoint: '%env(BRAZE_SDK_ENDPOINT)%'
    app.report_insights_enabled: '%env(bool:REPORT_INSIGHTS_ENABLED)%'
    app.report_insights_endpoint: '%env(string:REPORT_INSIGHTS_ENDPOINT)%'
    app.report_insights_getrix_app: 'getrix'
    app.report_insights_pro_app: 'pro'
    # Dashboard ad stats categories filter
    app.dashboard_ads_stats_categories_allowed:
        - !php/const App\Constants\Property\AdPropertyCategoriesConstants::RESIDENTIAL
        - !php/const App\Constants\Property\AdPropertyCategoriesConstants::COMMERCIAL
        - !php/const App\Constants\Property\AdPropertyCategoriesConstants::TURISTIC
        - !php/const App\Constants\Property\AdPropertyCategoriesConstants::ROOMS
        - !php/const App\Constants\Property\AdPropertyCategoriesConstants::INTERNATIONALSEARCH
        - !php/const App\Constants\Property\AdPropertyCategoriesConstants::NEWCONSTRUCTION
        - !php/const App\Constants\Property\AdPropertyCategoriesConstants::AUCTION
        - !php/const App\Constants\Property\AdPropertyCategoriesConstants::BUILDINGS
        - !php/const App\Constants\Property\AdPropertyCategoriesConstants::DEPOTWAREHOUSE
        - !php/const App\Constants\Property\AdPropertyCategoriesConstants::BOXAUTO
        - !php/const App\Constants\Property\AdPropertyCategoriesConstants::OFFICESCOWORKING
        - !php/const App\Constants\Property\AdPropertyCategoriesConstants::LANDS
        - !php/const App\Constants\Property\AdPropertyCategoriesConstants::SHEDS
        - !php/const App\Constants\Property\AdPropertyCategoriesConstants::COMMERCIALLOCALSHOPS
        - !php/const App\Constants\Property\AdPropertyCategoriesConstants::PROJECTS

    # Digital wallet
    app.digital_wallet_enabled: true

    # Cadastral data (Italy only)
    app.show_cadastral_data: false

    app.notification_settings_version: "%env(NOTIFICATION_SETTINGS_VER)%"
    app.notification_settings_v2_ids: "%env(NOTIFICATION_SETTINGS_V2_AGENCY_IDS)%"

    app.messaging_v2_enabled: "%env(MESSAGING_V2_ENABLED)%"
    app.messaging_v2_agency_enabled: "%env(MESSAGING_V2_AGENCY_ENABLED)%"

    # First Page Tag
    app.show_first_page_tag: true
    app.first_page_tag_threshold: 25
    app.cockade_icon_threshold: 1

    app.add_property_enabled: "%env(ADD_PROPERTY_ENABLED)%"
    # Property new fields (should be overridden by hr parameters)
    app.property_field_solar_water_heating_enabled: false
    app.property_field_available_for_students_enabled: false
    app.property_field_view_enabled: false
    app.property_field_distance_from_sea_enabled: false
    app.property_field_views: []
    app.property_distance_from_sea_feature_id: 6
    app.property_distance_from_sea_unit_of_measurement: 'm'

    # planimetries and photoplan
    app.max_ad_planimetry: 20
    # growthbook
    app.growthbook_api_host: "%env(GROWTHBOOK_API_HOST)%"
    app.growthbook_client_key: "%env(GROWTHBOOK_CLIENT_KEY)%"
    app.growthbook_decryption_key: "%env(GROWTHBOOK_DECRYPTION_KEY)%"
    app.growthbook_dev_mode_enabled: "%env(GROWTHBOOK_DEV_MODE_ENABLED)%"
    app.growthbook_enabled: "%env(GROWTHBOOK_ENABLED)%"

    # Web push notifications
    app.mixpanel_identify_tracking: true

    # sidebars
    app.sidebar_left_enabled: true
    app.sidebar_right_enabled: true

    app.fotoplan_baseurl: "%env(FOTOPLAN_BASEURL)%"
    
    app.getrix_media_url: "%env(GETRIX_MEDIA_URL)%"

   # Registration form 
    app.show_registration_business_type: true

   # Key to set/get languages map lookup in session storage
    app.languages_map_lookup_key: "languagesMapLookup"