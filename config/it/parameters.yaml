parameters:
    locale: it_IT
    default_lang_code: it-IT
    secret: ********************************
    app_secret: R2WIB6V0JN97SST0P688LNX16FQ5TKLL2SE0MXQ1
    auth_token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************.aWWodm63y4D_atUdjDCBqUqKWbmJS_p7zbHtU030YNw
    token_signature: be003aff1d79e54bb96f8c018281a8323903a464b49e3e7afc1b1bac3a881baba32feab0125979a25778219b9a8b96b4b8d0a283f58e1ad20b6c68440b9f02757231fc7fcbbd80b768b842de1a2a4a4ab9629a5e133baee3ef9976f5cee429d4

    new_getrix.api_key: "chhBDdRNiFuCXyRevdR3WcQUYQLywPcX"

    api_wsse_username: getrix-it
    api_wsse_password: 53d10328-cb36-467b-9c0c-b1461e1eab8e

    app_id: getrix
    app_tag_gtx: "gtx"
    app_tag_pro: "pro"

    sdk_cache_version: 1571222122

    thrifter_use_discovery: null
    thrifter_rpc_services: config/%env(COUNTRY_TAG)%/sdk/rpc_services_prod.yml

    log_stream_format: "%%datetime%% - %%channel%%.%%level_name%% - %%extra.appId%% - %%extra.token%% - %%extra.sessionId%% - %%extra.requestId%% - %%extra.serviceCanonicalName%% - %%extra.methodName%% - %%extra.methodArguments%% - %%message%% - %%extra%%\n"

    immobiliare.baseurl: "%env(IMMOBILIARE_BASEURL)%"
    immobiliare.baseurl_internal: "%env(IMMOBILIARE_BASEURL_INTERNAL)%"
    immobiliare.gestionale.baseurl: "%env(IMMOBILIARE_GESTIONALE_BASEURL)%"
    immobiliare.mediaurl: "%env(IMMOBILIARE_MEDIA_URL)%"
    immobiliare.responsabile_avatar_url: "%getrix.baseurl%/img_referente/"
    immobiliare.responsabile_avatar_sapi_url: "%env(IMMOBILIARE_RESPONSABILE_AVATAR_SAPI_URL)%"
    immobiliare.images_url: "%immobiliare.baseurl%/img2/"
    immobiliare.api_key: "chhBDdRNiFuCXyRevdR3WcQUYQLywPcX" # Chiave usata per SSO
    immobiliare.check_property.baseurl: "%env(CHECK_PROPERTY_BASEURL)%"

    getrix.name: "Getrix"
    getrix.baseurl: "%env(GETRIX_BASEURL)%"
    getrix.baseurl_internal: "%env(GETRIX_BASEURL_INTERNAL)%"
    getrix.media: "%env(GETRIX_MEDIA_URL)%"
    getrix.media_internal: "%env(GETRIX_MEDIA_URL_INTERNAL)%"
    getrix.media.images_path: "%env(GETRIX_MEDIA_IMAGES_PATH)%"
    getrix.media.images_backup_path: "%env(GETRIX_MEDIA_IMAGES_BACKUP_PATH)%"
    getrix.media.plans_path: "%env(GETRIX_MEDIA_PLANS_PATH)%"
    getrix.media.plans_backup_path: "%env(GETRIX_MEDIA_PLANS_BACKUP_PATH)%"
    getrix.static_page_telefono_smart: "%getrix.baseurl%/static/telefono-smart/"
    getrix.api_baseurl: "%env(GETRIX_API_BASEURL)%"
    getrix.api_timeout: "%env(GETRIX_API_TIMEOUT)%"
    getrix.api_counters_timeout: "%env(GETRIX_API_COUNTERS_TIMEOUT)%"
    getrix.api_login_url: "%env(string:GETRIX_API_LOGIN_URL)%"
    getrix.use_getrix_api_login: "%env(bool:USE_GETRIX_API_LOGIN)%"
    getrix.use_getrix_api_login_authentication_tokens: "%env(bool:USE_GETRIX_API_LOGIN_AUTHENTICATION_TOKENS)%"
    getrix.get_news_content_url: "%getrix.baseurl_internal%/ws/get_news_content"
    getrix.access_trace_enable: "%env(bool:ACCESS_TRACE_ENABLE)%"
    getrix.access_trace_base_url: "%env(ACCESS_TRACE_BASE_URL)%"
    getrix.access_trace_username: "%env(ACCESS_TRACE_USERNAME)%"
    getrix.access_trace_password: "%env(ACCESS_TRACE_PASSWORD)%"
    getrix.access_trace_timeout: "%env(ACCESS_TRACE_TIMEOUT)%"

    getrix.cookie_session_name: PHPSESSID
    getrix.cookie_session_persist_name: PROPERSID
    getrix.cookie_session_domain: "%env(GETRIX_COOKIE_SESSION_DOMAIN)%"

    app.csrf_token_registration_id: "register-token"

    getrix.googleMapsKey: "%env(GETRIX_GOOGLE_MAPS_KEY)%"
    getrix.googleMapsVersion: 3

    getrix.hotjarSwitch: 1
    getrix.hotjarId: "%env(GETRIX_HOTJAR_ID)%"
    getrix.hotjarSnippetVersion: "%env(GETRIX_HOTJAR_SNIPPET_VERSION)%"

    getrix.staticMapsBaseUrl: "%env(GETRIX_STATIC_MAPS_BASEURL)%"
    getrix.staticCityMapsBaseUrl: "%env(GETRIX_STATIC_MAPS_CITY_BASEURL)%"
    getrix.staticCityMapsTag: 2019070201
    getrix.logo: "/bundles/base/getrix/common/img/country/it/logo-gtx/logo.svg"
    getrix.favicon_path: "/bundles/base/getrix/common/img/country/it/favicon-gtx/"

    app.name: "Getrix"
    app.name_pro: "ImmobiliarePro"
    app.subscribed_agencies: "28.000"
    app.body_css_class_suffix: "%app.name_pro%"
    app.email_from_address: "<EMAIL>"
    app.email_a2f_from_address: "<EMAIL>"
    app.title: "%app.name%"
    app.title_pro: "%app.name_pro%"
    app.tag: "%app_tag_gtx%"
    app.firma_email: "Immobiliare.it"
    app.default_target_path: "/"
    app.cookie_loginevent_name: _APPLOGINEVT
    app.white_label: false
    app.logs_dir: "/var/log/apps/gestionale"
    app.email_servizio_clienti: "<EMAIL>"
    app.email_assistenza_contenuti: "<EMAIL>"
    app.email_assistenza_clienti: "<EMAIL>"
    app.email_responsabile_default: "<EMAIL>"
    app.tel_servizio_clienti: "02 87 11 87"
    app.email_responsabile_assistenza_clienti: "<EMAIL>"
    app.pro_dashboard_stats_filters: 0
    app.email_chat: "<EMAIL>"

    app.svg_icons_sprite_file_path: "/bundles/base/svg/icons-set/gx-icons-sprite.svg"
    app.common_templates_path: "base/Vendor/getrix/common/"
    app.common_image_base_url: "%app.baseurl%/bundles/base/getrix/common/img"
    app.logo: "/bundles/base/getrix/common/img/country/it/logo/logo-pro.svg"
    app.favicon_path: "/bundles/base/getrix/common/img/country/it/favicon/"

    app.cookie_session_domain: "%env(APP_COOKIE_SESSION_DOMAIN)%"
    app.cookie_session_name: PROMLSSID
    app.cookie_session_persist_name: PROPERSID
    app.cookie_mainteinance_access_name: _MAINTEINANCE_FORCE_ACCESS

    app.session_handler_id: getrix_app.session_getrix

    app.memcached.prefix: "mls_"

    app.baseurl: ""
    app.logout_target: "/signin/"
    app.user_provider.class: 'App\Security\User\GetrixUserProvider'
    app.session_handler_class_name: 'App\Service\Base\Session\Getrix'
    app.service_unavalaible_file: "%env(APP_SERVICE_UNAVAILABLE_FILE)%"

    app.a2f.api_key: "getrix"

    app.storage_responsible_images_api_enabled: "%env(STORAGE_RESPONSIBLE_IMAGES_API_ENABLED)%"

    maps.consumerKey: "immobiliarepro"
    maps.attribution: "Immobiliare.it"
    maps.tilesUrlTemplate: "%env(MAPS_TILES_URL_TEMPLATE)%"
    maps.geocoderUrlTemplate: "%env(MAPS_GEOCODER_URL_TEMPLATE)%"
    maps.language: '%env(MAPS_LANGUAGE)%'

    sso.listglobally.token_request_url: 'https://id.properstar.com/api/v1/token'
    sso.listglobally.sign_in_url: 'https://dashboard.properstar.com/auth'
    sso.listglobally.api_key: "23310bfa-5693-42a0-9fd6-5bf11e50bbf2"

    app.htmlpdf.baseurl: "%env(HTMLPDF_BASEURL)%"

    chat_bot.status: "%env(CHAT_BOT_ENABLED)%"
    chat_bot.groupID: "d3d28bf8176db04f66005fe7f3d99707"
    chat_bot.customerID: "7185f6b50e71ec6166cbe748e6a063bb"

    app.privacy_data_owner: "Immobiliare.it"
    app.ad_portal: "Immobiliare.it"
    app.ad_portal_detail_url: "%immobiliare.baseurl%/annunci/%%s"
    app.ad_portal_child_detail_url: "%immobiliare.baseurl%/annunci/%%s/%%s"
    app.ad_portal_preview_detail_url: "%immobiliare.baseurl%/annunci/%%s?anteprima=s"
    app.ad_portal_about_us_url: "%immobiliare.baseurl%/info/chi-siamo/"
    app.ad_portal_contact_us_url: "%immobiliare.baseurl%/info/contattaci/"
    app.phone_verification: "%env(PHONE_VERIFICATION)%"
    app.service_smart_phone: "Telefono Smart"
    app.service_virtual_tour_360: "Virtual Tour 360"
    app.service_photoplan: "Fotoplan"
    app.service_remote_visits: "Immovisita"
    app.login.meta_description: "Accedi a Getrix, il gestionale immobiliare N.1 in Italia con oltre 16.000 installazioni."
    app.needs_tos_update: true
    app.register.meta_description: "Registrati a Immobiliare Pro e pubblica i tuoi annunci sul portale immobiliare N.1 in Italia scelto da oltre 21.000 agenzie immobiliari e imprese edili."
    app.register.vat_field: true

    app.autocomplete.city_suggest_nation: 0
    app.autocomplete.city_suggest_type: "italian"

    app.facebook_url: "https://www.facebook.com/immobiliare.it/"
    app.twitter_url: "https://twitter.com/immobiliare_it"
    app.beamer_news: "%env(BEAMER_NEWS_ENABLED)%"
    app.beamer_product_id: "%env(BEAMER_PRODUCT_ID)%"

    app.onboarding_enabled: "%env(ONBOARDING_ENABLED)%"

    app.appcues_enabled: "%env(APPCUES_ENABLED)%"

    app.client_app_login_enabled: '%env(bool:CLIENT_APP_LOGIN_ENABLED)%'

    app.menu_counters_enabled: "%env(MENU_COUNTERS_ENABLED)%"
    app.menu_counters_interval: "%env(MENU_COUNTERS_POLLING_INTERVAL)%"
    app.menu_counters_searches_modification_date_days_interval: "%env(int:MENU_COUNTERS_SEARCHES_MODIFICATION_DATE_DAYS_INTERVAL)%"

    app.menu_counters_polling_interval: "%env(MENU_COUNTERS_POLLING_INTERVAL)%"
    app.menu_counters_polling_retries: '%env(MENU_COUNTERS_POLLING_RETRIES)%'

    app.property_list_performance: "%env(PROPERTY_LIST_PERFORMANCE)%"
    app.property_list_match_column: "%env(PROPERTY_LIST_MATCH_COLUMN)%"
    app.api_section_remote_visits: "%env(API_SECTION_REMOTE_VISITS)%"
    app.notification_settings_enabled: "%env(NOTIFICATION_SETTINGS_ENABLED)%"

    app.visibilities_all_extra_visibilities_product: "%env(VISIBILITIES_ALL_EXTRA_VISIBILITIES_PRODUCT)%"

    app.virtual_tour_360_service_name: "Virtual Tour 3D"

    app.a3f_status: 0
    app.check_source_ip: 1

    app.google.googleTagManagerId: "%env(GOOGLE_TAG_MANAGER_ID)%"

    app.youdomusSwitch: 1
    app.youdomusEndpoint: "https://youdomus.immobiliare.it"
    app.youdomusAuthCode: "729e7cac8d5559e055008c0e4ef728d6"
    app.youdomusGetrixApp: "getrix"
    app.youdomusProApp: "pro"
    app.youdomus.ObtainCadastralData: "%env(YOUDOMUS_OBTAIN_CADASTRAL_DATA_URL)%"

    app.mixpanel_token: "%env(MIXPANEL_TOKEN)%"
    app.mixpanel_debug: "%env(MIXPANEL_DEBUG)%"
    app.mixpanel_skip_backoffice: "%env(MIXPANEL_SKIP_BACKOFFICE)%"

    app.user_events_tracking_toggle: "%env(USER_EVENTS_TRACKING_TOGGLE)%"

    app.events_queue_ws_endpoint: "%env(EVENTS_QUEUE_WS_ENDPOINT)%"
    app.events_queue_ws_auth_username: immobiliare-pro
    app.events_queue_ws_auth_password: 56puTDnE2GZtLsJE

    proxy_redirect_base_url: "https://gp3iv5y2j8.execute-api.us-east-2.amazonaws.com/service-proxy"
    proxy_redirect_secret: "LoIY5gQ0YQEi3tQdhTF4"

    app.settings_general_privacy_instrument_appointment: "%env(bool:SETTINGS_GENERAL_PRIVACY_INSTRUMENT_APPOINTMENT)%"

    app.url_cdn_img_cartine: "%env(URL_CDN_IMG_CARTINE)%"
    app.img_cartine_size: "%env(IMG_CARTINE_SIZE)%"
    app.img_cartine_version: "%env(IMG_CARTINE_VERSION)%"

    app.local_timezone: "%env(LOCAL_TIMEZONE)%"
    app.datetime_format.short: !php/const App\Constants\Formatter\it\Datetime::DATETIME_FORMAT_SHORT
    app.datetime_format.medium: !php/const App\Constants\Formatter\it\Datetime::DATETIME_FORMAT_MEDIUM
    app.datetime_format.extended: !php/const App\Constants\Formatter\it\Datetime::DATETIME_FORMAT_EXTENDED
    app.datetime_format.only_time: !php/const App\Constants\Formatter\it\Datetime::DATETIME_FORMAT_ONLY_TIME
    app.datetime_format.default: !php/const App\Constants\Formatter\it\Datetime::DATETIME_FORMAT_DEFAULT

    # www_eurekasa.visibilita
    app.visibility.premium: 1
    app.visibility.top: 3
    app.visibility.star: 4
    app.visibility.showcase: 5

    app.visibility.premium_name: "Premium"
    app.visibility.top_name: "Top"
    app.visibility.star_name: "Star"
    app.visibility.showcase_name: "Vetrina"
    app.visibility.sky_name: "Sky"
    app.visibility.secret_property_name: "Immobile Segreto"

    app.salesforce_offline_form_enabled: "%env(SALESFORCE_OFFLINE_FORM_ENABLED)%"

    app.user_profile_url: "/profile/me"

    app.zone_package_tier_low: "bronze"
    app.zone_package_tier_medium: "silver"
    app.zone_package_tier_high: "gold"

    app.agency.evaluation_zone.only_getrix: true

    sapi.config:
        prod:
            services.getrix.getrix.annunci.immagini:
                baseUrl: "http://sapi.rete.farm:9977/"
                rootPath: "/storage/immobiliare/images/"
                auth: "gtx-prod"
                timeout: 20000
                connectTimeout: 3000
                verbose: false
                retry: 3
                persistent: false
            services.getrix.getrix.annunci.planimetrie:
                baseUrl: "http://sapi.rete.farm:9977/"
                rootPath: "/storage/immobiliare/plans/"
                auth: "gtx-prod"
                timeout: 20000
                connectTimeout: 3000
                verbose: false
                retry: 3
                persistent: false
            services.getrix.agenzie:
                baseUrl: "http://sapi.rete.farm:9977/"
                rootPath: "/storage/immobiliare/images"
                auth: "im-prod"
                timeout: 20000
                connectTimeout: 3000
                verbose: false
                retry: 3
                persistent: false
            services.getrix.agenti:
                baseUrl: "http://sapi.rete.farm:9977/"
                rootPath: "/storage/immobiliare/profiles/agents/images"
                auth: "im-prod"
                timeout: 20000
                connectTimeout: 3000
                verbose: false
                retry: 3
                persistent: false
            services.immobiliare.media:
                baseUrl: "http://sapi.rete.farm:9977/"
                rootPath: "/storage/immobiliare/documents"
                auth: "im-prod"
                timeout: 20000
                connectTimeout: 3000
                verbose: false
                retry: 3
                persistent: false

    # Telephone configurations
    app.phone.short_code: "it"
    app.phone.international_prefix: "+39"
    app.phone.mobile_regexp: '^(\+393){1}'

    # Cadastral data, for italian tenant only
    app.show_cadastral_data: true
